<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PSP运营后台 - EX账单管理</title>
    <style>
        /* 通用样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            text-align: center;
            font-size: 28px;
            font-weight: 600;
        }

        .header .subtitle {
            text-align: center;
            font-size: 16px;
            opacity: 0.9;
            margin-top: 8px;
        }

        /* 导航栏 */
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            background: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-right: 1px solid #eee;
        }

        .nav-tab:last-child {
            border-right: none;
        }

        .nav-tab.active {
            background: #667eea;
            color: white;
        }

        .nav-tab:hover:not(.active) {
            background: #f8f9fa;
            color: #333;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            text-align: center;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .stat-change {
            font-size: 12px;
            margin-top: 8px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .stat-change.positive {
            background: #d4edda;
            color: #155724;
        }

        .stat-change.negative {
            background: #f8d7da;
            color: #721c24;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            margin-bottom: 24px;
            overflow: hidden;
        }

        .card-header {
            padding: 20px 24px;
            border-bottom: 1px solid #eee;
            background: #fafbfc;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .card-body {
            padding: 24px;
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8f9fa;
            padding: 16px 20px;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #dee2e6;
            font-size: 14px;
        }

        .table td {
            padding: 16px 20px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        /* 状态标签 */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 搜索筛选 */
        .search-filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }

        .form-input {
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }

            .form-group {
                min-width: auto;
            }

            .nav-tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>PSP运营后台</h1>
            <div class="subtitle">EX账单管理系统 | PSP ID: PSP_001</div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">账单概览</button>
            <button class="nav-tab" onclick="showTab('bills')">账单明细</button>
            <button class="nav-tab" onclick="showTab('settlements')">结算记录</button>
            <button class="nav-tab" onclick="showTab('commission')">返点明细</button>
        </div>

        <!-- 账单概览 -->
        <div id="overview" class="tab-content active">
            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">$125,680</div>
                    <div class="stat-label">本月应付EX费用</div>
                    <div class="stat-change positive">+12.5%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$8,450</div>
                    <div class="stat-label">本月返点收入</div>
                    <div class="stat-change positive">+8.2%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$117,230</div>
                    <div class="stat-label">本月净支出</div>
                    <div class="stat-change negative">+15.3%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">2,456</div>
                    <div class="stat-label">本月交易笔数</div>
                    <div class="stat-change positive">+22.1%</div>
                </div>
            </div>

            <!-- 费用构成 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">费用构成分析</h3>
                    <button class="btn btn-primary btn-sm">导出报告</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>费用类型</th>
                                    <th>费率</th>
                                    <th>本月金额</th>
                                    <th>交易笔数</th>
                                    <th>占比</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Setup Fee</td>
                                    <td>$500/次</td>
                                    <td>$2,000</td>
                                    <td>4</td>
                                    <td>1.6%</td>
                                </tr>
                                <tr>
                                    <td>科技服务费</td>
                                    <td>$5,000/月</td>
                                    <td>$5,000</td>
                                    <td>1</td>
                                    <td>4.0%</td>
                                </tr>
                                <tr>
                                    <td>交易处理费</td>
                                    <td>0.3%</td>
                                    <td>$118,680</td>
                                    <td>2,456</td>
                                    <td>94.4%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 最近账单 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">最近账单</h3>
                    <button class="btn btn-primary btn-sm">查看全部</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>账单ID</th>
                                    <th>账单期间</th>
                                    <th>账单金额</th>
                                    <th>返点金额</th>
                                    <th>净支出</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>BILL_202412_001</td>
                                    <td>2024-12-01 ~ 2024-12-31</td>
                                    <td>$125,680</td>
                                    <td>$8,450</td>
                                    <td>$117,230</td>
                                    <td><span class="status-badge status-pending">待结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">查看详情</button></td>
                                </tr>
                                <tr>
                                    <td>BILL_202411_001</td>
                                    <td>2024-11-01 ~ 2024-11-30</td>
                                    <td>$112,340</td>
                                    <td>$7,560</td>
                                    <td>$104,780</td>
                                    <td><span class="status-badge status-success">已结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">查看详情</button></td>
                                </tr>
                                <tr>
                                    <td>BILL_202410_001</td>
                                    <td>2024-10-01 ~ 2024-10-31</td>
                                    <td>$98,750</td>
                                    <td>$6,890</td>
                                    <td>$91,860</td>
                                    <td><span class="status-badge status-success">已结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">查看详情</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 账单明细 -->
        <div id="bills" class="tab-content">
            <!-- 搜索筛选 -->
            <div class="search-filters">
                <div class="filter-row">
                    <div class="form-group">
                        <label class="form-label">账单期间</label>
                        <input type="month" class="form-input" value="2024-12">
                    </div>
                    <div class="form-group">
                        <label class="form-label">账单状态</label>
                        <select class="form-input">
                            <option value="">全部状态</option>
                            <option value="pending">待结算</option>
                            <option value="settled">已结算</option>
                            <option value="disputed">有争议</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">费用类型</label>
                        <select class="form-input">
                            <option value="">全部类型</option>
                            <option value="setup">Setup Fee</option>
                            <option value="service">科技服务费</option>
                            <option value="transaction">交易处理费</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary">搜索</button>
                    </div>
                </div>
            </div>

            <!-- 账单明细表格 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">账单明细列表</h3>
                    <button class="btn btn-primary btn-sm">导出Excel</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>账单ID</th>
                                    <th>费用类型</th>
                                    <th>计费基础</th>
                                    <th>费率</th>
                                    <th>费用金额</th>
                                    <th>账单期间</th>
                                    <th>生成时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>BILL_202412_001_001</td>
                                    <td>交易处理费</td>
                                    <td>$39,560,000</td>
                                    <td>0.30%</td>
                                    <td>$118,680</td>
                                    <td>2024-12-01 ~ 2024-12-31</td>
                                    <td>2024-12-31 18:00</td>
                                    <td><span class="status-badge status-pending">待结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">查看</button></td>
                                </tr>
                                <tr>
                                    <td>BILL_202412_001_002</td>
                                    <td>科技服务费</td>
                                    <td>月度固定</td>
                                    <td>$5,000</td>
                                    <td>$5,000</td>
                                    <td>2024-12-01 ~ 2024-12-31</td>
                                    <td>2024-12-01 00:00</td>
                                    <td><span class="status-badge status-success">已结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">查看</button></td>
                                </tr>
                                <tr>
                                    <td>BILL_202412_001_003</td>
                                    <td>Setup Fee</td>
                                    <td>新商户开户</td>
                                    <td>$500</td>
                                    <td>$2,000</td>
                                    <td>2024-12-01 ~ 2024-12-31</td>
                                    <td>2024-12-15 14:30</td>
                                    <td><span class="status-badge status-success">已结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">查看</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结算记录 -->
        <div id="settlements" class="tab-content">
            <!-- 搜索筛选 -->
            <div class="search-filters">
                <div class="filter-row">
                    <div class="form-group">
                        <label class="form-label">结算时间</label>
                        <input type="date" class="form-input" value="2024-12-01">
                    </div>
                    <div class="form-group">
                        <label class="form-label">至</label>
                        <input type="date" class="form-input" value="2024-12-31">
                    </div>
                    <div class="form-group">
                        <label class="form-label">结算状态</label>
                        <select class="form-input">
                            <option value="">全部状态</option>
                            <option value="pending">待结算</option>
                            <option value="processing">结算中</option>
                            <option value="completed">已完成</option>
                            <option value="failed">结算失败</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary">搜索</button>
                    </div>
                </div>
            </div>

            <!-- 结算记录表格 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">结算记录</h3>
                    <button class="btn btn-primary btn-sm">导出记录</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>结算ID</th>
                                    <th>结算类型</th>
                                    <th>结算金额</th>
                                    <th>结算方式</th>
                                    <th>结算时间</th>
                                    <th>关联账单</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>SETTLE_20241201_001</td>
                                    <td>科技服务费</td>
                                    <td>$5,000</td>
                                    <td>自动代扣</td>
                                    <td>2024-12-01 09:00</td>
                                    <td>BILL_202412_001_002</td>
                                    <td><span class="status-badge status-success">已完成</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>SETTLE_20241215_001</td>
                                    <td>Setup Fee</td>
                                    <td>$2,000</td>
                                    <td>自动代扣</td>
                                    <td>2024-12-15 15:00</td>
                                    <td>BILL_202412_001_003</td>
                                    <td><span class="status-badge status-success">已完成</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>SETTLE_20241231_001</td>
                                    <td>交易处理费</td>
                                    <td>$118,680</td>
                                    <td>自动代扣</td>
                                    <td>2024-12-31 18:30</td>
                                    <td>BILL_202412_001_001</td>
                                    <td><span class="status-badge status-pending">待结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>SETTLE_20241205_001</td>
                                    <td>返点结算</td>
                                    <td>$2,150</td>
                                    <td>银行转账</td>
                                    <td>2024-12-05 14:00</td>
                                    <td>COMM_202411_001</td>
                                    <td><span class="status-badge status-success">已完成</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 返点明细 -->
        <div id="commission" class="tab-content">
            <!-- 返点统计 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">$8,450</div>
                    <div class="stat-label">本月返点收入</div>
                    <div class="stat-change positive">+8.2%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$7,560</div>
                    <div class="stat-label">上月返点收入</div>
                    <div class="stat-change positive">+5.1%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">15.2%</div>
                    <div class="stat-label">平均返点率</div>
                    <div class="stat-change positive">+0.3%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$89,670</div>
                    <div class="stat-label">累计返点收入</div>
                    <div class="stat-change positive">+12.8%</div>
                </div>
            </div>

            <!-- 搜索筛选 -->
            <div class="search-filters">
                <div class="filter-row">
                    <div class="form-group">
                        <label class="form-label">返点期间</label>
                        <input type="month" class="form-input" value="2024-12">
                    </div>
                    <div class="form-group">
                        <label class="form-label">返点类型</label>
                        <select class="form-input">
                            <option value="">全部类型</option>
                            <option value="transaction">交易返点</option>
                            <option value="volume">业绩返点</option>
                            <option value="special">特殊返点</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">结算状态</label>
                        <select class="form-input">
                            <option value="">全部状态</option>
                            <option value="pending">待结算</option>
                            <option value="settled">已结算</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary">搜索</button>
                    </div>
                </div>
            </div>

            <!-- 返点明细表格 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">返点明细</h3>
                    <button class="btn btn-primary btn-sm">导出明细</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>返点ID</th>
                                    <th>返点类型</th>
                                    <th>计算基础</th>
                                    <th>返点率</th>
                                    <th>返点金额</th>
                                    <th>返点期间</th>
                                    <th>结算时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>COMM_202412_001</td>
                                    <td>交易返点</td>
                                    <td>$118,680</td>
                                    <td>7.0%</td>
                                    <td>$8,308</td>
                                    <td>2024-12-01 ~ 2024-12-31</td>
                                    <td>-</td>
                                    <td><span class="status-badge status-pending">待结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>COMM_202412_002</td>
                                    <td>业绩返点</td>
                                    <td>月度业绩</td>
                                    <td>-</td>
                                    <td>$142</td>
                                    <td>2024-12-01 ~ 2024-12-31</td>
                                    <td>-</td>
                                    <td><span class="status-badge status-pending">待结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>COMM_202411_001</td>
                                    <td>交易返点</td>
                                    <td>$108,000</td>
                                    <td>7.0%</td>
                                    <td>$7,560</td>
                                    <td>2024-11-01 ~ 2024-11-30</td>
                                    <td>2024-12-05 14:00</td>
                                    <td><span class="status-badge status-success">已结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换功能
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的active状态
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页内容
            document.getElementById(tabName).classList.add('active');

            // 设置选中的标签为active状态
            event.target.classList.add('active');
        }

        // 模拟数据加载
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PSP运营后台加载完成');
        });
    </script>
</body>
</html>
