<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理政策管理系统 - 交互式流程图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .tabs {
            display: flex;
            background: white;
            border-radius: 15px 15px 0 0;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab:hover {
            background: #e9ecef;
        }

        .tab.active {
            background: white;
            border-bottom-color: #667eea;
            color: #667eea;
        }

        .content {
            background: white;
            border-radius: 0 0 15px 15px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            min-height: 600px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .diagram-container {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .diagram-title {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 15px;
            color: #667eea;
        }

        .diagram-description {
            margin-bottom: 20px;
            color: #666;
            line-height: 1.6;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card h3 {
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .feature-card ul {
            list-style: none;
        }

        .feature-card li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .feature-card li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }

        .version-timeline {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .timeline-badge {
            width: 40px;
            height: 40px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        .timeline-content h4 {
            margin-bottom: 5px;
            color: #333;
        }

        .timeline-content p {
            color: #666;
            font-size: 0.9em;
        }

        .data-structure {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .table-container {
            overflow-x: auto;
            margin-top: 15px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 15px 0;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>代理政策管理系统</h1>
            <p>周期性政策发布 | 分层架构设计 | 版本管理</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('architecture')">系统架构</button>
            <button class="tab" onclick="showTab('workflow')">业务流程</button>
            <button class="tab" onclick="showTab('structure')">层级结构</button>
            <button class="tab" onclick="showTab('version')">版本管理</button>
            <button class="tab" onclick="showTab('database')">数据结构</button>
        </div>

        <div class="content">
            <!-- 系统架构 -->
            <div id="architecture" class="tab-content active">
                <div class="diagram-container">
                    <div class="diagram-title">代理政策管理系统架构</div>
                    <div class="diagram-description">
                        系统采用三层架构设计：原子配置层负责基础数据配置，政策版本层负责配置组合和版本管理，发布管理层负责政策生效和生命周期管理。
                    </div>
                    <div class="mermaid">
                        graph TB
                            A[代理政策管理系统] --> B[原子配置层]
                            A --> C[政策版本层]
                            A --> D[发布管理层]
                            
                            B --> B1[产品底价配置]
                            B --> B2[汇率配置]
                            
                            B1 --> B11[标准底价]
                            B1 --> B12[特殊底价]
                            
                            B2 --> B21[底价汇率]
                            B2 --> B22[固定返点]
                            
                            C --> C1[政策版本创建]
                            C --> C2[版本预览]
                            C --> C3[版本对比]
                            
                            D --> D1[政策发布]
                            D --> D2[生效管理]
                            D --> D3[历史版本]
                            
                            style A fill:#667eea,stroke:#333,stroke-width:3px,color:#fff
                            style B fill:#4CAF50,stroke:#333,stroke-width:2px,color:#fff
                            style C fill:#FF9800,stroke:#333,stroke-width:2px,color:#fff
                            style D fill:#9C27B0,stroke:#333,stroke-width:2px,color:#fff
                    </div>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>原子配置层</h3>
                        <ul>
                            <li>产品底价配置管理</li>
                            <li>汇率政策配置</li>
                            <li>标准与特殊配置分离</li>
                            <li>配置项验证和校验</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>政策版本层</h3>
                        <ul>
                            <li>配置组合和版本创建</li>
                            <li>版本预览和对比</li>
                            <li>版本继承和更新</li>
                            <li>配置完整性验证</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>发布管理层</h3>
                        <ul>
                            <li>政策发布和生效控制</li>
                            <li>代理商通知机制</li>
                            <li>历史版本管理</li>
                            <li>政策回滚和更新</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 业务流程 -->
            <div id="workflow" class="tab-content">
                <div class="diagram-container">
                    <div class="diagram-title">政策创建和发布流程</div>
                    <div class="diagram-description">
                        完整的政策创建流程包括原子配置、版本创建、审核发布三个主要阶段，确保政策的准确性和有效性。
                    </div>
                    <div class="mermaid">
                        sequenceDiagram
                            participant Admin as 管理员
                            participant System as 政策系统
                            participant Config as 配置模块
                            participant Version as 版本管理
                            participant Agent as 代理商
                            
                            Admin->>System: 启动新政策创建
                            System->>Config: 配置产品底价
                            Config->>Config: 标准底价设置
                            Config->>Config: 特殊底价设置
                            Config->>System: 底价配置完成
                            
                            System->>Config: 配置汇率政策
                            Config->>Config: 底价汇率设置
                            Config->>Config: 固定返点设置
                            Config->>System: 汇率配置完成
                            
                            System->>Version: 创建政策版本
                            Version->>Version: 版本预览生成
                            Admin->>Version: 审核政策版本
                            Version->>System: 版本审核通过
                            
                            System->>Agent: 发布政策通知
                            Agent->>System: 确认政策接收
                            System->>Version: 政策正式生效
                    </div>
                </div>

                <div class="version-timeline">
                    <h3>政策发布时间线</h3>
                    <div class="timeline-item">
                        <div class="timeline-badge">1</div>
                        <div class="timeline-content">
                            <h4>原子配置阶段</h4>
                            <p>配置产品底价和汇率政策，包括标准配置和特殊配置</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-badge">2</div>
                        <div class="timeline-content">
                            <h4>版本创建阶段</h4>
                            <p>将原子配置组合成政策版本，生成预览和对比报告</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-badge">3</div>
                        <div class="timeline-content">
                            <h4>审核发布阶段</h4>
                            <p>政策审核、代理商通知、正式生效和监控管理</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 层级结构 -->
            <div id="structure" class="tab-content">
                <div class="diagram-container">
                    <div class="diagram-title">政策层级结构图</div>
                    <div class="diagram-description">
                        政策系统采用三层结构：第一层为原子配置，第二层为政策版本，第三层为发布管理，层层递进，确保政策的完整性和可管理性。
                    </div>
                    <div class="mermaid">
                        graph TD
                            A[政策层级结构] --> B[第一层：原子配置]
                            A --> C[第二层：政策版本]
                            A --> D[第三层：发布管理]
                            
                            B --> B1[产品底价配置]
                            B --> B2[汇率配置]
                            
                            B1 --> B11[标准底价<br/>适用于常规业务场景]
                            B1 --> B12[特殊底价<br/>针对特定代理商]
                            
                            B2 --> B21[底价汇率<br/>基础汇率加点配置]
                            B2 --> B22[固定返点<br/>固定比例返点配置]
                            
                            C --> C1[政策版本创建<br/>组合原子配置]
                            C --> C2[版本管理<br/>版本号和历史追溯]
                            C --> C3[政策预览<br/>预览和对比功能]
                            
                            D --> D1[生效时间控制<br/>政策生效管理]
                            D --> D2[通知机制<br/>代理商通知]
                            D --> D3[回滚更新<br/>政策回滚和更新]
                            
                            style B fill:#4CAF50,stroke:#333,stroke-width:2px,color:#fff
                            style C fill:#FF9800,stroke:#333,stroke-width:2px,color:#fff
                            style D fill:#9C27B0,stroke:#333,stroke-width:2px,color:#fff
                    </div>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>原子配置特点</h3>
                        <ul>
                            <li>最小配置单元</li>
                            <li>独立配置和验证</li>
                            <li>支持标准和特殊配置</li>
                            <li>可重复使用和组合</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>政策版本特点</h3>
                        <ul>
                            <li>原子配置的有机组合</li>
                            <li>完整的业务政策包</li>
                            <li>版本控制和历史管理</li>
                            <li>支持继承和增量更新</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>发布管理特点</h3>
                        <ul>
                            <li>政策生命周期管理</li>
                            <li>自动化发布流程</li>
                            <li>实时监控和反馈</li>
                            <li>灵活的回滚机制</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 版本管理 -->
            <div id="version" class="tab-content">
                <div class="diagram-container">
                    <div class="diagram-title">政策版本管理</div>
                    <div class="diagram-description">
                        支持年度、半年度和临时政策的版本管理，采用标准化命名规则，确保政策版本的清晰识别和有效管理。
                    </div>
                    <div class="mermaid">
                        gantt
                            title 政策发布周期管理
                            dateFormat  YYYY-MM-DD
                            section 年度政策
                            2024年度政策V1    :done, annual1, 2024-01-01, 2024-12-31
                            2025年度政策V1    :active, annual2, 2025-01-01, 2025-12-31
                            section 半年度政策
                            2024上半年V1      :done, semi1, 2024-01-01, 2024-06-30
                            2024下半年V1      :done, semi2, 2024-07-01, 2024-12-31
                            2025上半年V1      :active, semi3, 2025-01-01, 2025-06-30
                            section 临时政策
                            春节特别政策      :done, temp1, 2024-02-01, 2024-02-29
                            中秋特别政策      :done, temp2, 2024-09-01, 2024-09-30
                    </div>
                </div>

                <div class="data-structure">
                    <h3>版本命名规则</h3>
                    <div class="code-block">
格式：YYYY.[H/A].V[n]

示例：
- 2024.A.V1  (2024年年度政策第1版)
- 2024.H1.V1 (2024年上半年政策第1版)  
- 2024.H2.V2 (2024年下半年政策第2版)
- 2024.T.V1  (2024年临时政策第1版)

说明：
- YYYY: 年份
- A: Annual (年度), H1/H2: 上/下半年, T: Temporary (临时)
- V[n]: 版本号，从1开始递增
                    </div>
                </div>

                <div class="version-timeline">
                    <h3>版本生命周期</h3>
                    <div class="timeline-item">
                        <div class="timeline-badge">草稿</div>
                        <div class="timeline-content">
                            <h4>DRAFT - 草稿状态</h4>
                            <p>政策正在创建和配置中，尚未完成</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-badge">待审</div>
                        <div class="timeline-content">
                            <h4>PENDING - 待审核状态</h4>
                            <p>政策配置完成，等待审核和批准</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-badge">生效</div>
                        <div class="timeline-content">
                            <h4>ACTIVE - 生效状态</h4>
                            <p>政策已发布并正在生效中</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-badge">过期</div>
                        <div class="timeline-content">
                            <h4>EXPIRED - 过期状态</h4>
                            <p>政策已过期，仅保留历史记录</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据结构 -->
            <div id="database" class="tab-content">
                <div class="data-structure">
                    <h3>政策版本表 (policy_versions)</h3>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>字段名</th>
                                    <th>类型</th>
                                    <th>说明</th>
                                    <th>示例</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>BIGINT</td>
                                    <td>主键ID</td>
                                    <td>1001</td>
                                </tr>
                                <tr>
                                    <td>version_code</td>
                                    <td>VARCHAR(20)</td>
                                    <td>版本编码</td>
                                    <td>2024.A.V1</td>
                                </tr>
                                <tr>
                                    <td>version_name</td>
                                    <td>VARCHAR(100)</td>
                                    <td>版本名称</td>
                                    <td>2024年度代理政策</td>
                                </tr>
                                <tr>
                                    <td>policy_type</td>
                                    <td>ENUM</td>
                                    <td>政策类型</td>
                                    <td>ANNUAL/SEMI_ANNUAL/TEMPORARY</td>
                                </tr>
                                <tr>
                                    <td>effective_date</td>
                                    <td>DATE</td>
                                    <td>生效日期</td>
                                    <td>2024-01-01</td>
                                </tr>
                                <tr>
                                    <td>status</td>
                                    <td>ENUM</td>
                                    <td>状态</td>
                                    <td>DRAFT/PENDING/ACTIVE/EXPIRED</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="data-structure">
                    <h3>产品底价配置表 (product_base_prices)</h3>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>字段名</th>
                                    <th>类型</th>
                                    <th>说明</th>
                                    <th>示例</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>BIGINT</td>
                                    <td>主键ID</td>
                                    <td>2001</td>
                                </tr>
                                <tr>
                                    <td>policy_version_id</td>
                                    <td>BIGINT</td>
                                    <td>政策版本ID</td>
                                    <td>1001</td>
                                </tr>
                                <tr>
                                    <td>product_type</td>
                                    <td>VARCHAR(50)</td>
                                    <td>产品类型</td>
                                    <td>REMITTANCE</td>
                                </tr>
                                <tr>
                                    <td>price_type</td>
                                    <td>ENUM</td>
                                    <td>价格类型</td>
                                    <td>STANDARD/SPECIAL</td>
                                </tr>
                                <tr>
                                    <td>base_price</td>
                                    <td>DECIMAL(10,4)</td>
                                    <td>底价</td>
                                    <td>15.0000</td>
                                </tr>
                                <tr>
                                    <td>agent_id</td>
                                    <td>BIGINT</td>
                                    <td>代理商ID(特殊价格)</td>
                                    <td>3001</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="data-structure">
                    <h3>汇率配置表 (exchange_rate_configs)</h3>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>字段名</th>
                                    <th>类型</th>
                                    <th>说明</th>
                                    <th>示例</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>BIGINT</td>
                                    <td>主键ID</td>
                                    <td>3001</td>
                                </tr>
                                <tr>
                                    <td>policy_version_id</td>
                                    <td>BIGINT</td>
                                    <td>政策版本ID</td>
                                    <td>1001</td>
                                </tr>
                                <tr>
                                    <td>currency_pair</td>
                                    <td>VARCHAR(10)</td>
                                    <td>货币对</td>
                                    <td>USD/CNY</td>
                                </tr>
                                <tr>
                                    <td>rate_type</td>
                                    <td>ENUM</td>
                                    <td>汇率类型</td>
                                    <td>BASE_RATE/FIXED_REBATE</td>
                                </tr>
                                <tr>
                                    <td>rate_value</td>
                                    <td>DECIMAL(8,6)</td>
                                    <td>汇率值</td>
                                    <td>0.005000</td>
                                </tr>
                                <tr>
                                    <td>agent_id</td>
                                    <td>BIGINT</td>
                                    <td>代理商ID(特殊汇率)</td>
                                    <td>3001</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            sequence: {
                useMaxWidth: true,
                showSequenceNumbers: true
            },
            gantt: {
                useMaxWidth: true,
                leftPadding: 100
            }
        });

        // Tab 切换功能
        function showTab(tabName) {
            // 隐藏所有内容
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有 tab 的 active 状态
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活选中的 tab
            event.target.classList.add('active');

            // 重新渲染 Mermaid 图表
            setTimeout(() => {
                mermaid.init();
            }, 100);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化 Mermaid
            mermaid.init();
        });
    </script>
</body>
</html>
