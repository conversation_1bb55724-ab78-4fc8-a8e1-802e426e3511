# 渠道能力输出模型文档

## 1. 产品概述

### 1.1 产品定义

渠道能力输出模型是一个多层级的金融科技服务平台，通过EX聚合收款科技公司和IPL支付公司的合作，为PSP机构客户及其终端商户提供完整的收付款解决方案。

### 1.2 核心价值

- **科技赋能**: EX为PSP提供先进的金融科技能力
- **渠道整合**: IPL为PSP提供VA收款渠道能力
- **分层代理**: 建立EX→PSP→终端商户的多层级服务体系
- **灵活计费**: 支持多种收费模式和返点机制
- **账单结算**: EX作为科技公司，与PSP通过账单方式结算，线下银行转账

## 2. 业务对象定义

### 2.1 核心对象

| 对象               | 全称             | 角色定义       | 主要职责                      |
| ------------------ | ---------------- | -------------- | ----------------------------- |
| **EX**       | 聚合收款科技公司 | 科技服务提供商 | 提供金融科技能力，代理IPL产品 |
| **IPL**      | 支付公司         | 支付渠道提供商 | 提供VA收款渠道，处理资金清算  |
| **PSP**      | 机构客户         | 支付服务提供商 | EX的客户，为终端商户提供服务  |
| **终端商户** | 最终用户         | 收付款需求方   | PSP的客户，实际使用收付款服务 |

### 2.2 对象关系

- **EX ↔ PSP**: 科技服务提供关系, EX为PSP提供技术能力
- **IPL ↔ PSP**: 渠道服务关系, IPL为PSP提供支付渠道
- **EX ↔ IPL**: 代理合作关系, EX代理IPL产品并二级分销
- **PSP ↔ 终端商户**: 服务提供关系, PSP为商户提供收付款能力

## 3. 业务关系图

### 3.1 整体业务关系图

```mermaid
Ssgraph TB
    subgraph "科技服务层"
        EX[EX科技公司]
    end

    subgraph "支付渠道层"
        IPL[IPL支付公司]
    end

    subgraph "机构客户层"
        PSP1[PSP机构客户1]
        PSP2[PSP机构客户2]
        PSPn[PSP机构客户n...]
    end

    subgraph "终端商户层"
        M1[终端商户1]
        M2[终端商户2]
        M3[终端商户3]
        Mn[终端商户n...]
    end

    %% 业务关系
    EX -->|科技服务| PSP1
    EX -->|科技服务| PSP2
    EX -->|科技服务| PSPn

    IPL -->|VA渠道服务| PSP1
    IPL -->|VA渠道服务| PSP2
    IPL -->|VA渠道服务| PSPn

    EX -.->|代理合作| IPL

    PSP1 -->|收付款服务| M1
    PSP1 -->|收付款服务| M2
    PSP2 -->|收付款服务| M3
    PSPn -->|收付款服务| Mn

    %% 费用流向
    PSP1 -.->|科技服务费| EX
    PSP2 -.->|科技服务费| EX
    PSPn -.->|科技服务费| EX

    PSP1 -.->|交易手续费| IPL
    PSP2 -.->|交易手续费| IPL
    PSPn -.->|交易手续费| IPL

    IPL -.->|代理返点| EX
    EX -.->|分佣返点| PSP1
    EX -.->|分佣返点| PSP2
    EX -.->|分佣返点| PSPn
```

### 3.2 收费模式关系图

```mermaid
graph LR
    PSP[PSP机构客户]

    subgraph EX收费模式
        A1[Setup Fee<br/>一次性收取]
        A2[科技服务费<br/>月/季/年]
        A3[交易处理费<br/>随交易收取]
    end

    subgraph IPL收费模式
        B1[交易手续费<br/>收款/付款/结汇]
    end

    subgraph 返点机制
        C1[IPL-EX<br/>代理返点]
        C2[EX-PSP<br/>分佣返点]
    end

    PSP -->|支付| A1
    PSP -->|支付| A2
    PSP -->|支付| A3
    PSP -->|支付| B1

    B1 -.->|部分返还| C1
    A3 -.->|部分返还| C2
```

### 3.3 代理层级关系图

```mermaid
graph TD
    IPL[IPL支付公司<br/>原始服务提供商]

    EX[EX聚合收款科技公司<br/>一级代理商]

    PSP1[PSP机构客户1<br/>二级代理商]
    PSP2[PSP机构客户2<br/>二级代理商]

    M1[终端商户1<br/>最终用户]
    M2[终端商户2<br/>最终用户]
    M3[终端商户3<br/>最终用户]

    IPL -->|代理底价| EX
    EX -->|代理底价| PSP1
    EX -->|代理底价| PSP2

    PSP1 -->|服务| M1
    PSP1 -->|服务| M2
    PSP2 -->|服务| M3

    %% 标注代理层级
    IPL -.->|L0: 原始层| IPL
    EX -.->|L1: 一级代理| EX
    PSP1 -.->|L2: 二级代理| PSP1
    PSP2 -.->|L2: 二级代理| PSP2
    M1 -.->|L3: 终端用户| M1
```

## 4. 账户结构设计

### 4.1 账户开设关系图

```mermaid
graph TB
    subgraph "IPL支付公司账户体系"
        IPL_SYS[IPL账户系统]

        subgraph "EX代理账户"
            EX_AGENT[EX代理主账户]
        end

        subgraph "PSP主账户群"
            PSP1_MAIN[PSP1主账户]
            PSP2_MAIN[PSP2主账户]
            PSPn_MAIN[PSPn主账户]
        end

        subgraph "终端商户子账户群"
            M1_SUB[商户1子账户]
            M2_SUB[商户2子账户]
            M3_SUB[商户3子账户]
            Mn_SUB[商户n子账户]
        end
    end

    subgraph "EX科技公司系统"
        EX_SYS[EX计费系统]
        EX_BILLING[账单管理系统]

        subgraph "账单记录"
            PSP1_BILL[PSP1账单记录]
            PSP2_BILL[PSP2账单记录]
            PSPn_BILL[PSPn账单记录]
        end
    end

    subgraph "银行转账结算"
        BANK_TRANSFER[线下银行转账]
    end

    %% 账户开设关系
    EX -->|开设代理账户| EX_AGENT
    PSP1 -->|开设主账户| PSP1_MAIN
    PSP2 -->|开设主账户| PSP2_MAIN
    PSPn -->|开设主账户| PSPn_MAIN

    PSP1_MAIN -->|开设子账户| M1_SUB
    PSP1_MAIN -->|开设子账户| M2_SUB
    PSP2_MAIN -->|开设子账户| M3_SUB
    PSPn_MAIN -->|开设子账户| Mn_SUB

    %% 资金流向
    M1_SUB -.->|归集| PSP1_MAIN
    M2_SUB -.->|归集| PSP1_MAIN
    M3_SUB -.->|归集| PSP2_MAIN
    Mn_SUB -.->|归集| PSPn_MAIN

    %% 账单结算关系
    EX_SYS -->|生成账单| PSP1_BILL
    EX_SYS -->|生成账单| PSP2_BILL
    EX_SYS -->|生成账单| PSPn_BILL

    PSP1_BILL -.->|线下转账| BANK_TRANSFER
    PSP2_BILL -.->|线下转账| BANK_TRANSFER
    PSPn_BILL -.->|线下转账| BANK_TRANSFER
```

### 4.2 账户层级结构

```mermaid
graph TB
    subgraph IPL ["IPL账户层级"]
        L1[EX代理账户<br/>Level 1]
        L2[PSP主账户<br/>Level 2]
        L3[商户子账户<br/>Level 3]
        L1 --> L2
        L2 --> L3
    end

    subgraph EX ["EX计费系统"]
        E1[计费引擎<br/>费用计算]
        E2[账单系统<br/>账单生成]
        E3[结算管理<br/>结算跟踪]
        E1 --> E2
        E2 --> E3
    end

    subgraph SETTLEMENT ["结算方式"]
        S1[账单结算<br/>月度/季度]
        S2[线下转账<br/>银行转账]
        S3[对账确认<br/>双方确认]
        S1 --> S2
        S2 --> S3
    end

    subgraph FUNC ["功能模块"]
        F1[资金归集]
        F2[费用计算]
        F3[账单生成]
        F4[余额查询]
    end

    L2 -.-> F1
    L1 -.-> F2
    E2 -.-> F3
    L3 -.-> F4
```

### 4.3 结算权限矩阵

| 系统/账户类型        | 管理主体 | 操作权限    | 查询权限     | 结算方式 |
| -------------------- | -------- | ----------- | ------------ | -------- |
| **EX计费系统** | EX       | 生成账单    | 查询计费明细 | 账单结算 |
| **EX代理账户** | EX       | 接收IPL返点 | 查询代理收益 | 账户转账 |
| **PSP主账户**  | PSP      | 归集、提现  | 查询账户余额 | 账户转账 |
| **商户子账户** | 终端商户 | 收款入账    | 查询交易明细 | 账户转账 |
| **EX-PSP结算** | 双方     | 账单确认    | 查询结算记录 | 线下转账 |

## 5. 交易流程设计

### 5.1 VA申请流程时序图

```mermaid
sequenceDiagram
    participant M as 终端商户
    participant PSP as PSP机构
    participant IPL as IPL支付公司
    participant EX as EX科技公司

    Note over M,EX: VA申请流程

    M->>PSP: 1. 申请VA账户
    Note over M,PSP: 商户提交VA申请请求

    PSP->>IPL: 2. 转发VA申请指令
    Note over PSP,IPL: PSP验证商户信息后转发

    IPL->>IPL: 3. 生成VA账户
    Note over IPL: 创建虚拟账户号码

    IPL->>PSP: 4. 返回VA信息
    Note over IPL,PSP: 返回VA账户号和相关信息

    PSP->>M: 5. 反馈VA信息给商户
    Note over PSP,M: 商户获得可用的VA账户

    PSP-->>EX: 6. 同步VA信息
    Note over PSP,EX: 用于EX系统记录和计费
```

### 5.2 收款交易流程时序图

```mermaid
sequenceDiagram
    participant PAYER as 付款方
    participant IPL as IPL支付公司
    participant RISK as 风控合规系统
    participant ROUTER as 交易分发系统
    participant EX as EX科技公司
    participant EX_BILLING as EX计费系统
    participant IPL_BILLING as IPL计费系统
    participant PSP as PSP机构
    participant M as 终端商户
    participant SUB_ACC as 商户子账户
    participant MAIN_ACC as PSP主账户

    Note over PAYER,MAIN_ACC: 收款交易完整流程

    %% 第一步：资金入账
    PAYER->>IPL: 1. 向VA账户转账
    Note over PAYER,IPL: 付款方向商户VA转账

    %% 第二步：风控合规审核
    IPL->>RISK: 2. 提交风控合规审核
    Note over RISK: 审核交易合规性

    alt 审核通过
        RISK->>IPL: 3-1. 审核通过

        %% 第三步：交易分发
        IPL->>ROUTER: 4. 交易分发判断
        Note over ROUTER: 判断是IPL直客还是EX的PSP商户

        alt EX的PSP商户交易
            ROUTER->>EX: 5-2. 走EX流程

            %% 第四步：EX计费处理
            EX->>EX_BILLING: 6-2. 调用EX计费系统
            Note over EX_BILLING: 计算EX交易费用和分佣

            EX_BILLING->>EX: 7-2. 返回EX计费结果
            Note over EX: 获得EX费用计算结果

            EX->>IPL: 8-2. 通知IPL记账指令
            Note over EX,IPL: 告知IPL扣除交易手续费

            IPL->>SUB_ACC: 9-2. 商户子账户入账
            Note over SUB_ACC: 入账金额 = 交易金额 - EX手续费

        else IPL直客交易
            ROUTER->>IPL: 5-1. 走IPL直客流程

            %% IPL直客也需要走计费SaaS服务
            IPL->>IPL_BILLING: 6-1. 调用IPL计费系统
            Note over IPL_BILLING: 计算IPL交易费用

            IPL_BILLING->>IPL: 7-1. 返回IPL计费结果
            Note over IPL: 获得IPL费用计算结果

            IPL->>SUB_ACC: 9-1. 商户子账户入账
            Note over SUB_ACC: 入账金额 = 交易金额 - IPL手续费
        end

        %% 第五步：归集操作
        PSP->>IPL: 10. 发起归集指令
        IPL->>SUB_ACC: 11. 从子账户扣款
        IPL->>MAIN_ACC: 12. 转入PSP主账户
        Note over MAIN_ACC: 资金归集到PSP主账户

    else 审核拒绝
        RISK->>IPL: 3-2. 审核拒绝
        IPL->>PAYER: 资金退回
        Note over PAYER: 交易被拒绝，资金原路退回

    else 需要补充资料
        RISK->>IPL: 3-3. 需要补充资料
        IPL->>M: 通知商户补充资料
        Note over M: 商户需要提供额外资料
    end
```

### 5.3 结算流程时序图

```mermaid
sequenceDiagram
    participant TIMER as 定时任务
    participant EX as EX科技公司
    participant BILLING as EX账单系统
    participant PSP as PSP机构
    participant IPL as IPL支付公司
    participant PSP_MAIN as PSP主账户
    participant EX_AGENT as EX代理账户
    participant BANK as 银行系统

    Note over TIMER,BANK: EX-PSP轧差结算流程

    %% 第一步：数据统计和对账单生成
    TIMER->>EX: 1. 触发结算周期
    Note over EX: 按照结算周期执行(月度/季度)

    EX->>BILLING: 2. 数据统计
    Note over BILLING: 分别统计交易服务费和返点佣金

    BILLING->>BILLING: 3-1. 统计PSP应付交易服务费
    Note over BILLING: 汇总期间内所有交易服务费

    BILLING->>BILLING: 3-2. 统计EX应付PSP返点
    Note over BILLING: 汇总期间内所有返点佣金

    BILLING->>BILLING: 4. 生成统一对账单
    Note over BILLING: 交易服务费 - 返点佣金 = 轧差金额

    EX->>EX: 5. 对账单内部核对
    Note over EX: EX确认对账单数据

    %% 第二步：对账单确认
    PSP->>PSP: 6. 对账单核对
    Note over PSP: PSP核对交易数据和返点金额

    PSP->>EX: 8. 对账单确认回复
    Note over EX: 双方确认对账单无误

    %% 第三步：轧差结算
    alt PSP需要支付EX(轧差为正)
        Note over EX,PSP: PSP应付金额 > EX应付返点

        alt PSP选择IPL主账户支付
            PSP->>IPL: 9-1a. 申请从主账户扣款
            IPL->>PSP_MAIN: 9-2a. 从PSP主账户扣款
            IPL->>EX_AGENT: 9-3a. 转入EX代理账户
            Note over EX_AGENT: 通过IPL账户体系完成支付
        else PSP选择线下银行转账
            PSP->>BANK: 9-1b. 发起银行转账
            Note over BANK: PSP通过银行转账给EX
            BANK->>EX: 9-2b. 转账到账通知
            Note over EX: EX收到银行转账
        end

        EX->>PSP: 10. 确认收款
        Note over PSP: 结算完成确认

    else EX需要支付PSP(轧差为负)
        Note over EX,PSP: EX应付返点 > PSP应付金额

        EX->>BANK: 9-1c. 发起线下银行转账
        Note over BANK: EX通过银行转账给PSP

        BANK->>PSP: 9-2c. 转账到账通知
        Note over PSP: PSP收到银行转账

        PSP->>EX: 10. 确认收款
        Note over EX: 结算完成确认

    else 轧差为零
        Note over EX,PSP: 交易服务费 = 返点佣金，无需资金流转
        EX->>PSP: 9. 轧差结算确认
        Note over PSP: 双方确认无需资金流转
    end

    %% 第四步：IPL给EX分佣返点(独立流程)
    IPL->>IPL: 11. 计算EX分佣返点
    Note over IPL: 根据代理业绩计算返点

    IPL->>EX_AGENT: 12. 转入EX代理账户
    Note over EX_AGENT: EX收到IPL分佣返点

    IPL->>EX: 13. 发送分佣账单
    Note over EX: EX收到IPL返点结算通知
```

## 6. API接口设计

### 6.1 接口架构

```mermaid
graph TB
    subgraph "API网关层"
        GATEWAY[API Gateway]
    end

    subgraph "EX科技公司API"
        EX_API[EX API服务]
        EX_BILLING[计费API]
        EX_SETTLEMENT[结算API]
    end

    subgraph "IPL支付公司API"
        IPL_API[IPL API服务]
        IPL_VA[VA管理API]
        IPL_PAYMENT[支付处理API]
        IPL_ACCOUNT[账户管理API]
    end

    subgraph "PSP机构API"
        PSP_API[PSP API服务]
        PSP_MERCHANT[商户管理API]
    end

    GATEWAY --> EX_API
    GATEWAY --> IPL_API
    GATEWAY --> PSP_API

    EX_API --> EX_BILLING
    EX_API --> EX_SETTLEMENT

    IPL_API --> IPL_VA
    IPL_API --> IPL_PAYMENT
    IPL_API --> IPL_ACCOUNT

    PSP_API --> PSP_MERCHANT
```

### 6.2 核心API接口

#### 6.2.1 VA管理接口

**申请VA账户**

```http
POST /api/v1/va/create
Content-Type: application/json
Authorization: Bearer {token}

{
  "merchant_id": "MERCHANT_001",
  "psp_id": "PSP_001",
  "currency": "USD",
  "purpose": "收款",
  "validity_period": "365"
}
```

**响应**

```json
{
  "code": "200",
  "message": "success",
  "data": {
    "va_account": "VA_1234567890",
    "bank_code": "BANK_001",
    "account_name": "MERCHANT_001_VA",
    "created_at": "2024-12-20T10:30:00Z",
    "expires_at": "2025-12-20T10:30:00Z"
  }
}
```

**查询VA信息**

```http
GET /api/v1/va/{va_account}
Authorization: Bearer {token}
```

#### 6.2.2 交易处理接口

**交易通知接口**

```http
POST /api/v1/transaction/notify
Content-Type: application/json
Authorization: Bearer {token}

{
  "transaction_id": "TXN_20241220_001",
  "va_account": "VA_1234567890",
  "amount": 10000.00,
  "currency": "USD",
  "payer_info": {
    "name": "John Doe",
    "account": "ACC_123456"
  },
  "timestamp": "2024-12-20T10:30:00Z"
}
```

**风控审核接口**

```http
POST /api/v1/risk/review
Content-Type: application/json
Authorization: Bearer {token}

{
  "transaction_id": "TXN_20241220_001",
  "amount": 10000.00,
  "payer_info": {
    "name": "John Doe",
    "country": "US",
    "risk_level": "LOW"
  },
  "merchant_info": {
    "merchant_id": "MERCHANT_001",
    "business_type": "E-COMMERCE"
  }
}
```

**响应**

```json
{
  "code": "200",
  "message": "success",
  "data": {
    "review_result": "APPROVED",
    "risk_score": 85,
    "review_time": "2024-12-20T10:31:00Z",
    "reviewer": "SYSTEM_AUTO"
  }
}
```

#### 6.2.3 计费接口

**EX计费接口**

```http
POST /api/v1/billing/calculate
Content-Type: application/json
Authorization: Bearer {token}

{
  "transaction_id": "TXN_20241220_001",
  "psp_id": "PSP_001",
  "merchant_id": "MERCHANT_001",
  "amount": 10000.00,
  "currency": "USD",
  "transaction_type": "PAYMENT"
}
```

**响应**

```json
{
  "code": "200",
  "message": "success",
  "data": {
    "billing_result": {
      "setup_fee": 0.00,
      "service_fee": 50.00,
      "transaction_fee": 30.00,
      "total_fee": 80.00
    },
    "fee_breakdown": {
      "ex_revenue": 50.00,
      "ipl_fee": 30.00
    },
    "net_amount": 9920.00
  }
}
```

#### 6.2.4 账户管理接口

**账户余额查询**

```http
GET /api/v1/account/{account_id}/balance
Authorization: Bearer {token}
```

**响应**

```json
{
  "code": "200",
  "message": "success",
  "data": {
    "account_id": "PSP_001_MAIN",
    "account_type": "MAIN",
    "balance": 150000.00,
    "available_balance": 148000.00,
    "frozen_balance": 2000.00,
    "currency": "USD",
    "last_updated": "2024-12-20T10:30:00Z"
  }
}
```

**归集操作接口**

```http
POST /api/v1/account/collect
Content-Type: application/json
Authorization: Bearer {token}

{
  "from_account": "MERCHANT_001_SUB",
  "to_account": "PSP_001_MAIN",
  "amount": 5000.00,
  "currency": "USD",
  "reference": "COLLECT_20241220_001"
}
```

#### 6.2.5 轧差结算接口

**生成轧差对账单**

```http
POST /api/v1/settlement/reconciliation/generate
Content-Type: application/json
Authorization: Bearer {token}

{
  "settlement_period": {
    "start_date": "2024-12-01",
    "end_date": "2024-12-31"
  },
  "psp_id": "PSP_001",
  "settlement_type": "NETTING"
}
```

**响应**

```json
{
  "code": "200",
  "message": "success",
  "data": {
    "reconciliation_id": "RECON_20241220_001",
    "psp_id": "PSP_001",
    "settlement_period": "2024-12",
    "service_fees": {
      "setup_fee": 1000.00,
      "monthly_service_fee": 5000.00,
      "transaction_processing_fee": 2500.00,
      "total_service_fees": 8500.00
    },
    "commission_rebates": {
      "transaction_commission": 1200.00,
      "volume_bonus": 300.00,
      "total_rebates": 1500.00
    },
    "netting_result": {
      "gross_amount": 8500.00,
      "rebate_amount": 1500.00,
      "net_amount": 7000.00,
      "payment_direction": "PSP_TO_EX"
    },
    "transaction_summary": {
      "transaction_count": 100,
      "total_transaction_amount": 1000000.00
    },
    "due_date": "2025-01-15",
    "status": "PENDING_CONFIRMATION"
  }
}
```

**对账单确认接口**

```http
POST /api/v1/settlement/reconciliation/confirm
Content-Type: application/json
Authorization: Bearer {token}

{
  "reconciliation_id": "RECON_20241220_001",
  "psp_id": "PSP_001",
  "confirmation_status": "CONFIRMED",
  "remarks": "对账单数据确认无误"
}
```

**支付方式选择接口**

```http
POST /api/v1/settlement/payment-method/select
Content-Type: application/json
Authorization: Bearer {token}

{
  "reconciliation_id": "RECON_20241220_001",
  "psp_id": "PSP_001",
  "payment_method": "IPL_ACCOUNT",
  "payment_details": {
    "account_type": "PSP_MAIN_ACCOUNT",
    "account_id": "PSP_001_MAIN",
    "remarks": "通过IPL主账户支付"
  }
}
```

**响应**

```json
{
  "code": "200",
  "message": "success",
  "data": {
    "payment_instruction_id": "PAY_INST_20241220_001",
    "payment_method": "IPL_ACCOUNT",
    "payment_amount": 7000.00,
    "estimated_completion": "2024-12-20T16:00:00Z",
    "status": "PENDING_EXECUTION"
  }
}
```

**IPL账户扣款接口**

```http
POST /api/v1/settlement/ipl-account/deduct
Content-Type: application/json
Authorization: Bearer {token}

{
  "payment_instruction_id": "PAY_INST_20241220_001",
  "from_account": "PSP_001_MAIN",
  "to_account": "EX_AGENT_001",
  "amount": 7000.00,
  "reference": "RECON_20241220_001"
}
```

**银行转账通知接口**

```http
POST /api/v1/settlement/bank-transfer/notify
Content-Type: application/json
Authorization: Bearer {token}

{
  "reconciliation_id": "RECON_20241220_001",
  "payment_reference": "BANK_TXN_20241220_001",
  "payment_amount": 7000.00,
  "payment_date": "2024-12-20",
  "payment_method": "BANK_TRANSFER",
  "payment_direction": "PSP_TO_EX",
  "bank_info": {
    "from_bank": "ABC Bank",
    "from_account": "PSP_001_BANK_ACC",
    "from_account_name": "Alpha Payment Services",
    "to_bank": "XYZ Bank",
    "to_account": "EX_BANK_ACC_001",
    "to_account_name": "EX Technology Co., Ltd"
  }
}
```

### 6.3 数据模型

#### 6.3.1 核心实体模型

**商户模型**

```json
{
  "merchant_id": "MERCHANT_001",
  "merchant_name": "ABC Company",
  "psp_id": "PSP_001",
  "business_type": "E_COMMERCE",
  "registration_info": {
    "country": "US",
    "registration_number": "REG123456",
    "tax_id": "TAX789012"
  },
  "account_info": {
    "sub_account_id": "MERCHANT_001_SUB",
    "va_accounts": ["VA_1234567890", "VA_0987654321"]
  },
  "status": "ACTIVE",
  "created_at": "2024-01-01T00:00:00Z"
}
```

**交易模型**

```json
{
  "transaction_id": "TXN_20241220_001",
  "va_account": "VA_1234567890",
  "merchant_id": "MERCHANT_001",
  "psp_id": "PSP_001",
  "amount": 10000.00,
  "currency": "USD",
  "transaction_type": "PAYMENT",
  "payer_info": {
    "name": "John Doe",
    "account": "ACC_123456",
    "bank": "BANK_001"
  },
  "fees": {
    "ex_fee": 50.00,
    "ipl_fee": 30.00,
    "total_fee": 80.00
  },
  "status": "COMPLETED",
  "risk_review": {
    "result": "APPROVED",
    "score": 85,
    "reviewer": "SYSTEM_AUTO"
  },
  "timestamps": {
    "created_at": "2024-12-20T10:30:00Z",
    "completed_at": "2024-12-20T10:31:00Z"
  }
}
```

#### 6.3.2 账户模型

**账户基础模型**

```json
{
  "account_id": "PSP_001_MAIN",
  "account_type": "MAIN",
  "owner_id": "PSP_001",
  "owner_type": "PSP",
  "currency": "USD",
  "balance": {
    "available": 148000.00,
    "frozen": 2000.00,
    "total": 150000.00
  },
  "limits": {
    "daily_limit": 1000000.00,
    "monthly_limit": ********.00
  },
  "status": "ACTIVE",
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### 6.3.3 轧差结算模型

**轧差对账单模型**

```json
{
  "reconciliation_id": "RECON_20241220_001",
  "psp_id": "PSP_001",
  "psp_name": "Alpha Payment Services",
  "settlement_period": {
    "start_date": "2024-12-01",
    "end_date": "2024-12-31",
    "period_type": "MONTHLY"
  },
  "service_fees": {
    "setup_fee": {
      "amount": 1000.00,
      "description": "系统接入费",
      "quantity": 1
    },
    "monthly_service_fee": {
      "amount": 5000.00,
      "description": "科技服务费",
      "rate": "月度固定费用"
    },
    "transaction_processing_fee": {
      "amount": 2500.00,
      "description": "交易处理费",
      "rate": 0.25,
      "transaction_count": 100,
      "total_transaction_amount": 1000000.00
    },
    "total_service_fees": 8500.00
  },
  "commission_rebates": {
    "transaction_commission": {
      "amount": 1200.00,
      "description": "交易返点",
      "rate": 0.12,
      "applicable_amount": 1000000.00
    },
    "volume_bonus": {
      "amount": 300.00,
      "description": "业绩奖励",
      "threshold": "月交易量超100万"
    },
    "total_rebates": 1500.00
  },
  "netting_result": {
    "gross_service_fees": 8500.00,
    "total_rebates": 1500.00,
    "net_amount": 7000.00,
    "payment_direction": "PSP_TO_EX",
    "currency": "USD"
  },
  "payment_options": [
    {
      "method": "IPL_ACCOUNT",
      "description": "通过IPL主账户支付",
      "available": true
    },
    {
      "method": "BANK_TRANSFER",
      "description": "线下银行转账",
      "available": true
    }
  ],
  "status": "PENDING_CONFIRMATION",
  "due_date": "2025-01-15",
  "created_at": "2024-12-20T10:00:00Z",
  "updated_at": "2024-12-20T10:00:00Z"
}
```

**轧差支付记录模型**

```json
{
  "payment_id": "PAY_20241220_001",
  "reconciliation_id": "RECON_20241220_001",
  "payment_instruction_id": "PAY_INST_20241220_001",
  "payment_amount": 7000.00,
  "payment_direction": "PSP_TO_EX",
  "payment_method": "IPL_ACCOUNT",
  "payment_date": "2024-12-20T14:30:00Z",
  "payment_details": {
    "ipl_account_info": {
      "from_account": "PSP_001_MAIN",
      "from_account_name": "Alpha Payment Services",
      "to_account": "EX_AGENT_001",
      "to_account_name": "EX Technology Co., Ltd",
      "transaction_reference": "IPL_TXN_20241220_001"
    }
  },
  "status": "COMPLETED",
  "confirmation": {
    "confirmed_by": "EX_FINANCE_TEAM",
    "confirmed_at": "2024-12-20T15:00:00Z",
    "remarks": "IPL账户转账已确认到账"
  }
}
```

**银行转账支付记录模型**

```json
{
  "payment_id": "PAY_20241220_002",
  "reconciliation_id": "RECON_20241220_002",
  "payment_amount": 5000.00,
  "payment_direction": "EX_TO_PSP",
  "payment_method": "BANK_TRANSFER",
  "payment_date": "2024-12-20T16:30:00Z",
  "payment_details": {
    "bank_info": {
      "from_bank": "XYZ Bank",
      "from_account": "EX_BANK_ACC_001",
      "from_account_name": "EX Technology Co., Ltd",
      "to_bank": "ABC Bank",
      "to_account": "PSP_001_BANK_ACC",
      "to_account_name": "Alpha Payment Services",
      "payment_reference": "BANK_TXN_20241220_002"
    }
  },
  "status": "COMPLETED",
  "confirmation": {
    "confirmed_by": "PSP_FINANCE_TEAM",
    "confirmed_at": "2024-12-20T17:00:00Z",
    "remarks": "EX返点银行转账已确认到账"
  }
}
```

### 6.4 错误码定义

| 错误码         | 错误信息                         | 说明            |
| -------------- | -------------------------------- | --------------- |
| **200**  | Success                          | 请求成功        |
| **400**  | Bad Request                      | 请求参数错误    |
| **401**  | Unauthorized                     | 未授权访问      |
| **403**  | Forbidden                        | 权限不足        |
| **404**  | Not Found                        | 资源不存在      |
| **500**  | Internal Server Error            | 服务器内部错误  |
| **1001** | Invalid Merchant                 | 商户信息无效    |
| **1002** | Insufficient Balance             | 账户余额不足    |
| **1003** | Risk Review Failed               | 风控审核失败    |
| **1004** | VA Creation Failed               | VA创建失败      |
| **1005** | Transaction Limit Exceeded       | 交易限额超限    |
| **2001** | Billing Calculation Error        | 计费计算错误    |
| **2002** | Reconciliation Generation Failed | 对账单生成失败  |
| **2003** | Reconciliation Not Found         | 对账单不存在    |
| **2004** | Reconciliation Already Settled   | 对账单已结算    |
| **2005** | Payment Method Not Available     | 支付方式不可用  |
| **2006** | Netting Calculation Error        | 轧差计算错误    |
| **2007** | Payment Confirmation Failed      | 支付确认失败    |
| **2008** | Commission Rate Invalid          | 返点费率无效    |
| **2009** | IPL Account Insufficient         | IPL账户余额不足 |
| **2010** | Bank Transfer Failed             | 银行转账失败    |
| **3001** | Account Not Found                | 账户不存在      |
| **3002** | Account Frozen                   | 账户已冻结      |
| **3003** | Collection Failed                | 归集失败        |

### 6.5 接口认证

#### 6.5.1 API Key认证

```http
Authorization: Bearer {api_key}
X-API-Version: v1
X-Request-ID: {unique_request_id}
```

#### 6.5.2 签名认证

```http
Authorization: Bearer {api_key}
X-Signature: {hmac_sha256_signature}
X-Timestamp: {unix_timestamp}
X-Nonce: {random_string}
```

**签名算法**

```
signature = HMAC-SHA256(api_secret, method + url + timestamp + nonce + body)
```
