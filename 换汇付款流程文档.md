# 换汇付款（C+P）流程文档

## 概述

换汇付款（Currency Exchange + Payment）是指商户使用一种货币账户余额，通过换汇方式向收款人支付另一种货币的业务流程。本文档以商户USD账户付款CNY为例，详细说明整个业务流程。

## 业务场景

- **付款方**: 商户USD账户
- **收款方**: CNY收款人
- **核心逻辑**: 优先保证客户下发成功
- **流程特点**: 先查余额，后决策路由

## 主要参与方

| 参与方     | 说明                   |
| ---------- | ---------------------- |
| 商户       | 发起换汇付款请求的客户 |
| 付款系统   | 处理付款请求的核心系统 |
| 渠道管理   | 管理各个支付渠道的系统 |
| 换汇系统   | 处理货币兑换的系统     |
| 自营渠道   | 虚拟渠道，用于资金调度 |
| 第三方渠道 | 外部支付渠道           |

## 流程详述

### 场景一：渠道余额充足

当目标货币（CNY）渠道余额充足时，系统优先使用现有余额完成下发，然后异步处理换汇交割。

### 场景二：渠道余额不足

当目标货币（CNY）渠道余额不足时，系统会：

1. 路由到下一个可用渠道
2. 如果只有一个渠道，则在该渠道执行换汇后下发
3. 先产生换汇支付单，再执行下发

## 时序图

```mermaid
sequenceDiagram
    participant M as 商户
    participant PS as 付款系统
    participant CM as 渠道管理
    participant ES as 换汇系统
    participant SC as 自营渠道
    participant TC as 第三方渠道
    participant RM as 资金老师

    M->>PS: 发起换汇付款请求(USD→CNY)
    PS->>PS: 渠道路由
    PS->>CM: 查询CNY渠道余额
  
    alt 余额充足场景
        CM->>PS: 返回余额充足
        PS->>TC: 使用CNY余额下发付款
        TC->>PS: 下发成功确认
        PS->>M: 返回付款成功
  
        Note over PS,ES: 异步处理换汇交割
        PS->>ES: 创建USD→CNY换汇支付单
        ES->>ES: 换汇路由
        alt 
            RM->>ES: 路由付款渠道
            ES->>TC: 执行换汇交割
            TC->>ES: 交割完成
        else 禁止直接交割
            RM->>ES: 禁止直接交割
            ES->>SC: 路由到自营渠道
            SC->>ES: 自营渠道交割完成
        end
  
        ES->>PS: 换汇交割完成
  
    else 余额不足场景
        CM->>PS: 返回余额不足
        PS->>CM: 查询下一个可用渠道
  
        alt 存在多个渠道
            CM->>PS: 返回下一个渠道信息
            PS->>PS: 递归执行余额检查流程
        else 只有一个渠道
            CM->>PS: 返回唯一渠道信息
            PS->>ES: 创建USD→CNY换汇支付单
            ES->>TC: 指定渠道执行换汇
            TC->>ES: 换汇完成
            ES->>PS: 换汇成功确认
            PS->>TC: 使用换汇后的CNY下发
            TC->>PS: 下发成功确认
            PS->>M: 返回付款成功
        end
    end
```

## 关键决策点

### 1. 余额检查策略

- **优先级**: 客户体验 > 成本优化
- **检查顺序**: 按渠道优先级依次检查
- **阈值设置**: 可配置的最低余额阈值

### 2. 换汇交割策略

- **直接交割**: 通过第三方渠道直接完成
- **自营交割**: 通过虚拟自营渠道完成
- **决策因子**: 资金老师的风控策略

### 3. 渠道路由策略

- **多渠道**: 按优先级轮询
- **单渠道**: 强制换汇后下发
- **失败处理**: 自动切换备用渠道

## 异常处理

### 下发失败

1. 记录失败原因
2. 回滚已扣除的USD余额
3. 取消相关换汇支付单
4. 通知商户付款失败

### 换汇失败

1. 记录换汇失败原因
2. 保持CNY下发成功状态
3. 人工介入处理换汇交割
4. 风险监控和报警

### 渠道异常

1. 自动切换备用渠道
2. 记录渠道异常信息
3. 通知运维团队处理
4. 更新渠道健康状态

## 监控指标

| 指标名称     | 说明               | 阈值   |
| ------------ | ------------------ | ------ |
| 付款成功率   | 整体付款成功比例   | >99.5% |
| 换汇成功率   | 换汇交割成功比例   | >99.9% |
| 平均处理时间 | 从请求到完成的时间 | <30秒  |
| 渠道可用率   | 各渠道的可用性     | >99.9% |

## 配置参数

```yaml
# 渠道配置
channels:
  - name: "渠道A"
    currency: "CNY"
    priority: 1
    min_balance: 10000
  - name: "渠道B" 
    currency: "CNY"
    priority: 2
    min_balance: 5000

# 换汇配置
exchange:
  auto_settlement: true
  settlement_channel: "self_operated"
  max_retry: 3
  timeout: 300

# 风控配置
risk_control:
  direct_settlement_enabled: true
  daily_limit: 1000000
  single_limit: 100000
```

## 总结

换汇付款流程通过智能的余额检查和渠道路由机制，确保了客户体验的同时优化了资金使用效率。关键在于：

1. **客户优先**: 优先保证下发成功
2. **智能路由**: 根据余额情况动态选择策略
3. **异步处理**: 换汇交割与付款下发解耦
4. **风险控制**: 多层次的异常处理和监控

该流程设计兼顾了业务需求、技术实现和风险控制，为跨币种付款提供了稳定可靠的解决方案。
