<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPL运营后台 - 资金账户管理</title>
    <style>
        /* 通用样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            text-align: center;
            font-size: 28px;
            font-weight: 600;
        }

        .header .subtitle {
            text-align: center;
            font-size: 16px;
            opacity: 0.9;
            margin-top: 8px;
        }

        /* 导航栏 */
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            background: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-right: 1px solid #eee;
        }

        .nav-tab:last-child {
            border-right: none;
        }

        .nav-tab.active {
            background: #28a745;
            color: white;
        }

        .nav-tab:hover:not(.active) {
            background: #f8f9fa;
            color: #333;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            text-align: center;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #28a745;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .stat-change {
            font-size: 12px;
            margin-top: 8px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .stat-change.positive {
            background: #d4edda;
            color: #155724;
        }

        .stat-change.negative {
            background: #f8d7da;
            color: #721c24;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            margin-bottom: 24px;
            overflow: hidden;
        }

        .card-header {
            padding: 20px 24px;
            border-bottom: 1px solid #eee;
            background: #fafbfc;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .card-body {
            padding: 24px;
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8f9fa;
            padding: 16px 20px;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #dee2e6;
            font-size: 14px;
        }

        .table td {
            padding: 16px 20px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        /* 状态标签 */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-frozen {
            background: #f8d7da;
            color: #721c24;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #28a745;
            color: white;
        }

        .btn-primary:hover {
            background: #218838;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 搜索筛选 */
        .search-filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }

        .form-input {
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 账户信息卡片 */
        .account-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .account-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .account-card h3 {
            font-size: 18px;
            margin-bottom: 16px;
            opacity: 0.9;
        }

        .account-balance {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .account-details {
            font-size: 14px;
            opacity: 0.8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }

            .form-group {
                min-width: auto;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .account-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>IPL运营后台</h1>
            <div class="subtitle">资金账户管理系统 | IPL Payment Company</div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">账户概览</button>
            <button class="nav-tab" onclick="showTab('main-accounts')">PSP主账户</button>
            <button class="nav-tab" onclick="showTab('sub-accounts')">商户子账户</button>
            <button class="nav-tab" onclick="showTab('agent-accounts')">EX代理账户</button>
            <button class="nav-tab" onclick="showTab('transactions')">交易记录</button>
        </div>

        <!-- 账户概览 -->
        <div id="overview" class="tab-content active">
            <!-- 总体统计 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">$45.2M</div>
                    <div class="stat-label">总资金池</div>
                    <div class="stat-change positive">+5.2%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">156</div>
                    <div class="stat-label">PSP主账户数</div>
                    <div class="stat-change positive">+3</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">2,847</div>
                    <div class="stat-label">商户子账户数</div>
                    <div class="stat-change positive">+45</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">12</div>
                    <div class="stat-label">EX代理账户数</div>
                    <div class="stat-change positive">+1</div>
                </div>
            </div>

            <!-- 账户类型分布 -->
            <div class="account-info">
                <div class="account-card">
                    <h3>PSP主账户池</h3>
                    <div class="account-balance">$38.5M</div>
                    <div class="account-details">
                        活跃账户: 142 | 冻结账户: 14<br>
                        今日流入: $2.3M | 今日流出: $1.8M
                    </div>
                </div>
                <div class="account-card">
                    <h3>商户子账户池</h3>
                    <div class="account-balance">$5.8M</div>
                    <div class="account-details">
                        活跃账户: 2,654 | 冻结账户: 193<br>
                        今日收款: $890K | 今日归集: $1.2M
                    </div>
                </div>
                <div class="account-card">
                    <h3>EX代理账户池</h3>
                    <div class="account-balance">$890K</div>
                    <div class="account-details">
                        活跃账户: 11 | 冻结账户: 1<br>
                        今日代收: $45K | 今日结算: $38K
                    </div>
                </div>
            </div>

            <!-- 今日交易概览 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">今日交易概览</h3>
                    <button class="btn btn-primary btn-sm">实时刷新</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>交易类型</th>
                                    <th>交易笔数</th>
                                    <th>交易金额</th>
                                    <th>成功率</th>
                                    <th>平均金额</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>收款入账</td>
                                    <td>1,245</td>
                                    <td>$890,560</td>
                                    <td>99.2%</td>
                                    <td>$715</td>
                                </tr>
                                <tr>
                                    <td>资金归集</td>
                                    <td>456</td>
                                    <td>$1,234,890</td>
                                    <td>100%</td>
                                    <td>$2,708</td>
                                </tr>
                                <tr>
                                    <td>代扣费用</td>
                                    <td>89</td>
                                    <td>$45,670</td>
                                    <td>98.9%</td>
                                    <td>$513</td>
                                </tr>
                                <tr>
                                    <td>提现转账</td>
                                    <td>234</td>
                                    <td>$1,789,450</td>
                                    <td>99.6%</td>
                                    <td>$7,647</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- PSP主账户 -->
        <div id="main-accounts" class="tab-content">
            <!-- 搜索筛选 -->
            <div class="search-filters">
                <div class="filter-row">
                    <div class="form-group">
                        <label class="form-label">PSP ID</label>
                        <input type="text" class="form-input" placeholder="输入PSP ID">
                    </div>
                    <div class="form-group">
                        <label class="form-label">账户状态</label>
                        <select class="form-input">
                            <option value="">全部状态</option>
                            <option value="active">活跃</option>
                            <option value="frozen">冻结</option>
                            <option value="suspended">暂停</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">余额范围</label>
                        <select class="form-input">
                            <option value="">全部范围</option>
                            <option value="0-10000">$0 - $10K</option>
                            <option value="10000-100000">$10K - $100K</option>
                            <option value="100000+">$100K+</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary">搜索</button>
                    </div>
                </div>
            </div>

            <!-- PSP主账户列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">PSP主账户列表</h3>
                    <button class="btn btn-primary btn-sm">导出列表</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>PSP ID</th>
                                    <th>PSP名称</th>
                                    <th>主账户ID</th>
                                    <th>账户余额</th>
                                    <th>可用余额</th>
                                    <th>冻结余额</th>
                                    <th>子账户数</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>PSP_001</td>
                                    <td>Alpha Payment</td>
                                    <td>MAIN_PSP001_001</td>
                                    <td>$2,456,780</td>
                                    <td>$2,356,780</td>
                                    <td>$100,000</td>
                                    <td>245</td>
                                    <td><span class="status-badge status-active">活跃</span></td>
                                    <td><button class="btn btn-primary btn-sm">管理</button></td>
                                </tr>
                                <tr>
                                    <td>PSP_002</td>
                                    <td>Beta Finance</td>
                                    <td>MAIN_PSP002_001</td>
                                    <td>$1,789,450</td>
                                    <td>$1,789,450</td>
                                    <td>$0</td>
                                    <td>156</td>
                                    <td><span class="status-badge status-active">活跃</span></td>
                                    <td><button class="btn btn-primary btn-sm">管理</button></td>
                                </tr>
                                <tr>
                                    <td>PSP_003</td>
                                    <td>Gamma Pay</td>
                                    <td>MAIN_PSP003_001</td>
                                    <td>$890,560</td>
                                    <td>$840,560</td>
                                    <td>$50,000</td>
                                    <td>89</td>
                                    <td><span class="status-badge status-frozen">冻结</span></td>
                                    <td><button class="btn btn-primary btn-sm">管理</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 主账户交易记录 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">主账户交易记录</h3>
                    <button class="btn btn-primary btn-sm">查看全部</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>交易ID</th>
                                    <th>PSP ID</th>
                                    <th>交易类型</th>
                                    <th>交易金额</th>
                                    <th>余额变动</th>
                                    <th>交易时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>TXN_20241220_001</td>
                                    <td>PSP_001</td>
                                    <td>归集入账</td>
                                    <td>$45,670</td>
                                    <td>+$45,670</td>
                                    <td>2024-12-20 14:30</td>
                                    <td><span class="status-badge status-active">成功</span></td>
                                </tr>
                                <tr>
                                    <td>TXN_20241220_002</td>
                                    <td>PSP_001</td>
                                    <td>代扣费用</td>
                                    <td>$2,340</td>
                                    <td>-$2,340</td>
                                    <td>2024-12-20 15:00</td>
                                    <td><span class="status-badge status-active">成功</span></td>
                                </tr>
                                <tr>
                                    <td>TXN_20241220_003</td>
                                    <td>PSP_002</td>
                                    <td>提现转账</td>
                                    <td>$100,000</td>
                                    <td>-$100,000</td>
                                    <td>2024-12-20 16:15</td>
                                    <td><span class="status-badge status-pending">处理中</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 商户子账户 -->
        <div id="sub-accounts" class="tab-content">
            <!-- 搜索筛选 -->
            <div class="search-filters">
                <div class="filter-row">
                    <div class="form-group">
                        <label class="form-label">商户ID</label>
                        <input type="text" class="form-input" placeholder="输入商户ID">
                    </div>
                    <div class="form-group">
                        <label class="form-label">所属PSP</label>
                        <select class="form-input">
                            <option value="">全部PSP</option>
                            <option value="PSP_001">Alpha Payment</option>
                            <option value="PSP_002">Beta Finance</option>
                            <option value="PSP_003">Gamma Pay</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">账户状态</label>
                        <select class="form-input">
                            <option value="">全部状态</option>
                            <option value="active">活跃</option>
                            <option value="frozen">冻结</option>
                            <option value="suspended">暂停</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary">搜索</button>
                    </div>
                </div>
            </div>

            <!-- 商户子账户列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">商户子账户列表</h3>
                    <button class="btn btn-primary btn-sm">导出列表</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>商户ID</th>
                                    <th>商户名称</th>
                                    <th>子账户ID</th>
                                    <th>所属PSP</th>
                                    <th>账户余额</th>
                                    <th>今日收款</th>
                                    <th>VA账户数</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>MERCHANT_001</td>
                                    <td>ABC电商</td>
                                    <td>SUB_MERCHANT001_001</td>
                                    <td>PSP_001</td>
                                    <td>$12,450</td>
                                    <td>$3,670</td>
                                    <td>3</td>
                                    <td><span class="status-badge status-active">活跃</span></td>
                                    <td><button class="btn btn-primary btn-sm">管理</button></td>
                                </tr>
                                <tr>
                                    <td>MERCHANT_002</td>
                                    <td>XYZ贸易</td>
                                    <td>SUB_MERCHANT002_001</td>
                                    <td>PSP_001</td>
                                    <td>$8,920</td>
                                    <td>$1,240</td>
                                    <td>2</td>
                                    <td><span class="status-badge status-active">活跃</span></td>
                                    <td><button class="btn btn-primary btn-sm">管理</button></td>
                                </tr>
                                <tr>
                                    <td>MERCHANT_003</td>
                                    <td>DEF科技</td>
                                    <td>SUB_MERCHANT003_001</td>
                                    <td>PSP_002</td>
                                    <td>$25,680</td>
                                    <td>$5,890</td>
                                    <td>5</td>
                                    <td><span class="status-badge status-frozen">冻结</span></td>
                                    <td><button class="btn btn-primary btn-sm">管理</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 子账户交易记录 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">子账户交易记录</h3>
                    <button class="btn btn-primary btn-sm">查看全部</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>交易ID</th>
                                    <th>商户ID</th>
                                    <th>交易类型</th>
                                    <th>交易金额</th>
                                    <th>手续费</th>
                                    <th>净入账</th>
                                    <th>交易时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>TXN_20241220_101</td>
                                    <td>MERCHANT_001</td>
                                    <td>收款入账</td>
                                    <td>$1,000</td>
                                    <td>$3</td>
                                    <td>$997</td>
                                    <td>2024-12-20 10:15</td>
                                    <td><span class="status-badge status-active">成功</span></td>
                                </tr>
                                <tr>
                                    <td>TXN_20241220_102</td>
                                    <td>MERCHANT_001</td>
                                    <td>归集转出</td>
                                    <td>$5,000</td>
                                    <td>$0</td>
                                    <td>-$5,000</td>
                                    <td>2024-12-20 14:30</td>
                                    <td><span class="status-badge status-active">成功</span></td>
                                </tr>
                                <tr>
                                    <td>TXN_20241220_103</td>
                                    <td>MERCHANT_002</td>
                                    <td>收款入账</td>
                                    <td>$2,500</td>
                                    <td>$7.5</td>
                                    <td>$2,492.5</td>
                                    <td>2024-12-20 11:45</td>
                                    <td><span class="status-badge status-active">成功</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- EX代理账户 -->
        <div id="agent-accounts" class="tab-content">
            <!-- EX代理账户统计 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">$890K</div>
                    <div class="stat-label">代理账户总余额</div>
                    <div class="stat-change positive">+12.3%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$45K</div>
                    <div class="stat-label">今日代收费用</div>
                    <div class="stat-change positive">+8.7%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$38K</div>
                    <div class="stat-label">今日结算金额</div>
                    <div class="stat-change positive">+15.2%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">11</div>
                    <div class="stat-label">活跃代理账户</div>
                    <div class="stat-change positive">+0</div>
                </div>
            </div>

            <!-- EX代理账户列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">EX代理账户列表</h3>
                    <button class="btn btn-primary btn-sm">导出列表</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>EX ID</th>
                                    <th>EX公司名称</th>
                                    <th>代理账户ID</th>
                                    <th>账户余额</th>
                                    <th>代收余额</th>
                                    <th>服务PSP数</th>
                                    <th>本月代收</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>EX_001</td>
                                    <td>Alpha Tech Solutions</td>
                                    <td>AGENT_EX001_001</td>
                                    <td>$456,780</td>
                                    <td>$23,450</td>
                                    <td>45</td>
                                    <td>$234,560</td>
                                    <td><span class="status-badge status-active">活跃</span></td>
                                    <td><button class="btn btn-primary btn-sm">管理</button></td>
                                </tr>
                                <tr>
                                    <td>EX_002</td>
                                    <td>Beta Payment Tech</td>
                                    <td>AGENT_EX002_001</td>
                                    <td>$234,560</td>
                                    <td>$12,340</td>
                                    <td>28</td>
                                    <td>$156,780</td>
                                    <td><span class="status-badge status-active">活跃</span></td>
                                    <td><button class="btn btn-primary btn-sm">管理</button></td>
                                </tr>
                                <tr>
                                    <td>EX_003</td>
                                    <td>Gamma Fintech</td>
                                    <td>AGENT_EX003_001</td>
                                    <td>$123,450</td>
                                    <td>$8,900</td>
                                    <td>18</td>
                                    <td>$89,670</td>
                                    <td><span class="status-badge status-active">活跃</span></td>
                                    <td><button class="btn btn-primary btn-sm">管理</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- EX代理账户交易记录 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">代理账户交易记录</h3>
                    <button class="btn btn-primary btn-sm">查看全部</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>交易ID</th>
                                    <th>EX ID</th>
                                    <th>交易类型</th>
                                    <th>交易金额</th>
                                    <th>关联PSP</th>
                                    <th>交易时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>TXN_20241220_201</td>
                                    <td>EX_001</td>
                                    <td>代收费用</td>
                                    <td>$2,340</td>
                                    <td>PSP_001</td>
                                    <td>2024-12-20 15:00</td>
                                    <td><span class="status-badge status-active">成功</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>TXN_20241220_202</td>
                                    <td>EX_001</td>
                                    <td>返点结算</td>
                                    <td>$1,560</td>
                                    <td>PSP_002</td>
                                    <td>2024-12-20 16:30</td>
                                    <td><span class="status-badge status-active">成功</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>TXN_20241220_203</td>
                                    <td>EX_002</td>
                                    <td>IPL返点</td>
                                    <td>$890</td>
                                    <td>-</td>
                                    <td>2024-12-20 18:00</td>
                                    <td><span class="status-badge status-pending">处理中</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 交易记录 -->
        <div id="transactions" class="tab-content">
            <!-- 搜索筛选 -->
            <div class="search-filters">
                <div class="filter-row">
                    <div class="form-group">
                        <label class="form-label">交易时间</label>
                        <input type="date" class="form-input" value="2024-12-20">
                    </div>
                    <div class="form-group">
                        <label class="form-label">至</label>
                        <input type="date" class="form-input" value="2024-12-20">
                    </div>
                    <div class="form-group">
                        <label class="form-label">交易类型</label>
                        <select class="form-input">
                            <option value="">全部类型</option>
                            <option value="payment">收款入账</option>
                            <option value="collection">资金归集</option>
                            <option value="deduction">代扣费用</option>
                            <option value="withdrawal">提现转账</option>
                            <option value="settlement">结算转账</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">交易状态</label>
                        <select class="form-input">
                            <option value="">全部状态</option>
                            <option value="success">成功</option>
                            <option value="pending">处理中</option>
                            <option value="failed">失败</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary">搜索</button>
                    </div>
                </div>
            </div>

            <!-- 交易统计 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">2,024</div>
                    <div class="stat-label">今日交易笔数</div>
                    <div class="stat-change positive">+156</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$4.96M</div>
                    <div class="stat-label">今日交易金额</div>
                    <div class="stat-change positive">+12.8%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">99.2%</div>
                    <div class="stat-label">交易成功率</div>
                    <div class="stat-change positive">+0.3%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$2,451</div>
                    <div class="stat-label">平均交易金额</div>
                    <div class="stat-change negative">-5.2%</div>
                </div>
            </div>

            <!-- 交易记录列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">交易记录列表</h3>
                    <button class="btn btn-primary btn-sm">导出记录</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>交易ID</th>
                                    <th>交易类型</th>
                                    <th>发起方</th>
                                    <th>接收方</th>
                                    <th>交易金额</th>
                                    <th>手续费</th>
                                    <th>交易时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>TXN_20241220_001</td>
                                    <td>收款入账</td>
                                    <td>付款方_001</td>
                                    <td>MERCHANT_001</td>
                                    <td>$1,000</td>
                                    <td>$3</td>
                                    <td>2024-12-20 10:15</td>
                                    <td><span class="status-badge status-active">成功</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>TXN_20241220_002</td>
                                    <td>资金归集</td>
                                    <td>MERCHANT_001</td>
                                    <td>PSP_001</td>
                                    <td>$5,000</td>
                                    <td>$0</td>
                                    <td>2024-12-20 14:30</td>
                                    <td><span class="status-badge status-active">成功</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>TXN_20241220_003</td>
                                    <td>代扣费用</td>
                                    <td>EX_001</td>
                                    <td>PSP_001</td>
                                    <td>$2,340</td>
                                    <td>$0</td>
                                    <td>2024-12-20 15:00</td>
                                    <td><span class="status-badge status-active">成功</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>TXN_20241220_004</td>
                                    <td>提现转账</td>
                                    <td>PSP_002</td>
                                    <td>银行账户</td>
                                    <td>$100,000</td>
                                    <td>$50</td>
                                    <td>2024-12-20 16:15</td>
                                    <td><span class="status-badge status-pending">处理中</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>TXN_20241220_005</td>
                                    <td>结算转账</td>
                                    <td>IPL</td>
                                    <td>EX_001</td>
                                    <td>$1,560</td>
                                    <td>$0</td>
                                    <td>2024-12-20 18:00</td>
                                    <td><span class="status-badge status-active">成功</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换功能
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的active状态
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页内容
            document.getElementById(tabName).classList.add('active');

            // 设置选中的标签为active状态
            event.target.classList.add('active');
        }

        // 模拟数据刷新
        function refreshData() {
            console.log('刷新数据...');
            // 这里可以添加AJAX请求来刷新数据
        }

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('IPL运营后台加载完成');

            // 设置定时刷新
            setInterval(refreshData, 30000); // 每30秒刷新一次
        });
    </script>
</body>
</html>
