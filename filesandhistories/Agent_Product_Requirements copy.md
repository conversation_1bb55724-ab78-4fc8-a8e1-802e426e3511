# 代理产品需求文档

## 1. 背景

EUREWAX SaaS平台需要建立完整的代理商管理系统，支持代理商从签约、政策配置、分佣计算到结算的全生命周期管理。系统需要支持多种分佣模式和汇率返点机制，确保代理商业务的规范化运营和精确的财务结算。

## 2. 功能需求

### 2.1 代理商签约管理

#### 2.1.1 签约流程需求

**流程描述**：

```mermaid
graph TD
    A[租户运营录入代理商基本信息] --> B[配置代理政策]
    B --> C[财务审核]
    C --> E[运营发送激活链接]
    E --> F[代理商激活]
    F --> D[代理商拓展客户]
    D --> O[流程结束]
```

**功能点**：

- 租户管理员可录入代理商基本信息（名称、联系方式、合同信息等）
- 支持为代理商配置专属的分佣政策和汇率返点政策
- 提供代理商激活功能，控制代理商业务开通
- 代理商激活后可开始拓展客户业务

#### 2.1.2 激活状态管理

**状态图**：

```mermaid
stateDiagram-v2
    [*] --> 未激活
    未激活 --> 已激活: 管理员激活
    已激活 --> 未激活: 管理员停用
```

**功能点**：

- 代理商默认状态为"未激活"
- 管理员可手动激活代理商
- 支持激活状态的切换控制

#### 2.1.3 签约状态管理

**状态图**：

```mermaid
stateDiagram-v2
    [*] --> 未生效
    未生效 --> 生效中: 到达签约开始日期
    生效中 --> 签约过期: 到达签约结束日期
```

**功能点**：

- 代理商默认签约状态为"未生效"
- 系统自动根据签约开始日期将状态更新为"生效中"
- 系统自动根据签约结束日期将状态更新为"签约过期"
- 支持签约期限的自动化管理

### 2.2 代理商政策配置

#### 2.2.1 交易分佣-产品底价配置

**配置流程**：

```mermaid
graph TD
    A[租户运营录入代理商底价] --> B[系统检查底价是否符合规则]
    B --> C[提交工单审核]
    C --> D[财务审核]
    D --> E[更新代理商政策]
```

**功能点**：

- 支持为代理商配置产品底价
- 系统自动检查底价是否符合规则
- 提交工单进行财务审核
- 审核通过后更新代理商政策

**风险控制规则**：

- **价格层级关系**：EX给租户报价 → 租户给代理报价 → 租户给客户报价
- **让利规则**：租户可以给代理突破自己的成本价（让利给代理）
- **风险红线**：租户给代理的价格默认不能突破EX给租户的价格
- **成本保护**：如果突破EX给租户的价格，会导致EX的成本被击穿，存在重大资金风险
- **审核要求**：任何可能突破EX成本价的配置必须经过特殊审核流程
- **本期范围**：本期仅包括底价分佣模式
- **配置参考**：配置底价时，需要参考自身成本价和对客标准报价

#### 2.2.2 EX配置租户底价与汇率返点

**配置流程**：

```mermaid
graph TD
    A[EX配置租户底价与汇率返点] --> B[系统检查配置是否符合规则]
    B --> C[提交工单审核]
    C --> D[财务审核]
    D --> E[更新租户政策]
```

**功能点**：

- 支持EX配置租户底价与汇率返点
- 系统自动检查配置是否符合规则
- 提交工单进行财务审核
- 审核通过后更新租户政策

**风险控制规则**：

- **价格层级关系**：EX给租户报价 → 租户给代理报价 → 租户给客户报价
- **让利规则**：租户可以给代理突破自己的成本价（让利给代理）
- **风险红线**：租户给代理的价格默认不能突破EX给租户的价格
- **成本保护**：如果突破EX给租户的价格，会导致EX的成本被击穿，存在重大资金风险
- **审核要求**：任何可能突破EX成本价的配置必须经过特殊审核流程

#### 2.2.3 汇率返点-固定比例返点配置

**配置流程**：

```mermaid
graph TD
    A[租户运营录入返点比例] --> B[系统检查返点是否符合规则]
    B --> C[提交工单审核]
    C --> D[财务审核]
    D --> E[更新代理商政策]
```
**配置流程**：与产品底价配置流程相同

**功能点**：

- 支持汇率返点比例配置
- 遵循相同的审核流程
- 配置生效后应用于汇率返点计算

**风险控制规则**：

- **价格层级关系**：EX给租户报价 → 租户给代理报价 → 租户给客户报价
- **让利规则**：租户可以给代理突破自己的成本价（让利给代理）
- **风险红线**：租户给代理的汇率默认不能突破EX给租户的汇率
- **成本保护**：如果突破EX给租户的汇率，会导致EX的成本被击穿，存在重大资金风险
- **审核要求**：任何可能突破EX成本汇率的配置必须经过特殊审核流程

#### 2.2.4 工单审核状态管理

**状态图**：

```mermaid
stateDiagram-v2
    [*] --> 待审核
    待审核 --> 审核通过: 审核员通过
    待审核 --> 审核拒绝: 审核员拒绝
```

**功能点**：

- 配置提交后工单状态为"待审核"
- 审核员可选择"审核通过"或"审核拒绝"
- 审核结果影响配置的生效状态

#### 2.2.5 风控配置参数

**风控配置参数表**：

| 参数类型 | 说明 |
|---------|------|
| 可突破底价的配置条数 | 允许突破底价的最大政策条目数量 |
| 突破的浮动值 | 允许突破底价的最大百分比或固定值 |
| 突破的交易笔数 | 允许在突破底价情况下的最大交易笔数 |
| 突破的交易金额 | 允许在突破底价情况下的最大交易金额总额 |

**风控处理机制**：

当交易超过风控阈值时，系统将采取以下措施：
- **出款交易**：直接拦截，不允许完成交易
- **入账交易**：进入 holding 状态，等待人工审核

#### 2.2.6 风控业务流程图

```mermaid
flowchart TD
    A[开始] --> B{配置类型?}
    B -->|租户给代理商配置| C[检查底价]
    B -->|ex给租户配置| D[风控审核]
    
    C --> C1{是否突破ex给租户底价?}
    C1 -->|是| C2[拒绝配置]
    C1 -->|否| C3[允许配置]
    
    D --> D1{是否突破ex底价?}
    D1 -->|是| D2{是否在风控允许范围?}
    D1 -->|否| D3[允许配置]
    
    D2 -->|是| D4[允许配置但标记风险]
    D2 -->|否| D5[拒绝配置]
    
    C3 --> E[配置生效]
    D3 --> E
    D4 --> E
    
    E --> F[交易执行]
    F --> G{是否超过风控阈值?}
    G -->|是| H{交易类型?}
    G -->|否| I[正常处理]
    
    H -->|出款| J[直接拦截]
    H -->|入账| K[进入holding状态]
    
    J --> L[结束]
    K --> M[人工审核]
    I --> L
    M --> L
```

### 2.3 分佣管理

#### 2.3.1 可分佣收费项配置

**功能点**：

- 系统预配置可分佣的收费项，收费类型（比例费/固定费）
- 支持针对不同ex 对不同租户的配置
- 租户对于代理商继承ex-租户的配置
- 无需提供配置页面，系统后台配置即可

#### 2.3.2 分佣计算流程

**计算流程**：

```mermaid
graph TD
    A[每日凌晨同步交易数据] --> B[根据代理商政策计算分佣]
    B --> C[按结算币种计算佣金]
    C --> D[使用实时汇率转换]
    D --> E[生成分佣记录]
```

**功能点**：

- 系统每日自动同步交易数据
- 根据代理商配置的政策自动计算分佣
- 佣金币种等于结算币种
- 使用分佣计算时点的实时汇率进行币种转换
- 生成详细的分佣计算记录

#### 2.3.3 账单生成-核对流程

**账单状态图**：

```mermaid
stateDiagram-v2
    [*] --> 待财务核对
    待财务核对 --> 待代理商确认: 财务核对完成
    待代理商确认 --> 核对完成: 代理商同意
    待代理商确认 --> 代理商拒绝: 代理商拒绝
```

**功能点**：

- 系统自动生成分佣账单，状态为"待财务核对"
- 财务人员核对后状态更新为"待代理商确认"
- 代理商可选择"同意"或"拒绝"账单
- 代理商同意后状态更新为"核对完成"
- 代理商拒绝后状态更新为"代理商拒绝"

### 2.4 结算管理

#### 2.4.1 结算流程

**结算流程**：

```mermaid
graph TD
    A[账单核对完成] --> B[账单结算状态更新为待结算]
    B --> C[财务勾选待结算账单]
    C --> D[生成结算单]
    D --> E[线下出款]
    E --> F[更新结算完成状态]
```

**功能点**：

- 账单核对完成后，结算状态自动更新为"待结算"
- 财务人员可勾选多个待结算账单生成结算单
- 支持线下出款操作
- 出款完成后可更新结算单状态为"结算完成"

#### 2.4.2 结算单状态管理

**状态图**：

```mermaid
stateDiagram-v2
    [*] --> 待结算
    待结算 --> 结算完成: 财务确认出款完成
```

**功能点**：

- 结算单生成后状态为"待结算"
- 财务确认出款完成后状态更新为"结算完成"
- 支持结算状态的查询和管理

## 3. 用户用例 (Use Cases)

### 3.1 UC001: 代理商签约

**参与者**：租户管理员、代理商
**前置条件**：租户管理员已登录系统，具有代理商管理权限

**详细流程**：

**步骤1：录入代理商基本信息**

- **操作**：管理员进入代理商管理页面，点击"新增代理商"按钮
- **界面**：打开代理商信息录入表单，包含以下字段：
  - 代理商全称/简称、合同编号、代理商类型（个人/企业）
  - 代理商等级（A级/B级/C级）、合约期限（开始-结束日期）
  - 联系人信息、银行账户信息、业务范围等
- **效果**：表单实时验证，必填项标红提示，保存后生成代理商ID

**步骤2：配置代理政策**

- **操作**：在代理商详情页点击"配置政策"，选择分佣模式
- **界面**：政策配置向导，支持三种模式选择：
  - 手续费分佣：配置各产品的分佣比例或底价
  - 汇率返点：配置货币对的返点比例
  - 混合模式：同时配置分佣和返点
- **效果**：政策配置保存后状态为"待生效"，显示配置摘要

**步骤3：激活代理商**

- **操作**：管理员在代理商列表中点击"激活"按钮
- **界面**：弹出确认对话框，显示代理商信息和政策摘要
- **效果**：
  - 代理商状态从"未激活"变为"已激活"
  - 系统发送激活通知邮件给代理商
  - 代理商获得系统登录权限
  - 政策配置正式生效

**步骤4：代理商业务开展**

- **操作**：代理商登录系统，查看可用功能和政策
- **界面**：代理商工作台显示：
  - 当前政策信息（分佣比例、返点比例）
  - 客户管理功能
  - 交易数据查看
  - 分佣账单查询
- **效果**：代理商可以开始拓展客户，产生的交易将按配置政策计算分佣

**后置条件**：代理商账户激活，政策生效，可正常开展业务并获得分佣

**异常流程**：

- 信息录入不完整：系统提示必填项，阻止保存
- 合同编号重复：系统提示重复，要求修改
- 激活失败：记录错误日志，通知管理员处理

---

### 3.2 UC002: 政策配置审核

**参与者**：代理商财务、审核员、系统管理员
**前置条件**：代理商已签约并激活，财务人员具有政策配置权限

**详细流程**：

**步骤1：代理商财务发起配置**

- **操作**：财务登录系统，进入"政策管理"→"新增配置"
- **界面**：配置表单根据类型显示不同字段：
  - 产品底价：选择产品类型，输入底价金额和币种
  - 固定比例：选择产品，设置分佣比例（如2.5%）
  - 汇率返点：选择货币对，设置返点比例（如0.3%）
- **输入示例**：
  - 外贸收款产品底价：0.8%
  - 跨境电商固定分佣：1.2%
  - USD/CNY汇率返点：0.25%
- **效果**：配置保存后状态为"待审核"，生成工单编号

**步骤2：系统生成审核工单**

- **操作**：系统自动触发工单生成流程
- **界面**：工单包含以下信息：
  - 工单编号、申请人、申请时间
  - 代理商信息、配置类型、具体参数
  - 风险评估（如：比例是否超出标准范围）
  - 审核建议（系统根据规则自动生成）
- **效果**：
  - 审核员收到待审核通知
  - 配置状态显示为"审核中"
  - 申请人可查看审核进度

**步骤3：审核员处理审核**

- **操作**：审核员登录系统，进入"工单管理"→"待审核列表"
- **界面**：审核页面显示：
  - 申请详情和配置参数对比
  - 历史配置记录
  - 风险评估结果
  - 审核意见输入框
  - "通过"/"拒绝"按钮
- **审核标准**：
  - 底价不低于成本价
  - 分佣比例在合理范围内（0.5%-3%）
  - 汇率返点不超过0.5%
- **效果**：
  - 审核通过：配置立即生效，状态变为"已生效"
  - 审核拒绝：配置失效，申请人收到拒绝通知和原因

**步骤4：配置生效处理**

- **操作**：系统自动处理审核结果
- **界面**：
  - 申请人收到审核结果通知
  - 政策配置页面更新状态
  - 生效的配置显示在"当前政策"列表中
- **效果**：
  - 新配置从次日开始应用于分佣计算
  - 旧配置自动失效
  - 生成配置变更记录

**后置条件**：政策配置生效，用于后续交易的分佣计算

**异常流程**：

- 配置参数超出限制：系统自动拒绝，提示修改
- 审核超时：系统自动提醒，升级处理
- 配置冲突：系统检测并提示处理方案

---

### 3.3 UC003: 分佣计算与账单生成

**参与者**：系统（自动化）、财务人员、代理商
**前置条件**：存在有效的代理商政策配置，有交易数据产生

**详细流程**：

**步骤1：系统自动同步交易数据**

- **操作**：系统每日凌晨2:00自动执行数据同步任务
- **处理逻辑**：
  - 从交易系统拉取前一日的所有交易记录
  - 筛选出代理商相关的交易（根据客户归属关系）
  - 验证数据完整性（交易金额、手续费、汇率等）
- **数据示例**：
  ```
  交易ID: TXN20240129001
  代理商: AG001 (润泽科技)
  客户: CU12345
  产品: 外贸收款
  交易金额: $10,000
  手续费: $80 (0.8%)
  结算币种: CNY
  交易时间: 2024-01-28 14:30:00
  ```
- **效果**：交易数据导入分佣计算表，状态为"待计算"

**步骤2：自动计算分佣金额**

- **操作**：系统根据代理商政策自动计算分佣
- **计算逻辑**：
  - 获取代理商当前生效的政策配置
  - 根据产品类型选择对应的分佣规则
  - 计算分佣金额和汇率返点
- **计算示例**：
  ```
  基础信息：
  - 交易手续费：$80
  - 代理商底价：0.6%
  - 实际费率：0.8%
  - 汇率：USD/CNY = 7.2500

  分佣计算：
  - 分佣金额 = $10,000 × (0.8% - 0.6%) = $20
  - 分佣人民币 = $20 × 7.2500 = ¥145

  汇率返点：
  - 返点比例：0.25%
  - 返点金额 = $10,000 × 0.25% = $25
  - 返点人民币 = $25 × 7.2500 = ¥181.25

  总分佣 = ¥145 + ¥181.25 = ¥326.25
  ```
- **效果**：生成详细的分佣计算记录，包含计算过程和结果

**步骤3：生成分佣账单**

- **操作**：系统按代理商和月份汇总生成账单
- **界面**：账单包含以下信息：
  - 账单编号、代理商信息、账单周期
  - 交易汇总：笔数、总金额、总手续费
  - 分佣明细：按产品类型分组显示
  - 汇率返点明细：按货币对分组显示
  - 账单总金额、应付金额
- **账单示例**：
  ```
  账单编号：BILL202401-AG001
  代理商：润泽科技 (AG001)
  账单周期：2024年1月

  交易汇总：
  - 交易笔数：156笔
  - 交易总额：$1,250,000
  - 手续费总额：$12,500

  分佣明细：
  - 外贸收款：¥18,750 (125笔)
  - 跨境电商：¥5,625 (31笔)

  汇率返点：
  - USD/CNY：¥3,125
  - EUR/CNY：¥875

  账单总额：¥28,375
  ```
- **效果**：账单状态为"待财务核对"，代理商可查看但不可确认

**步骤4：财务核对账单**

- **操作**：财务人员登录系统，进入"账单管理"→"待核对列表"
- **界面**：核对页面显示：
  - 账单详情和分佣计算明细
  - 交易数据抽样验证
  - 异常交易标记（如金额异常、费率异常）
  - 核对意见输入框
  - "核对完成"/"退回修正"按钮
- **核对要点**：
  - 验证分佣计算准确性
  - 检查汇率使用是否正确
  - 确认政策应用是否准确
  - 核实异常交易处理
- **效果**：
  - 核对通过：账单状态变为"待代理商确认"
  - 退回修正：账单状态变为"核对中"，记录问题

**步骤5：代理商确认账单**

- **操作**：代理商登录系统，查看"待确认账单"
- **界面**：确认页面显示：
  - 完整的账单信息
  - 分佣明细可展开查看
  - 历史账单对比
  - 确认意见输入框
  - "同意"/"拒绝"按钮
- **代理商检查要点**：
  - 核实交易笔数和金额
  - 确认分佣比例应用正确
  - 检查特殊交易处理
- **效果**：
  - 同意：账单状态变为"核对完成"，进入待结算
  - 拒绝：账单状态变为"代理商拒绝"，需要重新核对

**后置条件**：账单核对完成，进入待结算状态，可以进行结算处理

**异常流程**：

- 数据同步失败：系统报警，人工处理
- 计算异常：记录错误日志，暂停相关账单
- 核对超时：系统提醒，升级处理
- 争议处理：启动争议解决流程

---

### 3.4 UC004: 结算处理

**参与者**：财务人员、代理商（查询）
**前置条件**：存在"核对完成"状态的账单

**详细流程**：

**步骤1：财务选择待结算账单**

- **操作**：财务登录系统，进入"结算管理"→"待结算列表"
- **界面**：待结算列表显示：
  - 账单列表：编号、代理商、金额、确认时间
  - 批量选择功能（复选框）
  - 筛选条件：代理商、金额范围、时间范围
  - 汇总信息：选中账单数量、总金额
- **选择示例**：
  ```
  选中账单：
  ☑ BILL202401-AG001  润泽科技    ¥28,375
  ☑ BILL202401-AG002  海外贸易    ¥15,620
  ☑ BILL202401-AG003  跨境电商    ¥42,890

  汇总：3个账单，总金额：¥86,885
  ```
- **效果**：选中的账单高亮显示，底部显示汇总信息

**步骤2：生成结算单**

- **操作**：点击"生成结算单"按钮
- **界面**：结算单生成页面：
  - 结算单编号（自动生成）
  - 结算日期（默认当日，可修改）
  - 包含的账单列表
  - 结算方式选择（银行转账/支票/其他）
  - 备注信息输入
- **结算单示例**：
  ```
  结算单编号：STL20240129001
  结算日期：2024-01-29
  结算方式：银行转账

  包含账单：
  1. BILL202401-AG001  ¥28,375
  2. BILL202401-AG002  ¥15,620
  3. BILL202401-AG003  ¥42,890

  结算总额：¥86,885
  手续费：¥15 (转账费)
  实际出款：¥86,870
  ```
- **效果**：
  - 结算单状态为"待结算"
  - 包含的账单状态变为"结算中"
  - 生成结算单PDF文件

**步骤3：执行线下出款**

- **操作**：财务人员根据结算单进行银行转账操作
- **线下流程**：
  - 登录网银系统
  - 根据代理商银行信息创建转账指令
  - 输入转账金额和备注
  - 提交转账申请
  - 等待银行处理完成
- **银行操作示例**：
  ```
  收款人：润泽科技有限公司
  收款账号：6228480402564890018
  开户行：招商银行深圳分行
  转账金额：¥28,375
  用途：代理商分佣结算 STL20240129001
  ```
- **效果**：银行转账成功，获得转账凭证号

**步骤4：更新结算完成状态**

- **操作**：财务在系统中更新结算状态
- **界面**：结算确认页面：
  - 结算单信息显示
  - 转账凭证号输入
  - 实际转账时间选择
  - 转账凭证附件上传
  - "确认完成"按钮
- **更新信息**：
  ```
  转账凭证号：20240129156789
  转账时间：2024-01-29 15:30:00
  转账状态：成功
  转账金额：¥86,870
  ```
- **效果**：
  - 结算单状态变为"结算完成"
  - 包含的账单状态变为"已结算"
  - 系统发送结算通知给代理商
  - 生成结算完成报告

**步骤5：代理商查询结算结果**

- **操作**：代理商登录系统查看结算状态
- **界面**：结算查询页面显示：
  - 结算历史记录
  - 结算单详情
  - 转账凭证信息
  - 到账状态确认
- **通知方式**：
  - 系统内消息通知
  - 邮件通知
  - 短信通知（可选）
- **效果**：代理商确认收到款项，结算流程完成

**后置条件**：结算完成，代理商收到佣金，财务记录完整

**异常流程**：

- 转账失败：记录失败原因，重新发起转账
- 金额不符：核查原因，调整后重新结算
- 代理商账户异常：联系代理商确认账户信息
- 银行系统故障：等待恢复后继续处理

**业务指标**：

- 结算及时性：账单确认后3个工作日内完成结算
- 结算准确性：99.9%的结算金额准确无误
- 异常处理：异常情况24小时内处理完成

## 4. 验收标准

### 4.1 代理商签约管理

- [ ] 支持代理商基本信息的完整录入
- [ ] 支持代理商激活状态的正确切换
- [ ] 支持签约状态的自动化管理
- [ ] 签约开始/结束日期的自动状态更新

### 4.2 政策配置管理

- [ ] 支持产品底价配置和审核流程
- [ ] 支持固定比例分佣配置和审核流程
- [ ] 支持汇率返点配置和审核流程
- [ ] 工单审核状态的正确流转

### 4.3 分佣计算

- [ ] 每日自动同步交易数据
- [ ] 根据政策配置正确计算分佣
- [ ] 使用实时汇率进行币种转换
- [ ] 生成准确的分佣记录

### 4.4 账单管理

- [ ] 自动生成分佣账单
- [ ] 支持财务核对功能
- [ ] 支持代理商确认/拒绝功能
- [ ] 账单状态的正确流转

### 4.5 结算管理

- [ ] 支持批量选择待结算账单
- [ ] 支持结算单生成功能
- [ ] 支持结算状态更新
- [ ] 结算流程的完整性

### 4.6 系统性能

- [ ] 分佣计算的准确性和及时性
- [ ] 状态流转的实时性
- [ ] 数据同步的可靠性
- [ ] 用户操作的响应性能

### 4.7 数据安全

- [ ] 分佣数据的准确性
- [ ] 账单数据的完整性
- [ ] 结算记录的可追溯性
- [ ] 用户权限的正确控制
