<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPL运营后台 - 资金账户管理系统</title>
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 主布局容器 */
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 左侧边栏 */
        .sidebar {
            width: 260px;
            background: linear-gradient(180deg, #28a745 0%, #20c997 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .sidebar.collapsed {
            width: 70px;
        }

        /* 侧边栏头部 */
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 20px 10px;
            justify-content: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
        }

        .logo-text {
            font-size: 18px;
            font-weight: 600;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .logo-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        /* 用户信息 */
        .user-info {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidebar.collapsed .user-info {
            padding: 15px 10px;
            justify-content: center;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .user-details {
            flex: 1;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .user-details {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .user-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .user-role {
            font-size: 12px;
            opacity: 0.7;
        }

        /* 导航菜单 */
        .nav-menu {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 4px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s ease;
            position: relative;
        }

        .sidebar.collapsed .nav-link {
            padding: 12px 20px;
            justify-content: center;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border-right: 3px solid #28a745;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .sidebar.collapsed .nav-icon {
            margin-right: 0;
        }

        .nav-text {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .nav-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .nav-arrow {
            font-size: 12px;
            transition: transform 0.2s ease, opacity 0.3s ease;
        }

        .sidebar.collapsed .nav-arrow {
            opacity: 0;
        }

        /* 子菜单 */
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.1);
        }

        .nav-submenu.open {
            max-height: 300px;
        }

        .nav-submenu .nav-link {
            padding: 10px 20px 10px 52px;
            font-size: 13px;
        }

        .sidebar.collapsed .nav-submenu .nav-link {
            padding: 10px 20px;
        }

        .nav-submenu .nav-link:before {
            content: '•';
            margin-right: 8px;
            opacity: 0.6;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 260px;
            transition: margin-left 0.3s ease;
            min-height: 100vh;
        }

        .sidebar.collapsed + .main-content {
            margin-left: 70px;
        }

        /* 顶部导航栏 */
        .top-navbar {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
        }

        .top-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        /* 内容区域 */
        .content-area {
            padding: 30px;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .stat-icon.primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .stat-icon.success {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }

        .stat-icon.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .stat-icon.info {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
            margin-bottom: 12px;
        }

        .stat-change {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .stat-change.positive {
            background: #d4edda;
            color: #155724;
        }

        .stat-change.negative {
            background: #f8d7da;
            color: #721c24;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            margin-bottom: 24px;
            overflow: hidden;
        }

        .card-header {
            padding: 20px 24px;
            border-bottom: 1px solid #eee;
            background: #fafbfc;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .card-body {
            padding: 24px;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 页面内容隐藏/显示 */
        .page-content {
            display: none;
        }

        .page-content.active {
            display: block;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                width: 260px;
                transform: translateX(-100%);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar.collapsed + .main-content {
                margin-left: 0;
            }

            .content-area {
                padding: 20px 15px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧边栏 -->
        <div class="sidebar" id="sidebar">
            <!-- 侧边栏头部 -->
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon">I</div>
                    <div class="logo-text">IPL Admin</div>
                </div>
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <span>‹</span>
                </button>
            </div>
            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="#" class="nav-link active" onclick="showPage('dashboard')">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">账户概览</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                        <span class="nav-icon">🏦</span>
                        <span class="nav-text">EX 聚合收款</span>
                        <span class="nav-arrow">▼</span>
                    </a>
                    <div class="nav-submenu">
                        <!-- 租户查询 -->
                        <div class="nav-item">
                            <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                                <span class="nav-text">机构账户管理</span>
                                <span class="nav-arrow">▼</span>
                            </a>
                            <div class="nav-submenu">
                                <a href="#" class="nav-link" onclick="showPage('institution-info')">
                                    <span class="nav-text">机构信息查询</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('institution-accounts')">
                                    <span class="nav-text">机构账户管理</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('institution-transactions')">
                                    <span class="nav-text">机构交易记录</span>
                                </a>
                            </div>
                        </div>

                        <!-- 子商户 -->
                        <div class="nav-item">
                            <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                                <span class="nav-text">机构子商户</span>
                                <span class="nav-arrow">▼</span>
                            </a>
                            <div class="nav-submenu">
                                <a href="#" class="nav-link" onclick="showPage('sub-merchant-info')">
                                    <span class="nav-text">商户信息查询</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('sub-merchant-accounts')">
                                    <span class="nav-text">商户账户管理</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('sub-merchant-transactions')">
                                    <span class="nav-text">商户交易记录</span>
                                </a>
                            </div>
                        </div>

                        <!-- EX代理账户 -->
                        <div class="nav-item">
                            <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                                <span class="nav-text">EX代理账户</span>
                                <span class="nav-arrow">▼</span>
                            </a>
                            <div class="nav-submenu">
                                <a href="#" class="nav-link" onclick="showPage('ex-bill-records')">
                                    <span class="nav-text">EX账单记录</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('ex-transaction-details')">
                                    <span class="nav-text">EX交易明细</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('ex-settlement-records')">
                                    <span class="nav-text">结算记录</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showPage('reports')">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">报表分析</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showPage('settings')">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">系统设置</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="top-navbar">
                <h1 class="page-title" id="pageTitle">账户概览</h1>
                <div class="top-actions">
                    <button class="btn btn-primary btn-sm">
                        🔄 实时刷新
                    </button>
                    <button class="btn btn-primary btn-sm">
                        📤 导出数据
                    </button>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 账户概览页面 -->
                <div id="dashboard" class="page-content active">
                    <!-- 总体统计 -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon primary">💰</div>
                            </div>
                            <div class="stat-value">$45.2M</div>
                            <div class="stat-label">总资金池</div>
                            <div class="stat-change positive">+5.2%</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon success">🏦</div>
                            </div>
                            <div class="stat-value">156</div>
                            <div class="stat-label">PSP主账户数</div>
                            <div class="stat-change positive">+3</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon warning">🏪</div>
                            </div>
                            <div class="stat-value">2,847</div>
                            <div class="stat-label">商户子账户数</div>
                            <div class="stat-change positive">+45</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon info">🤝</div>
                            </div>
                            <div class="stat-value">12</div>
                            <div class="stat-label">EX代理账户数</div>
                            <div class="stat-change positive">+1</div>
                        </div>
                    </div>

                    <!-- 今日交易概览 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">今日交易概览</h3>
                            <button class="btn btn-primary btn-sm">实时刷新</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">交易类型</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">交易笔数</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">交易金额</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">成功率</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">平均金额</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">收款入账</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">1,245</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$890,560</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">99.2%</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$715</td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">资金归集</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">456</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$1,234,890</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">100%</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$2,708</td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">代扣费用</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">89</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$45,670</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">98.9%</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$513</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 机构商户信息查询页面 -->
                <div id="institution-info" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">机构ID</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入机构ID">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">机构名称</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入机构名称">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">机构状态</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部状态</option>
                                    <option value="active">活跃</option>
                                    <option value="frozen">冻结</option>
                                    <option value="suspended">暂停</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出信息</button>
                            </div>
                        </div>
                    </div>

                    <!-- 机构商户信息列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">机构商户信息</h3>
                            <button class="btn btn-primary btn-sm">新增机构</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">机构ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">机构名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">业务类型</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">注册时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">联系人</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">联系方式</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">主账户数</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">支付服务</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2023-01-15</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">张经理</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">+1-555-0123</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">3</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">活跃</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Beta Finance</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">金融科技</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2023-03-22</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">李总监</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">+1-555-0456</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">活跃</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Gamma Pay</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">移动支付</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2023-06-10</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">王主管</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">+1-555-0789</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">1</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #f8d7da; color: #721c24;">暂停</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 机构主账户页面 -->
                <div id="institution-accounts" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">机构ID</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入机构ID">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">账户状态</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部状态</option>
                                    <option value="active">活跃</option>
                                    <option value="frozen">冻结</option>
                                    <option value="suspended">暂停</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">余额范围</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部范围</option>
                                    <option value="0-10000">$0 - $10K</option>
                                    <option value="10000-100000">$10K - $100K</option>
                                    <option value="100000+">$100K+</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出列表</button>
                            </div>
                        </div>
                    </div>

                    <!-- 机构主账户列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">机构主账户列表</h3>
                            <button class="btn btn-primary btn-sm">导出列表</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">机构ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">机构名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">主账户ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账户余额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">可用余额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">冻结余额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">子账户数</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr style="transition: background 0.2s ease;">
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MAIN_PSP001_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$2,456,780</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$2,356,780</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$100,000</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">245</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">活跃</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">管理</button>
                                            </td>
                                        </tr>
                                        <tr style="transition: background 0.2s ease;">
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Beta Finance</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MAIN_PSP002_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$1,789,450</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$1,789,450</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$0</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">156</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">活跃</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">管理</button>
                                            </td>
                                        </tr>
                                        <tr style="transition: background 0.2s ease;">
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Gamma Pay</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MAIN_PSP003_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$890,560</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$840,560</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$50,000</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">89</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #f8d7da; color: #721c24;">冻结</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">管理</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主账户交易记录页面 -->
                <div id="institution-transactions" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">机构ID</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部机构</option>
                                    <option value="PSP_001">Alpha Payment</option>
                                    <option value="PSP_002">Beta Finance</option>
                                    <option value="PSP_003">Gamma Pay</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">交易类型</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部类型</option>
                                    <option value="deposit">归集入账</option>
                                    <option value="fee">代扣费用</option>
                                    <option value="withdraw">提现转账</option>
                                    <option value="transfer">内部转账</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 150px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">交易时间</label>
                                <input type="date" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" value="2024-12-20">
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出记录</button>
                            </div>
                        </div>
                    </div>

                    <!-- 主账户交易记录表格 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">主账户交易记录</h3>
                            <button class="btn btn-primary btn-sm">查看全部</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">机构ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易类型</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易金额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">余额变动</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TXN_20241220_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">归集入账</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$45,670</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">+$45,670</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 14:30</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">成功</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TXN_20241220_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">代扣费用</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$2,340</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">-$2,340</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 15:00</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">成功</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TXN_20241220_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">提现转账</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$100,000</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">-$100,000</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 16:15</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">处理中</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 子商户信息查询页面 -->
                <div id="sub-merchant-info" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">商户ID</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入商户ID">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">商户名称</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入商户名称">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">所属机构</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部机构</option>
                                    <option value="PSP_001">Alpha Payment</option>
                                    <option value="PSP_002">Beta Finance</option>
                                    <option value="PSP_003">Gamma Pay</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">商户状态</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部状态</option>
                                    <option value="active">活跃</option>
                                    <option value="frozen">冻结</option>
                                    <option value="suspended">暂停</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出信息</button>
                            </div>
                        </div>
                    </div>

                    <!-- 子商户信息列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">子商户信息</h3>
                            <button class="btn btn-primary btn-sm">新增商户</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">所属机构</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">业务类型</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">注册时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">联系人</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">联系方式</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">VA账户数</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MERCHANT_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">电子商务</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-01-15</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">张总</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">+86-138-0000-1234</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">3</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">活跃</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MERCHANT_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">国际贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-02-20</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">李经理</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">+86-139-0000-5678</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">活跃</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MERCHANT_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">DEF科技</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Beta Finance</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">软件服务</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-03-10</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">王总监</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">+86-150-0000-9012</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">5</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #f8d7da; color: #721c24;">冻结</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                </div>

                <!-- 子账户管理页面 -->
                <div id="sub-merchant-accounts" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">商户ID</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入商户ID">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">所属机构</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部机构</option>
                                    <option value="PSP_001">Alpha Payment</option>
                                    <option value="PSP_002">Beta Finance</option>
                                    <option value="PSP_003">Gamma Pay</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">账户状态</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部状态</option>
                                    <option value="active">活跃</option>
                                    <option value="frozen">冻结</option>
                                    <option value="suspended">暂停</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出列表</button>
                            </div>
                        </div>
                    </div>

                    <!-- 子账户管理列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">子账户管理列表</h3>
                            <button class="btn btn-primary btn-sm">导出列表</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">子账户ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">所属机构</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账户余额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">今日收款</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">VA账户数</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MERCHANT_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">SUB_MERCHANT001_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$12,450</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$3,670</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">3</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">活跃</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">管理</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MERCHANT_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">SUB_MERCHANT002_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$8,920</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$1,240</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">活跃</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">管理</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MERCHANT_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">DEF科技</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">SUB_MERCHANT003_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Beta Finance</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$25,680</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$5,890</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">5</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #f8d7da; color: #721c24;">冻结</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">管理</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 子商户交易记录页面 -->
                <div id="sub-merchant-transactions" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">商户ID</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入商户ID">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">交易类型</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部类型</option>
                                    <option value="receive">收款入账</option>
                                    <option value="transfer">归集转出</option>
                                    <option value="fee">费用扣除</option>
                                    <option value="refund">退款处理</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">交易状态</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部状态</option>
                                    <option value="success">成功</option>
                                    <option value="pending">处理中</option>
                                    <option value="failed">失败</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 150px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">交易时间</label>
                                <input type="date" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" value="2024-12-20">
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出记录</button>
                            </div>
                        </div>
                    </div>

                    <!-- 子商户交易记录表格 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">子商户交易记录</h3>
                            <button class="btn btn-primary btn-sm">查看全部</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">子账户ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易类型</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易金额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">手续费</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">净入账</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TXN_20241220_101</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MERCHANT_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">SUB_MERCHANT001_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">收款入账</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$1,000</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$3</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$997</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 10:15</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">成功</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TXN_20241220_102</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MERCHANT_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">SUB_MERCHANT001_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">归集转出</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$5,000</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$0</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">-$5,000</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 14:30</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">成功</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TXN_20241220_103</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MERCHANT_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">SUB_MERCHANT002_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">收款入账</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$2,500</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$7.5</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$2,492.5</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 11:45</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">成功</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="ex-accounts" class="page-content">
                    <!-- EX代理账户统计 -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon primary">💰</div>
                            </div>
                            <div class="stat-value">$890K</div>
                            <div class="stat-label">代理账户总余额</div>
                            <div class="stat-change positive">+12.3%</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon success">📈</div>
                            </div>
                            <div class="stat-value">$45K</div>
                            <div class="stat-label">今日代收费用</div>
                            <div class="stat-change positive">+8.7%</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon warning">💸</div>
                            </div>
                            <div class="stat-value">$38K</div>
                            <div class="stat-label">今日结算金额</div>
                            <div class="stat-change positive">+15.2%</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon info">🤝</div>
                            </div>
                            <div class="stat-value">11</div>
                            <div class="stat-label">活跃代理账户</div>
                            <div class="stat-change positive">+0</div>
                        </div>
                    </div>

                    <!-- EX代理账户列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">EX代理账户列表</h3>
                            <button class="btn btn-primary btn-sm">导出列表</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">EX ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">EX公司名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">代理账户ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账户余额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">代收余额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务PSP数</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">本月代收</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">EX_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Tech Solutions</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">AGENT_EX001_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$456,780</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$23,450</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">45</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$234,560</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">活跃</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">管理</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">EX_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Beta Payment Tech</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">AGENT_EX002_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$234,560</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$12,340</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">28</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$156,780</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">活跃</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">管理</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">EX_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Gamma Fintech</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">AGENT_EX003_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$123,450</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$8,900</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">18</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$89,670</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">活跃</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">管理</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- EX代理账户交易记录 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">代理账户交易记录</h3>
                            <button class="btn btn-primary btn-sm">查看全部</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">EX ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易类型</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易金额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">关联PSP</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TXN_20241220_201</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">EX_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">代收费用</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$2,340</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 15:00</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">成功</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TXN_20241220_202</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">EX_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">返点结算</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$1,560</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 16:30</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">成功</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TXN_20241220_203</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">EX_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">IPL返点</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$890</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">-</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 18:00</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">处理中</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="reports" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">报表分析</h3>
                        </div>
                        <div class="card-body">
                            <p>报表分析页面内容...</p>
                        </div>
                    </div>
                </div>

                <div id="settings" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">系统设置</h3>
                        </div>
                        <div class="card-body">
                            <p>系统设置页面内容...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 侧边栏折叠功能
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('collapsed');
        }

        // 子菜单展开/折叠
        function toggleSubmenu(element) {
            const submenu = element.nextElementSibling;
            const arrow = element.querySelector('.nav-arrow');

            if (submenu && submenu.classList.contains('nav-submenu')) {
                submenu.classList.toggle('open');
                if (arrow) {
                    arrow.style.transform = submenu.classList.contains('open') ? 'rotate(180deg)' : 'rotate(0deg)';
                }
            }
        }

        // 页面切换功能
        function showPage(pageId) {
            // 隐藏所有页面内容
            const allPages = document.querySelectorAll('.page-content');
            allPages.forEach(page => {
                page.classList.remove('active');
            });

            // 显示选中的页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }

            // 更新导航状态
            const allNavLinks = document.querySelectorAll('.nav-link');
            allNavLinks.forEach(link => {
                link.classList.remove('active');
            });

            // 设置当前页面的导航为活跃状态
            event.target.classList.add('active');

            // 更新页面标题
            const pageTitles = {
                'dashboard': '账户概览',
                'institution-info': '机构商户信息查询',
                'institution-accounts': '机构主账户',
                'institution-transactions': '主账户交易记录',
                'sub-merchant-info': '子商户信息查询',
                'sub-merchant-accounts': '子账户管理',
                'sub-merchant-transactions': '子商户交易记录',
                'ex-accounts': 'EX代理账户',
                'reports': '报表分析',
                'settings': '系统设置'
            };

            const pageTitle = document.getElementById('pageTitle');
            if (pageTitle && pageTitles[pageId]) {
                pageTitle.textContent = pageTitles[pageId];
            }
        }
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('IPL运营后台加载完成');
            // 检查屏幕尺寸，在移动端自动折叠侧边栏
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                sidebar.classList.add('collapsed');
            }
        });
    </script>
</body>
</html>
