<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EX运营后台 - 综合管理平台</title>
    <style>
        /* 通用样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            text-align: center;
            font-size: 28px;
            font-weight: 600;
        }

        .header .subtitle {
            text-align: center;
            font-size: 16px;
            opacity: 0.9;
            margin-top: 8px;
        }

        /* 导航栏 */
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            background: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-right: 1px solid #eee;
        }

        .nav-tab:last-child {
            border-right: none;
        }

        .nav-tab.active {
            background: #6f42c1;
            color: white;
        }

        .nav-tab:hover:not(.active) {
            background: #f8f9fa;
            color: #333;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            text-align: center;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #6f42c1;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .stat-change {
            font-size: 12px;
            margin-top: 8px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .stat-change.positive {
            background: #d4edda;
            color: #155724;
        }

        .stat-change.negative {
            background: #f8d7da;
            color: #721c24;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            margin-bottom: 24px;
            overflow: hidden;
        }

        .card-header {
            padding: 20px 24px;
            border-bottom: 1px solid #eee;
            background: #fafbfc;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .card-body {
            padding: 24px;
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8f9fa;
            padding: 16px 20px;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #dee2e6;
            font-size: 14px;
        }

        .table td {
            padding: 16px 20px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        /* 状态标签 */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .status-processing {
            background: #cce7ff;
            color: #004085;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #6f42c1;
            color: white;
        }

        .btn-primary:hover {
            background: #5a359a;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 搜索筛选 */
        .search-filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }

        .form-input {
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #6f42c1;
            box-shadow: 0 0 0 3px rgba(111, 66, 193, 0.1);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }

            .form-group {
                min-width: auto;
            }

            .nav-tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>EX运营后台</h1>
            <div class="subtitle">聚合收款科技公司综合管理平台 | EX ID: EX_001</div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">业务概览</button>
            <button class="nav-tab" onclick="showTab('transactions')">交易查询</button>
            <button class="nav-tab" onclick="showTab('ipl-bills')">IPL账单</button>
            <button class="nav-tab" onclick="showTab('psp-bills')">PSP账单</button>
            <button class="nav-tab" onclick="showTab('settlements')">结算管理</button>
        </div>

        <!-- 业务概览 -->
        <div id="overview" class="tab-content active">
            <!-- 核心业务指标 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">45</div>
                    <div class="stat-label">服务PSP数量</div>
                    <div class="stat-change positive">+3</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">2,847</div>
                    <div class="stat-label">覆盖商户数</div>
                    <div class="stat-change positive">+156</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$39.2M</div>
                    <div class="stat-label">本月交易总额</div>
                    <div class="stat-change positive">+18.5%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$234K</div>
                    <div class="stat-label">本月总收入</div>
                    <div class="stat-change positive">+22.3%</div>
                </div>
            </div>

            <!-- 收入构成分析 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">收入构成分析</h3>
                    <button class="btn btn-primary btn-sm">导出报告</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>收入类型</th>
                                    <th>本月金额</th>
                                    <th>上月金额</th>
                                    <th>环比增长</th>
                                    <th>占比</th>
                                    <th>趋势</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Setup Fee</td>
                                    <td>$8,000</td>
                                    <td>$6,500</td>
                                    <td>+23.1%</td>
                                    <td>3.4%</td>
                                    <td><span class="status-badge status-success">上升</span></td>
                                </tr>
                                <tr>
                                    <td>科技服务费</td>
                                    <td>$45,000</td>
                                    <td>$42,000</td>
                                    <td>+7.1%</td>
                                    <td>19.2%</td>
                                    <td><span class="status-badge status-success">上升</span></td>
                                </tr>
                                <tr>
                                    <td>交易处理费</td>
                                    <td>$118,680</td>
                                    <td>$98,750</td>
                                    <td>+20.2%</td>
                                    <td>50.7%</td>
                                    <td><span class="status-badge status-success">上升</span></td>
                                </tr>
                                <tr>
                                    <td>IPL返点收入</td>
                                    <td>$62,520</td>
                                    <td>$58,900</td>
                                    <td>+6.1%</td>
                                    <td>26.7%</td>
                                    <td><span class="status-badge status-success">上升</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- TOP PSP客户 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">TOP PSP客户</h3>
                    <button class="btn btn-primary btn-sm">查看全部</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>PSP名称</th>
                                    <th>商户数</th>
                                    <th>本月交易额</th>
                                    <th>贡献收入</th>
                                    <th>增长率</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>Alpha Payment</td>
                                    <td>245</td>
                                    <td>$15.6M</td>
                                    <td>$93,600</td>
                                    <td>+25.3%</td>
                                    <td><span class="status-badge status-success">活跃</span></td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>Beta Finance</td>
                                    <td>156</td>
                                    <td>$12.3M</td>
                                    <td>$73,800</td>
                                    <td>+18.7%</td>
                                    <td><span class="status-badge status-success">活跃</span></td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>Gamma Pay</td>
                                    <td>89</td>
                                    <td>$8.9M</td>
                                    <td>$53,400</td>
                                    <td>+12.1%</td>
                                    <td><span class="status-badge status-success">活跃</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 交易查询 -->
        <div id="transactions" class="tab-content">
            <!-- 搜索筛选 -->
            <div class="search-filters">
                <div class="filter-row">
                    <div class="form-group">
                        <label class="form-label">PSP机构</label>
                        <select class="form-input">
                            <option value="">全部PSP</option>
                            <option value="PSP_001">Alpha Payment</option>
                            <option value="PSP_002">Beta Finance</option>
                            <option value="PSP_003">Gamma Pay</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">商户ID</label>
                        <input type="text" class="form-input" placeholder="输入商户ID">
                    </div>
                    <div class="form-group">
                        <label class="form-label">交易类型</label>
                        <select class="form-input">
                            <option value="">全部类型</option>
                            <option value="payment">收款</option>
                            <option value="remittance">付款</option>
                            <option value="exchange">换汇</option>
                            <option value="settlement">结汇</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">交易时间</label>
                        <input type="date" class="form-input" value="2024-12-20">
                    </div>
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary">搜索</button>
                    </div>
                </div>
            </div>

            <!-- 交易统计 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">1,245</div>
                    <div class="stat-label">今日收款笔数</div>
                    <div class="stat-change positive">+156</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">234</div>
                    <div class="stat-label">今日付款笔数</div>
                    <div class="stat-change positive">+23</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">89</div>
                    <div class="stat-label">今日换汇笔数</div>
                    <div class="stat-change positive">+12</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">45</div>
                    <div class="stat-label">今日结汇笔数</div>
                    <td>+8</td>
                </div>
            </div>

            <!-- 交易明细列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">交易明细列表</h3>
                    <button class="btn btn-primary btn-sm">导出Excel</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>交易ID</th>
                                    <th>PSP机构</th>
                                    <th>商户名称</th>
                                    <th>交易类型</th>
                                    <th>交易金额</th>
                                    <th>币种</th>
                                    <th>EX费用</th>
                                    <th>交易时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>TXN_20241220_001</td>
                                    <td>Alpha Payment</td>
                                    <td>ABC电商</td>
                                    <td>收款</td>
                                    <td>$1,000</td>
                                    <td>USD</td>
                                    <td>$3.00</td>
                                    <td>2024-12-20 10:15</td>
                                    <td><span class="status-badge status-success">成功</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>TXN_20241220_002</td>
                                    <td>Beta Finance</td>
                                    <td>XYZ贸易</td>
                                    <td>付款</td>
                                    <td>$2,500</td>
                                    <td>USD</td>
                                    <td>$7.50</td>
                                    <td>2024-12-20 11:30</td>
                                    <td><span class="status-badge status-success">成功</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>TXN_20241220_003</td>
                                    <td>Gamma Pay</td>
                                    <td>DEF科技</td>
                                    <td>换汇</td>
                                    <td>¥50,000</td>
                                    <td>CNY→USD</td>
                                    <td>$21.00</td>
                                    <td>2024-12-20 14:45</td>
                                    <td><span class="status-badge status-processing">处理中</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>TXN_20241220_004</td>
                                    <td>Alpha Payment</td>
                                    <td>GHI进出口</td>
                                    <td>结汇</td>
                                    <td>€15,000</td>
                                    <td>EUR→USD</td>
                                    <td>$45.00</td>
                                    <td>2024-12-20 16:20</td>
                                    <td><span class="status-badge status-success">成功</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- IPL账单 -->
        <div id="ipl-bills" class="tab-content">
            <!-- IPL账单统计 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">$62,520</div>
                    <div class="stat-label">本月IPL返点</div>
                    <div class="stat-change positive">+6.1%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$58,900</div>
                    <div class="stat-label">上月IPL返点</div>
                    <div class="stat-change positive">+4.2%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">15.2%</div>
                    <div class="stat-label">平均返点率</div>
                    <div class="stat-change positive">+0.3%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$456,780</div>
                    <div class="stat-label">累计返点收入</div>
                    <div class="stat-change positive">+18.7%</div>
                </div>
            </div>

            <!-- 搜索筛选 -->
            <div class="search-filters">
                <div class="filter-row">
                    <div class="form-group">
                        <label class="form-label">账单期间</label>
                        <input type="month" class="form-input" value="2024-12">
                    </div>
                    <div class="form-group">
                        <label class="form-label">账单状态</label>
                        <select class="form-input">
                            <option value="">全部状态</option>
                            <option value="pending">待结算</option>
                            <option value="settled">已结算</option>
                            <option value="disputed">有争议</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">返点类型</label>
                        <select class="form-input">
                            <option value="">全部类型</option>
                            <option value="transaction">交易返点</option>
                            <option value="volume">业绩返点</option>
                            <option value="special">特殊返点</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary">搜索</button>
                    </div>
                </div>
            </div>

            <!-- IPL账单明细 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">EX与IPL账单明细</h3>
                    <button class="btn btn-primary btn-sm">导出账单</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>账单ID</th>
                                    <th>账单期间</th>
                                    <th>代理业绩</th>
                                    <th>返点率</th>
                                    <th>返点金额</th>
                                    <th>结算时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>IPL_BILL_202412_001</td>
                                    <td>2024-12-01 ~ 2024-12-31</td>
                                    <td>$39,200,000</td>
                                    <td>15.2%</td>
                                    <td>$62,520</td>
                                    <td>-</td>
                                    <td><span class="status-badge status-pending">待结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">查看</button></td>
                                </tr>
                                <tr>
                                    <td>IPL_BILL_202411_001</td>
                                    <td>2024-11-01 ~ 2024-11-30</td>
                                    <td>$36,800,000</td>
                                    <td>15.0%</td>
                                    <td>$58,900</td>
                                    <td>2024-12-05 14:00</td>
                                    <td><span class="status-badge status-success">已结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">查看</button></td>
                                </tr>
                                <tr>
                                    <td>IPL_BILL_202410_001</td>
                                    <td>2024-10-01 ~ 2024-10-31</td>
                                    <td>$32,500,000</td>
                                    <td>14.8%</td>
                                    <td>$52,340</td>
                                    <td>2024-11-05 14:00</td>
                                    <td><span class="status-badge status-success">已结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">查看</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- IPL结算记录 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">IPL结算记录</h3>
                    <button class="btn btn-primary btn-sm">查看全部</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>结算ID</th>
                                    <th>结算类型</th>
                                    <th>结算金额</th>
                                    <th>结算方式</th>
                                    <th>结算时间</th>
                                    <th>关联账单</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>IPL_SETTLE_20241205_001</td>
                                    <td>代理返点</td>
                                    <td>$58,900</td>
                                    <td>银行转账</td>
                                    <td>2024-12-05 14:00</td>
                                    <td>IPL_BILL_202411_001</td>
                                    <td><span class="status-badge status-success">已完成</span></td>
                                </tr>
                                <tr>
                                    <td>IPL_SETTLE_20241105_001</td>
                                    <td>代理返点</td>
                                    <td>$52,340</td>
                                    <td>银行转账</td>
                                    <td>2024-11-05 14:00</td>
                                    <td>IPL_BILL_202410_001</td>
                                    <td><span class="status-badge status-success">已完成</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- PSP账单 -->
        <div id="psp-bills" class="tab-content">
            <!-- PSP账单统计 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">$171,680</div>
                    <div class="stat-label">本月PSP应付</div>
                    <div class="stat-change positive">+18.5%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$37,450</div>
                    <div class="stat-label">本月PSP返点</div>
                    <div class="stat-change positive">+12.3%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$134,230</div>
                    <div class="stat-label">本月净收入</div>
                    <div class="stat-change positive">+20.8%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">45</div>
                    <div class="stat-label">服务PSP数</div>
                    <div class="stat-change positive">+3</div>
                </div>
            </div>

            <!-- 搜索筛选 */
            <div class="search-filters">
                <div class="filter-row">
                    <div class="form-group">
                        <label class="form-label">PSP机构</label>
                        <select class="form-input">
                            <option value="">全部PSP</option>
                            <option value="PSP_001">Alpha Payment</option>
                            <option value="PSP_002">Beta Finance</option>
                            <option value="PSP_003">Gamma Pay</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">账单期间</label>
                        <input type="month" class="form-input" value="2024-12">
                    </div>
                    <div class="form-group">
                        <label class="form-label">账单状态</label>
                        <select class="form-input">
                            <option value="">全部状态</option>
                            <option value="pending">待结算</option>
                            <option value="settled">已结算</option>
                            <option value="disputed">有争议</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary">搜索</button>
                    </div>
                </div>
            </div>

            <!-- PSP账单明细 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">EX与PSP账单明细</h3>
                    <button class="btn btn-primary btn-sm">导出账单</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>账单ID</th>
                                    <th>PSP名称</th>
                                    <th>账单期间</th>
                                    <th>应收费用</th>
                                    <th>返点金额</th>
                                    <th>净收入</th>
                                    <th>结算状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>PSP_BILL_202412_001</td>
                                    <td>Alpha Payment</td>
                                    <td>2024-12-01 ~ 2024-12-31</td>
                                    <td>$93,600</td>
                                    <td>$18,720</td>
                                    <td>$74,880</td>
                                    <td><span class="status-badge status-pending">待结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">查看</button></td>
                                </tr>
                                <tr>
                                    <td>PSP_BILL_202412_002</td>
                                    <td>Beta Finance</td>
                                    <td>2024-12-01 ~ 2024-12-31</td>
                                    <td>$73,800</td>
                                    <td>$14,760</td>
                                    <td>$59,040</td>
                                    <td><span class="status-badge status-pending">待结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">查看</button></td>
                                </tr>
                                <tr>
                                    <td>PSP_BILL_202411_001</td>
                                    <td>Alpha Payment</td>
                                    <td>2024-11-01 ~ 2024-11-30</td>
                                    <td>$84,500</td>
                                    <td>$16,900</td>
                                    <td>$67,600</td>
                                    <td><span class="status-badge status-success">已结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">查看</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- PSP返点记录 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">PSP返点记录</h3>
                    <button class="btn btn-primary btn-sm">查看全部</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>返点ID</th>
                                    <th>PSP名称</th>
                                    <th>返点类型</th>
                                    <th>计算基础</th>
                                    <th>返点率</th>
                                    <th>返点金额</th>
                                    <th>结算时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>PSP_COMM_202412_001</td>
                                    <td>Alpha Payment</td>
                                    <td>交易返点</td>
                                    <td>$93,600</td>
                                    <td>20.0%</td>
                                    <td>$18,720</td>
                                    <td>-</td>
                                    <td><span class="status-badge status-pending">待结算</span></td>
                                </tr>
                                <tr>
                                    <td>PSP_COMM_202412_002</td>
                                    <td>Beta Finance</td>
                                    <td>交易返点</td>
                                    <td>$73,800</td>
                                    <td>20.0%</td>
                                    <td>$14,760</td>
                                    <td>-</td>
                                    <td><span class="status-badge status-pending">待结算</span></td>
                                </tr>
                                <tr>
                                    <td>PSP_COMM_202411_001</td>
                                    <td>Alpha Payment</td>
                                    <td>交易返点</td>
                                    <td>$84,500</td>
                                    <td>20.0%</td>
                                    <td>$16,900</td>
                                    <td>2024-12-05 16:00</td>
                                    <td><span class="status-badge status-success">已结算</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结算管理 -->
        <div id="settlements" class="tab-content">
            <!-- 结算统计 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">$234,560</div>
                    <div class="stat-label">本月总结算</div>
                    <div class="stat-change positive">+15.8%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">156</div>
                    <div class="stat-label">结算笔数</div>
                    <div class="stat-change positive">+23</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">98.5%</div>
                    <div class="stat-label">结算成功率</div>
                    <div class="stat-change positive">+0.8%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$1,504</div>
                    <div class="stat-label">平均结算金额</div>
                    <div class="stat-change negative">-3.2%</div>
                </div>
            </div>

            <!-- 搜索筛选 -->
            <div class="search-filters">
                <div class="filter-row">
                    <div class="form-group">
                        <label class="form-label">结算类型</label>
                        <select class="form-input">
                            <option value="">全部类型</option>
                            <option value="ipl_commission">IPL返点</option>
                            <option value="psp_commission">PSP返点</option>
                            <option value="fee_collection">费用代收</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">结算时间</label>
                        <input type="date" class="form-input" value="2024-12-01">
                    </div>
                    <div class="form-group">
                        <label class="form-label">至</label>
                        <input type="date" class="form-input" value="2024-12-31">
                    </div>
                    <div class="form-group">
                        <label class="form-label">结算状态</label>
                        <select class="form-input">
                            <option value="">全部状态</option>
                            <option value="pending">待结算</option>
                            <option value="processing">结算中</option>
                            <option value="completed">已完成</option>
                            <option value="failed">结算失败</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary">搜索</button>
                    </div>
                </div>
            </div>

            <!-- 待处理结算 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">待处理结算</h3>
                    <button class="btn btn-primary btn-sm">批量处理</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>选择</th>
                                    <th>结算ID</th>
                                    <th>结算类型</th>
                                    <th>对象</th>
                                    <th>结算金额</th>
                                    <th>创建时间</th>
                                    <th>优先级</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>SETTLE_20241231_001</td>
                                    <td>IPL返点</td>
                                    <td>IPL Payment</td>
                                    <td>$62,520</td>
                                    <td>2024-12-31 18:00</td>
                                    <td><span class="status-badge status-success">高</span></td>
                                    <td><span class="status-badge status-pending">待结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">处理</button></td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>SETTLE_20241231_002</td>
                                    <td>PSP返点</td>
                                    <td>Alpha Payment</td>
                                    <td>$18,720</td>
                                    <td>2024-12-31 18:30</td>
                                    <td><span class="status-badge status-pending">中</span></td>
                                    <td><span class="status-badge status-pending">待结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">处理</button></td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>SETTLE_20241231_003</td>
                                    <td>PSP返点</td>
                                    <td>Beta Finance</td>
                                    <td>$14,760</td>
                                    <td>2024-12-31 18:30</td>
                                    <td><span class="status-badge status-pending">中</span></td>
                                    <td><span class="status-badge status-pending">待结算</span></td>
                                    <td><button class="btn btn-primary btn-sm">处理</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 结算历史记录 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">结算历史记录</h3>
                    <button class="btn btn-primary btn-sm">导出记录</button>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>结算ID</th>
                                    <th>结算类型</th>
                                    <th>对象</th>
                                    <th>结算金额</th>
                                    <th>结算方式</th>
                                    <th>结算时间</th>
                                    <th>处理人</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>SETTLE_20241205_001</td>
                                    <td>IPL返点</td>
                                    <td>IPL Payment</td>
                                    <td>$58,900</td>
                                    <td>银行转账</td>
                                    <td>2024-12-05 14:00</td>
                                    <td>系统自动</td>
                                    <td><span class="status-badge status-success">已完成</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>SETTLE_20241205_002</td>
                                    <td>PSP返点</td>
                                    <td>Alpha Payment</td>
                                    <td>$16,900</td>
                                    <td>账户转账</td>
                                    <td>2024-12-05 16:00</td>
                                    <td>admin_001</td>
                                    <td><span class="status-badge status-success">已完成</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>SETTLE_20241201_001</td>
                                    <td>费用代收</td>
                                    <td>Alpha Payment</td>
                                    <td>$5,000</td>
                                    <td>自动代扣</td>
                                    <td>2024-12-01 09:00</td>
                                    <td>系统自动</td>
                                    <td><span class="status-badge status-success">已完成</span></td>
                                    <td><button class="btn btn-primary btn-sm">详情</button></td>
                                </tr>
                                <tr>
                                    <td>SETTLE_20241130_001</td>
                                    <td>PSP返点</td>
                                    <td>Beta Finance</td>
                                    <td>$12,450</td>
                                    <td>账户转账</td>
                                    <td>2024-11-30 17:30</td>
                                    <td>admin_002</td>
                                    <td><span class="status-badge status-failed">失败</span></td>
                                    <td><button class="btn btn-primary btn-sm">重试</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换功能
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的active状态
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页内容
            document.getElementById(tabName).classList.add('active');

            // 设置选中的标签为active状态
            event.target.classList.add('active');
        }

        // 批量选择功能
        function toggleSelectAll() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            const selectAllCheckbox = document.getElementById('selectAll');

            checkboxes.forEach(checkbox => {
                if (checkbox !== selectAllCheckbox) {
                    checkbox.checked = selectAllCheckbox.checked;
                }
            });
        }

        // 批量处理结算
        function batchProcess() {
            const selectedItems = [];
            const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');

            checkboxes.forEach(checkbox => {
                if (checkbox.id !== 'selectAll') {
                    selectedItems.push(checkbox.closest('tr'));
                }
            });

            if (selectedItems.length === 0) {
                alert('请选择要处理的结算项目');
                return;
            }

            if (confirm(`确定要批量处理 ${selectedItems.length} 个结算项目吗？`)) {
                console.log('批量处理结算:', selectedItems.length, '个项目');
                // 这里可以添加实际的批量处理逻辑
                alert('批量处理已提交，请稍后查看处理结果');
            }
        }

        // 模拟数据刷新
        function refreshData() {
            console.log('刷新数据...');
            // 这里可以添加AJAX请求来刷新数据
        }

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('EX运营后台加载完成');

            // 设置定时刷新
            setInterval(refreshData, 60000); // 每60秒刷新一次
        });
    </script>
</body>
</html>
