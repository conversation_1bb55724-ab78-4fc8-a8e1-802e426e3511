<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理商管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: #f0f2f5;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            padding: 20px 30px;
            border-bottom: 1px solid #e8e8e8;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .breadcrumb {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            margin-bottom: 8px;
        }

        .breadcrumb a {
            color: #1890ff;
            text-decoration: none;
        }

        .page-title {
            font-size: 24px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: 600;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
        }

        /* 主要内容区域 */
        .main-content {
            display: flex;
            min-height: calc(100vh - 100px);
        }

        /* 左侧菜单 */
        .sidebar {
            width: 250px;
            background: #fff;
            border-right: 1px solid #e8e8e8;
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s;
            position: relative;
        }

        .menu-item:hover {
            background: #f5f5f5;
        }

        .menu-item.active {
            background: #e6f7ff;
            border-right: 3px solid #1890ff;
            color: #1890ff;
        }

        .menu-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .menu-desc {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
        }

        /* 右侧内容区域 */
        .content-area {
            flex: 1;
            padding: 24px;
            background: #fafafa;
        }

        .content-section {
            display: none;
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .content-section.active {
            display: block;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid #1890ff;
        }

        /* Tab 样式 */
        .tab-container {
            margin-bottom: 24px;
        }

        .tab-nav {
            display: flex;
            border-bottom: 1px solid #e8e8e8;
            margin-bottom: 20px;
        }

        .tab-item {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
        }

        .tab-item:hover {
            color: #1890ff;
        }

        .tab-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            background: #f0f8ff;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 表单样式 */
        .form-section {
            margin-bottom: 32px;
        }

        .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
            align-items: center;
        }

        .form-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-item label {
            min-width: 120px;
            text-align: right;
            color: rgba(0, 0, 0, 0.85);
        }

        .form-item input, .form-item select, .form-item textarea {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-item input:focus, .form-item select:focus, .form-item textarea:focus {
            outline: none;
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            border: none;
            font-size: 14px;
            transition: all 0.3s;
        }

        .btn-primary {
            background-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: rgba(0, 0, 0, 0.65);
            border: 1px solid #d9d9d9;
        }

        .btn-secondary:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }

        /* 表格样式 */
        .table-container {
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            overflow: hidden;
        }

        .table-header {
            background: #fafafa;
            padding: 12px 16px;
            border-bottom: 1px solid #e8e8e8;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
        }

        .table-row {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .table-row:last-child {
            border-bottom: none;
        }

        .table-row:hover {
            background: #fafafa;
        }

        /* 单选框样式 */
        .radio-group {
            display: flex;
            gap: 24px;
        }

        .radio-item {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            transition: all 0.3s;
            background: #fff;
        }

        .radio-item:hover {
            border-color: #40a9ff;
            background: #f0f8ff;
        }

        .radio-item input[type="radio"] {
            margin-right: 8px;
            accent-color: #1890ff;
        }

        .radio-item:has(input[type="radio"]:checked) {
            border-color: #1890ff;
            background: #e6f7ff;
        }

        .radio-text {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid #e8e8e8;
            }
            
            .content-area {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="breadcrumb">
                <a href="#">PSP管理</a> / 代理商管理
            </div>
            <div class="page-title">代理商管理</div>
            <div class="page-subtitle">管理代理商签约信息、交易分佣、汇率返点和代理政策</div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 左侧菜单 -->
            <div class="sidebar">
                <div class="menu-item active" onclick="showSection('contract')">
                    <div class="menu-title">代理商签约信息</div>
                    <div class="menu-desc">管理代理商基本信息和签约状态</div>
                </div>
                <div class="menu-item" onclick="showSection('commission')">
                    <div class="menu-title">交易分佣管理</div>
                    <div class="menu-desc">配置产品底价和固定比例分佣</div>
                </div>
                <div class="menu-item" onclick="showSection('exchange')">
                    <div class="menu-title">汇率返点管理</div>
                    <div class="menu-desc">管理底价汇率和固定返点</div>
                </div>
                <div class="menu-item" onclick="showSection('policy')">
                    <div class="menu-title">代理政策管理</div>
                    <div class="menu-desc">政策查询、分配和管理</div>
                </div>
            </div>

            <!-- 右侧内容区域 -->
            <div class="content-area">
                <!-- 代理商签约信息 -->
                <div id="contract-section" class="content-section active">
                    <div class="section-title">代理商签约信息</div>
                    
                    <div class="form-section">
                        <div class="form-row">
                            <div class="form-item">
                                <label>代理商名称：</label>
                                <input type="text" placeholder="请输入代理商名称" style="width: 300px;">
                            </div>
                            <div class="form-item">
                                <label>代理商等级：</label>
                                <select style="width: 150px;">
                                    <option value="">请选择等级</option>
                                    <option value="A">A级代理商</option>
                                    <option value="B">B级代理商</option>
                                    <option value="C">C级代理商</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-item">
                                <label>联系人：</label>
                                <input type="text" placeholder="请输入联系人姓名" style="width: 200px;">
                            </div>
                            <div class="form-item">
                                <label>联系电话：</label>
                                <input type="tel" placeholder="请输入联系电话" style="width: 200px;">
                            </div>
                            <div class="form-item">
                                <label>邮箱：</label>
                                <input type="email" placeholder="请输入邮箱地址" style="width: 250px;">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-item">
                                <label>签约状态：</label>
                                <div class="radio-group">
                                    <label class="radio-item">
                                        <input type="radio" name="contract-status" value="draft">
                                        <span class="radio-text">草稿</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="contract-status" value="pending">
                                        <span class="radio-text">待审核</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="contract-status" value="active">
                                        <span class="radio-text">已生效</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-item">
                                <label>备注说明：</label>
                                <textarea placeholder="请输入备注说明..." style="width: 500px; height: 80px;"></textarea>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <button class="btn btn-primary">保存</button>
                            <button class="btn btn-secondary">重置</button>
                        </div>
                    </div>
                </div>

                <!-- 交易分佣管理 -->
                <div id="commission-section" class="content-section">
                    <div class="section-title">交易分佣管理</div>

                    <div class="tab-container">
                        <div class="tab-nav">
                            <div class="tab-item active" onclick="showTab('commission', 'product')">产品底价</div>
                            <div class="tab-item" onclick="showTab('commission', 'ratio')">固定比例</div>
                        </div>

                        <!-- 产品底价 Tab -->
                        <div id="commission-product-tab" class="tab-content active">
                            <div class="form-section">
                                <div class="form-row">
                                    <div class="form-item">
                                        <label>分佣简介：</label>
                                        <input type="text" placeholder="简介" style="width: 400px;">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-item">
                                        <label>产品名称：</label>
                                        <select style="width: 200px;">
                                            <option value="">请选择产品</option>
                                            <option value="B2C-电商收款">B2C-电商收款</option>
                                            <option value="B2C-开发者平台收款">B2C-开发者平台收款</option>
                                            <option value="B2C-广告联盟收款">B2C-广告联盟收款</option>
                                            <option value="B2B-外贸收款">B2B-外贸收款</option>
                                            <option value="B2B-物流收款">B2B-物流收款</option>
                                            <option value="人民币结汇">人民币结汇</option>
                                            <option value="全球付款">全球付款</option>
                                            <option value="本地付款">本地付款</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-item">
                                        <label>底价类型：</label>
                                        <div class="radio-group">
                                            <label class="radio-item">
                                                <input type="radio" name="base-price-type" value="standard">
                                                <span class="radio-text">标准底价</span>
                                            </label>
                                            <label class="radio-item">
                                                <input type="radio" name="base-price-type" value="special">
                                                <span class="radio-text">特殊底价</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-item">
                                        <label>备注说明：</label>
                                        <textarea placeholder="请输入备注说明..." style="width: 400px; height: 80px;"></textarea>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <button class="btn btn-primary">保存配置</button>
                                    <button class="btn btn-secondary">重置</button>
                                </div>
                            </div>
                        </div>

                        <!-- 固定比例 Tab -->
                        <div id="commission-ratio-tab" class="tab-content">
                            <div class="form-section">
                                <div class="form-row">
                                    <div class="form-item">
                                        <label>分佣简介：</label>
                                        <input type="text" placeholder="简介" style="width: 400px;">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-item">
                                        <label>分佣比例：</label>
                                        <input type="number" placeholder="请输入比例" style="width: 150px;" min="0" max="100" step="0.1">
                                        <span style="margin-left: 8px; color: rgba(0, 0, 0, 0.45);">%</span>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-item">
                                        <label>比例类型：</label>
                                        <div class="radio-group">
                                            <label class="radio-item">
                                                <input type="radio" name="ratio-type" value="standard">
                                                <span class="radio-text">标准比例</span>
                                            </label>
                                            <label class="radio-item">
                                                <input type="radio" name="ratio-type" value="special">
                                                <span class="radio-text">特殊比例</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-item">
                                        <label>备注说明：</label>
                                        <textarea placeholder="请输入备注说明..." style="width: 400px; height: 80px;"></textarea>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <button class="btn btn-primary">保存配置</button>
                                    <button class="btn btn-secondary">重置</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 汇率返点管理 -->
                <div id="exchange-section" class="content-section">
                    <div class="section-title">汇率返点管理</div>

                    <div class="tab-container">
                        <div class="tab-nav">
                            <div class="tab-item active" onclick="showTab('exchange', 'base')">底价汇率</div>
                            <div class="tab-item" onclick="showTab('exchange', 'fixed')">固定返点</div>
                        </div>

                        <!-- 底价汇率 Tab -->
                        <div id="exchange-base-tab" class="tab-content active">
                            <div class="form-section">
                                <div class="form-row">
                                    <div class="form-item">
                                        <label>返点简介：</label>
                                        <input type="text" placeholder="简介" style="width: 400px;">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-item">
                                        <label>底价类型：</label>
                                        <div class="radio-group">
                                            <label class="radio-item">
                                                <input type="radio" name="exchange-base-type" value="standard">
                                                <span class="radio-text">标准底价汇率</span>
                                            </label>
                                            <label class="radio-item">
                                                <input type="radio" name="exchange-base-type" value="special">
                                                <span class="radio-text">特殊底价汇率</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- 汇率返点配置表格 -->
                                <div style="margin-top: 24px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                        <h4 style="margin: 0; font-size: 16px; font-weight: 500; color: rgba(0, 0, 0, 0.85);">汇率返点配置</h4>
                                        <button class="btn btn-primary" style="font-size: 12px; padding: 6px 12px;">+ 新增配置</button>
                                    </div>

                                    <div class="table-container">
                                        <div class="table-header" style="display: grid; grid-template-columns: 1fr 1fr 1fr 2fr 100px; gap: 16px;">
                                            <div>卖出币种</div>
                                            <div>买入币种</div>
                                            <div>返点</div>
                                            <div>备注</div>
                                            <div>操作</div>
                                        </div>

                                        <div class="table-row" style="display: grid; grid-template-columns: 1fr 1fr 1fr 2fr 100px; gap: 16px; background: #fafafa;">
                                            <div style="padding: 6px 12px; background: #f5f5f5; border-radius: 4px; text-align: center;">其他</div>
                                            <div style="padding: 6px 12px; background: #f5f5f5; border-radius: 4px; text-align: center;">其他</div>
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <input type="number" placeholder="50" style="width: 80px; padding: 6px 8px; border: 1px solid #d9d9d9; border-radius: 4px;" min="0" max="100" step="0.1">
                                                <span style="color: rgba(0, 0, 0, 0.45); font-size: 14px;">%</span>
                                            </div>
                                            <input type="text" placeholder="请输入备注" style="width: 100%; padding: 6px 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                            <div style="color: rgba(0, 0, 0, 0.45); font-size: 12px; text-align: center;">固定</div>
                                        </div>

                                        <div class="table-row" style="display: grid; grid-template-columns: 1fr 1fr 1fr 2fr 100px; gap: 16px;">
                                            <select style="width: 100%; padding: 6px 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                                <option value="">请选择</option>
                                                <option value="USD">USD</option>
                                                <option value="EUR">EUR</option>
                                                <option value="HKD">HKD</option>
                                                <option value="CNH">CNH</option>
                                                <option value="GBP">GBP</option>
                                                <option value="JPY">JPY</option>
                                            </select>
                                            <select style="width: 100%; padding: 6px 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                                <option value="">请选择</option>
                                                <option value="USD">USD</option>
                                                <option value="EUR">EUR</option>
                                                <option value="HKD">HKD</option>
                                                <option value="CNH">CNH</option>
                                                <option value="GBP">GBP</option>
                                                <option value="JPY">JPY</option>
                                            </select>
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <input type="number" placeholder="50" style="width: 80px; padding: 6px 8px; border: 1px solid #d9d9d9; border-radius: 4px;" min="0" max="100" step="0.1">
                                                <span style="color: rgba(0, 0, 0, 0.45); font-size: 14px;">%</span>
                                            </div>
                                            <input type="text" placeholder="请输入备注" style="width: 100%; padding: 6px 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                            <div style="display: flex; gap: 4px; justify-content: center;">
                                                <button class="btn btn-primary" style="font-size: 12px; padding: 4px 8px;">➕</button>
                                                <button class="btn btn-secondary" style="font-size: 12px; padding: 4px 8px;">🗑️</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row" style="margin-top: 24px;">
                                    <button class="btn btn-primary">保存配置</button>
                                    <button class="btn btn-secondary">重置</button>
                                </div>
                            </div>
                        </div>

                        <!-- 固定返点 Tab -->
                        <div id="exchange-fixed-tab" class="tab-content">
                            <div class="form-section">
                                <div class="form-row">
                                    <div class="form-item">
                                        <label>返点简介：</label>
                                        <input type="text" placeholder="简介" style="width: 400px;">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-item">
                                        <label>返点类型：</label>
                                        <div class="radio-group">
                                            <label class="radio-item">
                                                <input type="radio" name="exchange-fixed-type" value="standard">
                                                <span class="radio-text">标准返点</span>
                                            </label>
                                            <label class="radio-item">
                                                <input type="radio" name="exchange-fixed-type" value="special">
                                                <span class="radio-text">特殊返点</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-item">
                                        <label>固定返点比例：</label>
                                        <input type="number" placeholder="请输入返点比例" style="width: 150px;" min="0" max="100" step="0.1">
                                        <span style="margin-left: 8px; color: rgba(0, 0, 0, 0.45);">%</span>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-item">
                                        <label>备注说明：</label>
                                        <textarea placeholder="请输入备注说明..." style="width: 400px; height: 80px;"></textarea>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <button class="btn btn-primary">保存配置</button>
                                    <button class="btn btn-secondary">重置</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 代理政策管理 -->
                <div id="policy-section" class="content-section">
                    <div class="section-title">代理政策管理</div>

                    <div class="tab-container">
                        <div class="tab-nav">
                            <div class="tab-item active" onclick="showTab('policy', 'query')">政策查询</div>
                            <div class="tab-item" onclick="showTab('policy', 'assign')">政策分配</div>
                        </div>

                        <!-- 政策查询 Tab -->
                        <div id="policy-query-tab" class="tab-content active">
                            <div class="form-section">
                                <div class="form-row">
                                    <div class="form-item">
                                        <label>政策名称：</label>
                                        <input type="text" placeholder="请输入政策名称" style="width: 300px;">
                                    </div>
                                    <div class="form-item">
                                        <label>政策类型：</label>
                                        <select style="width: 150px;">
                                            <option value="">全部类型</option>
                                            <option value="standard">标准政策</option>
                                            <option value="special">特殊政策</option>
                                            <option value="custom">定制政策</option>
                                        </select>
                                    </div>
                                    <button class="btn btn-primary">查询</button>
                                </div>

                                <!-- 政策列表 -->
                                <div style="margin-top: 24px;">
                                    <div class="table-container">
                                        <div class="table-header" style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr 1fr 120px; gap: 16px;">
                                            <div>政策名称</div>
                                            <div>政策类型</div>
                                            <div>适用等级</div>
                                            <div>创建时间</div>
                                            <div>状态</div>
                                            <div>操作</div>
                                        </div>

                                        <div class="table-row" style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr 1fr 120px; gap: 16px;">
                                            <div>A级代理商标准政策包</div>
                                            <div><span style="background: #e6f7ff; color: #1890ff; padding: 2px 8px; border-radius: 12px; font-size: 12px;">标准政策</span></div>
                                            <div>A级</div>
                                            <div>2025-01-15</div>
                                            <div><span style="background: #f6ffed; color: #52c41a; padding: 2px 8px; border-radius: 12px; font-size: 12px;">生效中</span></div>
                                            <div style="display: flex; gap: 4px;">
                                                <button class="btn btn-primary" style="font-size: 12px; padding: 4px 8px;">查看</button>
                                                <button class="btn btn-secondary" style="font-size: 12px; padding: 4px 8px;">编辑</button>
                                            </div>
                                        </div>

                                        <div class="table-row" style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr 1fr 120px; gap: 16px;">
                                            <div>B级代理商标准政策包</div>
                                            <div><span style="background: #e6f7ff; color: #1890ff; padding: 2px 8px; border-radius: 12px; font-size: 12px;">标准政策</span></div>
                                            <div>B级</div>
                                            <div>2025-01-15</div>
                                            <div><span style="background: #f6ffed; color: #52c41a; padding: 2px 8px; border-radius: 12px; font-size: 12px;">生效中</span></div>
                                            <div style="display: flex; gap: 4px;">
                                                <button class="btn btn-primary" style="font-size: 12px; padding: 4px 8px;">查看</button>
                                                <button class="btn btn-secondary" style="font-size: 12px; padding: 4px 8px;">编辑</button>
                                            </div>
                                        </div>

                                        <div class="table-row" style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr 1fr 120px; gap: 16px;">
                                            <div>润泽科技特殊优惠政策包</div>
                                            <div><span style="background: #fff3cd; color: #856404; padding: 2px 8px; border-radius: 12px; font-size: 12px;">特殊政策</span></div>
                                            <div>-</div>
                                            <div>2025-01-20</div>
                                            <div><span style="background: #f6ffed; color: #52c41a; padding: 2px 8px; border-radius: 12px; font-size: 12px;">生效中</span></div>
                                            <div style="display: flex; gap: 4px;">
                                                <button class="btn btn-primary" style="font-size: 12px; padding: 4px 8px;">查看</button>
                                                <button class="btn btn-secondary" style="font-size: 12px; padding: 4px 8px;">编辑</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 政策分配 Tab -->
                        <div id="policy-assign-tab" class="tab-content">
                            <div class="form-section">
                                <div class="form-row">
                                    <div class="form-item">
                                        <label>代理商名称：</label>
                                        <select style="width: 300px;">
                                            <option value="">请选择代理商</option>
                                            <option value="agent1">上海润泽科技有限公司</option>
                                            <option value="agent2">北京智汇通科技</option>
                                            <option value="agent3">深圳创新支付</option>
                                        </select>
                                    </div>
                                    <div class="form-item">
                                        <label>政策包：</label>
                                        <select style="width: 300px;">
                                            <option value="">请选择政策包</option>
                                            <option value="policy1">A级代理商标准政策包</option>
                                            <option value="policy2">B级代理商标准政策包</option>
                                            <option value="policy3">C级代理商标准政策包</option>
                                            <option value="policy4">润泽科技特殊优惠政策包</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-item">
                                        <label>生效日期：</label>
                                        <input type="date" style="width: 200px;">
                                    </div>
                                    <div class="form-item">
                                        <label>失效日期：</label>
                                        <input type="date" style="width: 200px;">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-item">
                                        <label>备注说明：</label>
                                        <textarea placeholder="请输入备注说明..." style="width: 500px; height: 80px;"></textarea>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <button class="btn btn-primary">分配政策</button>
                                    <button class="btn btn-secondary">重置</button>
                                </div>

                                <!-- 已分配政策列表 -->
                                <div style="margin-top: 32px;">
                                    <h4 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 500; color: rgba(0, 0, 0, 0.85);">已分配政策</h4>
                                    <div class="table-container">
                                        <div class="table-header" style="display: grid; grid-template-columns: 2fr 2fr 1fr 1fr 1fr 100px; gap: 16px;">
                                            <div>代理商名称</div>
                                            <div>政策包名称</div>
                                            <div>生效日期</div>
                                            <div>失效日期</div>
                                            <div>状态</div>
                                            <div>操作</div>
                                        </div>

                                        <div class="table-row" style="display: grid; grid-template-columns: 2fr 2fr 1fr 1fr 1fr 100px; gap: 16px;">
                                            <div>上海润泽科技有限公司</div>
                                            <div>A级代理商标准政策包</div>
                                            <div>2025-01-01</div>
                                            <div>2025-12-31</div>
                                            <div><span style="background: #f6ffed; color: #52c41a; padding: 2px 8px; border-radius: 12px; font-size: 12px;">生效中</span></div>
                                            <div style="display: flex; gap: 4px;">
                                                <button class="btn btn-secondary" style="font-size: 12px; padding: 4px 8px;">取消</button>
                                            </div>
                                        </div>

                                        <div class="table-row" style="display: grid; grid-template-columns: 2fr 2fr 1fr 1fr 1fr 100px; gap: 16px;">
                                            <div>北京智汇通科技</div>
                                            <div>B级代理商标准政策包</div>
                                            <div>2025-01-01</div>
                                            <div>2025-12-31</div>
                                            <div><span style="background: #f6ffed; color: #52c41a; padding: 2px 8px; border-radius: 12px; font-size: 12px;">生效中</span></div>
                                            <div style="display: flex; gap: 4px;">
                                                <button class="btn btn-secondary" style="font-size: 12px; padding: 4px 8px;">取消</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示不同的内容区域
        function showSection(sectionName) {
            // 隐藏所有内容区域
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // 移除所有菜单项的active状态
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的内容区域
            document.getElementById(sectionName + '-section').classList.add('active');

            // 添加选中菜单项的active状态
            event.target.closest('.menu-item').classList.add('active');
        }

        // 显示不同的Tab内容
        function showTab(sectionName, tabName) {
            // 隐藏该section下的所有tab内容
            const tabContents = document.querySelectorAll(`#${sectionName}-section .tab-content`);
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除该section下所有tab的active状态
            const tabItems = document.querySelectorAll(`#${sectionName}-section .tab-item`);
            tabItems.forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的tab内容
            document.getElementById(`${sectionName}-${tabName}-tab`).classList.add('active');

            // 添加选中tab的active状态
            event.target.classList.add('active');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('代理商管理系统已加载');
        });
    </script>
</body>
</html>
