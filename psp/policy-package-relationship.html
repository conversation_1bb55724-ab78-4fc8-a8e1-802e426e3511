<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策包关系图</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; padding: 20px; }
        
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { font-size: 28px; color: #333; margin-bottom: 10px; }
        .header p { color: #666; font-size: 16px; }
        
        .diagram { background: white; border-radius: 12px; padding: 30px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        
        .level { margin-bottom: 40px; }
        .level-title { font-size: 18px; font-weight: 600; color: #333; margin-bottom: 20px; text-align: center; }
        
        .items { display: flex; justify-content: center; gap: 20px; flex-wrap: wrap; }
        .item { background: #f8f9fa; border: 2px solid #dee2e6; border-radius: 8px; padding: 15px; min-width: 200px; text-align: center; position: relative; }
        
        .package { background: #e3f2fd; border-color: #2196f3; }
        .agent { background: #f3e5f5; border-color: #9c27b0; }
        .merchant { background: #e8f5e8; border-color: #4caf50; }
        .special { background: #fff3e0; border-color: #ff9800; }
        
        .item-title { font-weight: 600; margin-bottom: 8px; }
        .item-desc { font-size: 12px; color: #666; }
        
        .arrow { position: absolute; left: 50%; transform: translateX(-50%); }
        .arrow-down { top: 100%; margin-top: 10px; }
        .arrow-up { bottom: 100%; margin-bottom: 10px; }
        
        .connection-line { height: 30px; width: 2px; background: #ddd; margin: 0 auto; }
        
        .flow-section { margin-top: 40px; }
        .flow-title { font-size: 20px; font-weight: 600; color: #333; margin-bottom: 20px; text-align: center; }
        
        .flow-steps { display: flex; justify-content: space-between; align-items: center; margin: 20px 0; }
        .flow-step { background: white; border: 2px solid #007bff; border-radius: 8px; padding: 15px; flex: 1; margin: 0 10px; text-align: center; }
        .flow-arrow { font-size: 24px; color: #007bff; }
        
        .example-section { margin-top: 40px; background: #f8f9fa; border-radius: 8px; padding: 20px; }
        .example-title { font-size: 18px; font-weight: 600; color: #333; margin-bottom: 15px; }
        .example-item { background: white; border-radius: 6px; padding: 15px; margin-bottom: 15px; border-left: 4px solid #007bff; }
        .example-scenario { font-weight: 600; color: #333; margin-bottom: 8px; }
        .example-details { font-size: 14px; color: #666; line-height: 1.5; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>代理商政策包关系图</h1>
            <p>展示政策包、代理商、商户之间的关系和应用逻辑</p>
        </div>
        
        <div class="diagram">
            <!-- 政策包层级 -->
            <div class="level">
                <div class="level-title">政策包层级</div>
                <div class="items">
                    <div class="item package">
                        <div class="item-title">A级代理商政策包</div>
                        <div class="item-desc">8个产品 | 最优费率</div>
                    </div>
                    <div class="item package">
                        <div class="item-title">B级代理商政策包</div>
                        <div class="item-desc">8个产品 | 标准费率</div>
                    </div>
                    <div class="item package">
                        <div class="item-title">C级代理商政策包</div>
                        <div class="item-desc">8个产品 | 基础费率</div>
                    </div>
                    <div class="item special">
                        <div class="item-title">特殊政策包</div>
                        <div class="item-desc">定制费率 | 特定适用</div>
                    </div>
                </div>
            </div>
            
            <div class="connection-line"></div>
            
            <!-- 代理商层级 -->
            <div class="level">
                <div class="level-title">代理商层级</div>
                <div class="items">
                    <div class="item agent">
                        <div class="item-title">上海润泽科技</div>
                        <div class="item-desc">A级代理商 | 使用A级政策包</div>
                    </div>
                    <div class="item agent">
                        <div class="item-title">北京智汇通</div>
                        <div class="item-desc">B级代理商 | 使用B级政策包</div>
                    </div>
                    <div class="item agent">
                        <div class="item-title">深圳创新支付</div>
                        <div class="item-desc">C级代理商 | 使用C级政策包</div>
                    </div>
                </div>
            </div>
            
            <div class="connection-line"></div>
            
            <!-- 商户层级 -->
            <div class="level">
                <div class="level-title">商户层级</div>
                <div class="items">
                    <div class="item merchant">
                        <div class="item-title">普通商户</div>
                        <div class="item-desc">继承代理商政策包</div>
                    </div>
                    <div class="item merchant special">
                        <div class="item-title">大商户</div>
                        <div class="item-desc">可配置特殊政策包</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 应用流程 -->
        <div class="flow-section">
            <div class="flow-title">政策包应用流程</div>
            <div class="flow-steps">
                <div class="flow-step">
                    <div style="font-weight: 600; margin-bottom: 8px;">1. 商户交易</div>
                    <div style="font-size: 12px; color: #666;">商户发起支付交易</div>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step">
                    <div style="font-weight: 600; margin-bottom: 8px;">2. 查询政策包</div>
                    <div style="font-size: 12px; color: #666;">检查特殊政策包</div>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step">
                    <div style="font-weight: 600; margin-bottom: 8px;">3. 获取费率</div>
                    <div style="font-size: 12px; color: #666;">从政策包获取产品费率</div>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step">
                    <div style="font-weight: 600; margin-bottom: 8px;">4. 计算费用</div>
                    <div style="font-size: 12px; color: #666;">计算最终交易费用</div>
                </div>
            </div>
        </div>
        
        <!-- 应用示例 -->
        <div class="example-section">
            <div class="example-title">政策包应用示例</div>
            
            <div class="example-item">
                <div class="example-scenario">场景1：A级代理商的普通商户</div>
                <div class="example-details">
                    代理商：上海润泽科技(A级) → 商户：普通电商商户<br>
                    适用政策包：A级代理商政策包<br>
                    电商收款费率：0.9% + $0.3
                </div>
            </div>
            
            <div class="example-item">
                <div class="example-scenario">场景2：A级代理商的特殊大商户</div>
                <div class="example-details">
                    代理商：上海润泽科技(A级) → 商户：润泽电商平台(大商户)<br>
                    适用政策包：润泽科技特殊优惠政策包<br>
                    电商收款费率：0.7% + $0.2（特殊优惠）
                </div>
            </div>
            
            <div class="example-item">
                <div class="example-scenario">场景3：B级代理商的普通商户</div>
                <div class="example-details">
                    代理商：北京智汇通(B级) → 商户：普通外贸商户<br>
                    适用政策包：B级代理商政策包<br>
                    外贸收款费率：1.0% + $0.3
                </div>
            </div>
        </div>
    </div>
</body>
</html>
