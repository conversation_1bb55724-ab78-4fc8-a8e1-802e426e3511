<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑政策包</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .breadcrumb { font-size: 14px; color: #666; margin-bottom: 10px; }
        .breadcrumb a { color: #007bff; text-decoration: none; }
        .page-title { font-size: 24px; font-weight: 600; color: #333; margin-bottom: 8px; }
        .page-desc { color: #666; font-size: 14px; }
        .package-info { display: flex; gap: 20px; margin-top: 15px; }
        .info-item { background: #f8f9fa; padding: 8px 12px; border-radius: 4px; font-size: 12px; }
        .info-label { color: #666; }
        .info-value { color: #333; font-weight: 500; }
        
        .form-section { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .section-title { font-size: 18px; font-weight: 600; color: #333; margin-bottom: 20px; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        
        .form-row { display: flex; gap: 20px; margin-bottom: 20px; align-items: center; }
        .form-group { display: flex; flex-direction: column; min-width: 200px; }
        .form-group label { font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px; }
        .form-group input, .form-group select, .form-group textarea { padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; }
        .form-group textarea { min-height: 80px; resize: vertical; }
        
        .product-section { margin-bottom: 25px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden; }
        .product-header { background: #f8f9fa; padding: 15px 20px; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #e9ecef; }
        .product-title { font-size: 16px; font-weight: 600; color: #333; }
        .product-status { display: flex; align-items: center; gap: 15px; }
        .status-badge { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; }
        .status-enabled { background: #d4edda; color: #155724; }
        .status-disabled { background: #f8d7da; color: #721c24; }
        
        .product-content { padding: 20px; }
        .config-list { margin-bottom: 15px; }
        .config-item { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 15px; margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center; transition: all 0.3s; }
        .config-item:hover { background: #e9ecef; }
        .config-item.selected { background: #e3f2fd; border-color: #007bff; }
        
        .config-details { flex: 1; }
        .config-name { font-weight: 500; color: #333; margin-bottom: 4px; }
        .config-desc { font-size: 12px; color: #666; margin-bottom: 8px; }
        .config-params { display: flex; gap: 15px; font-size: 12px; }
        .param-item { background: white; padding: 4px 8px; border-radius: 4px; border: 1px solid #dee2e6; }
        .param-label { color: #666; }
        .param-value { color: #333; font-weight: 500; }
        
        .config-rate { font-weight: 600; color: #007bff; margin-right: 15px; font-size: 16px; }
        .config-actions { display: flex; gap: 8px; }
        
        .btn { padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; transition: all 0.3s; }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-outline { background: white; color: #007bff; border: 1px solid #007bff; }
        .btn-outline:hover { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-danger:hover { background: #c82333; }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        
        .add-config-btn { width: 100%; padding: 15px; border: 2px dashed #dee2e6; background: white; color: #6c757d; border-radius: 6px; cursor: pointer; transition: all 0.3s; }
        .add-config-btn:hover { border-color: #007bff; color: #007bff; }
        
        .actions-section { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: right; }
        .btn-large { padding: 12px 24px; font-size: 14px; margin-left: 10px; }
        
        .product-toggle { display: flex; align-items: center; gap: 10px; }
        .toggle-switch { position: relative; width: 44px; height: 24px; background: #ccc; border-radius: 12px; cursor: pointer; transition: background 0.3s; }
        .toggle-switch.active { background: #007bff; }
        .toggle-slider { position: absolute; top: 2px; left: 2px; width: 20px; height: 20px; background: white; border-radius: 50%; transition: transform 0.3s; }
        .toggle-switch.active .toggle-slider { transform: translateX(20px); }
        
        .change-indicator { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 8px 12px; margin-top: 10px; font-size: 12px; color: #856404; }
        .change-indicator .change-type { font-weight: 600; }
        
        .empty-state { text-align: center; padding: 30px; color: #6c757d; background: #f8f9fa; border-radius: 6px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="breadcrumb">
                <a href="agent-policy-package.html">政策包管理</a> / 编辑政策包
            </div>
            <div class="page-title">编辑政策包 - A级代理商政策包</div>
            <div class="page-desc">修改政策包的产品配置和计费规则</div>
            <div class="package-info">
                <div class="info-item">
                    <span class="info-label">创建时间：</span>
                    <span class="info-value">2025-01-15 10:30</span>
                </div>
                <div class="info-item">
                    <span class="info-label">最后修改：</span>
                    <span class="info-value">2025-01-20 14:25</span>
                </div>
                <div class="info-item">
                    <span class="info-label">使用代理商：</span>
                    <span class="info-value">12个</span>
                </div>
                <div class="info-item">
                    <span class="info-label">状态：</span>
                    <span class="info-value" style="color: #28a745;">生效中</span>
                </div>
            </div>
        </div>
        
        <!-- 基本信息 -->
        <div class="form-section">
            <div class="section-title">基本信息</div>
            <div class="form-row">
                <div class="form-group">
                    <label>政策包名称</label>
                    <input type="text" value="A级代理商政策包">
                </div>
                <div class="form-group">
                    <label>政策包类型</label>
                    <select>
                        <option value="standard" selected>标准政策包</option>
                        <option value="special">特殊政策包</option>
                        <option value="custom">定制政策包</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>适用代理商等级</label>
                    <select>
                        <option value="A" selected>A级代理商</option>
                        <option value="B">B级代理商</option>
                        <option value="C">C级代理商</option>
                        <option value="all">所有等级</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group" style="flex: 1;">
                    <label>政策包描述</label>
                    <textarea>适用于A级代理商及其所有商户，费率相对较低，适合大型代理商</textarea>
                </div>
            </div>
        </div>
        
        <!-- 产品配置 -->
        <div class="form-section">
            <div class="section-title">产品配置 (8个产品)</div>
            
            <!-- 外贸收款 -->
            <div class="product-section">
                <div class="product-header">
                    <div class="product-title">1. 外贸收款</div>
                    <div class="product-status">
                        <span class="status-badge status-enabled">已启用</span>
                        <div class="product-toggle">
                            <span>启用</span>
                            <div class="toggle-switch active" onclick="toggleProduct(this, 'trade')">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="product-content">
                    <div class="config-list">
                        <div class="config-item selected">
                            <div class="config-details">
                                <div class="config-name">全球收款→HKD→DBS银行</div>
                                <div class="config-desc">适用于全球范围的外贸收款，结算到香港DBS银行</div>
                                <div class="config-params">
                                    <div class="param-item">
                                        <span class="param-label">收款类型：</span>
                                        <span class="param-value">全球收款</span>
                                    </div>
                                    <div class="param-item">
                                        <span class="param-label">结算币种：</span>
                                        <span class="param-value">HKD</span>
                                    </div>
                                    <div class="param-item">
                                        <span class="param-label">结算银行：</span>
                                        <span class="param-value">DBS</span>
                                    </div>
                                </div>
                            </div>
                            <div class="config-rate">0.8% + $0.3</div>
                            <div class="config-actions">
                                <button class="btn btn-outline" onclick="editConfig(this)">编辑费率</button>
                                <button class="btn btn-warning" onclick="replaceConfig(this)">更换配置</button>
                                <button class="btn btn-danger" onclick="removeConfig(this)">删除</button>
                            </div>
                        </div>
                        
                        <div class="config-item">
                            <div class="config-details">
                                <div class="config-name">本地收款→HKD→其他银行</div>
                                <div class="config-desc">适用于香港本地的外贸收款，结算到其他银行</div>
                                <div class="config-params">
                                    <div class="param-item">
                                        <span class="param-label">收款类型：</span>
                                        <span class="param-value">本地收款</span>
                                    </div>
                                    <div class="param-item">
                                        <span class="param-label">结算币种：</span>
                                        <span class="param-value">HKD</span>
                                    </div>
                                    <div class="param-item">
                                        <span class="param-label">结算银行：</span>
                                        <span class="param-value">其他</span>
                                    </div>
                                </div>
                            </div>
                            <div class="config-rate">1.0% + $0.3</div>
                            <div class="config-actions">
                                <button class="btn btn-outline" onclick="editConfig(this)">编辑费率</button>
                                <button class="btn btn-danger" onclick="removeConfig(this)">删除</button>
                            </div>
                        </div>
                    </div>
                    
                    <button class="add-config-btn" onclick="addConfig('trade')">
                        + 添加外贸收款配置
                    </button>
                    
                    <div class="change-indicator">
                        <span class="change-type">待保存更改：</span>
                        修改了全球收款费率从 0.9% + $0.3 → 0.8% + $0.3
                    </div>
                </div>
            </div>
            
            <!-- 电商收款 -->
            <div class="product-section">
                <div class="product-header">
                    <div class="product-title">2. 电商收款</div>
                    <div class="product-status">
                        <span class="status-badge status-enabled">已启用</span>
                        <div class="product-toggle">
                            <span>启用</span>
                            <div class="toggle-switch active" onclick="toggleProduct(this, 'ecommerce')">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="product-content">
                    <div class="config-list">
                        <div class="config-item selected">
                            <div class="config-details">
                                <div class="config-name">平台收款→USD→Stripe</div>
                                <div class="config-desc">适用于电商平台的收款，通过Stripe处理</div>
                                <div class="config-params">
                                    <div class="param-item">
                                        <span class="param-label">收款类型：</span>
                                        <span class="param-value">平台收款</span>
                                    </div>
                                    <div class="param-item">
                                        <span class="param-label">结算币种：</span>
                                        <span class="param-value">USD</span>
                                    </div>
                                    <div class="param-item">
                                        <span class="param-label">支付通道：</span>
                                        <span class="param-value">Stripe</span>
                                    </div>
                                </div>
                            </div>
                            <div class="config-rate">0.9% + $0.3</div>
                            <div class="config-actions">
                                <button class="btn btn-outline" onclick="editConfig(this)">编辑费率</button>
                                <button class="btn btn-warning" onclick="replaceConfig(this)">更换配置</button>
                                <button class="btn btn-danger" onclick="removeConfig(this)">删除</button>
                            </div>
                        </div>
                    </div>
                    
                    <button class="add-config-btn" onclick="addConfig('ecommerce')">
                        + 添加电商收款配置
                    </button>
                </div>
            </div>
            
            <!-- 物流收款 - 禁用状态 -->
            <div class="product-section">
                <div class="product-header">
                    <div class="product-title">3. 物流收款</div>
                    <div class="product-status">
                        <span class="status-badge status-disabled">已禁用</span>
                        <div class="product-toggle">
                            <span>启用</span>
                            <div class="toggle-switch" onclick="toggleProduct(this, 'logistics')">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="product-content" style="display: none;">
                    <div class="empty-state">
                        <div>物流收款已禁用，点击上方开关启用此产品</div>
                    </div>
                </div>
            </div>
            
            <!-- 其他产品折叠显示 -->
            <div style="text-align: center; margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                <p style="color: #666; margin-bottom: 15px;">还有5个产品配置：开发者平台收款、广告联盟收款、全球支付、本地支付、结算服务</p>
                <button class="btn btn-outline" onclick="expandAllProducts()">展开所有产品配置</button>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="actions-section">
            <a href="agent-policy-package.html" class="btn btn-outline btn-large">取消</a>
            <button class="btn btn-outline btn-large" onclick="saveDraft()">保存草稿</button>
            <button class="btn btn-success btn-large" onclick="previewChanges()">预览更改</button>
            <button class="btn btn-primary btn-large" onclick="saveChanges()">保存更改</button>
        </div>
    </div>
    
    <script>
        function toggleProduct(toggle, productType) {
            toggle.classList.toggle('active');
            const productContent = toggle.closest('.product-section').querySelector('.product-content');
            const statusBadge = toggle.closest('.product-section').querySelector('.status-badge');
            
            if (toggle.classList.contains('active')) {
                productContent.style.display = 'block';
                statusBadge.textContent = '已启用';
                statusBadge.className = 'status-badge status-enabled';
            } else {
                productContent.style.display = 'none';
                statusBadge.textContent = '已禁用';
                statusBadge.className = 'status-badge status-disabled';
            }
        }
        
        function editConfig(btn) {
            alert('编辑费率配置');
        }
        
        function replaceConfig(btn) {
            alert('更换为其他配置');
        }
        
        function removeConfig(btn) {
            if (confirm('确定要删除这个配置吗？')) {
                btn.closest('.config-item').remove();
            }
        }
        
        function addConfig(productType) {
            alert(`添加${productType}产品配置`);
        }
        
        function expandAllProducts() {
            alert('展开所有8个产品配置');
        }
        
        function saveDraft() {
            alert('草稿已保存');
        }
        
        function previewChanges() {
            alert('预览所有更改');
        }
        
        function saveChanges() {
            if (confirm('确定要保存所有更改吗？这将影响使用此政策包的所有代理商。')) {
                alert('更改已保存！');
                window.location.href = 'agent-policy-package.html';
            }
        }
    </script>
</body>
</html>
