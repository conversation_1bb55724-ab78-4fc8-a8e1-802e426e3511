<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理商政策包管理</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header h1 { font-size: 24px; color: #333; margin-bottom: 10px; }
        .header p { color: #666; font-size: 14px; }
        
        .tabs { display: flex; gap: 2px; margin-bottom: 20px; }
        .tab { padding: 12px 24px; background: white; border: none; border-radius: 8px 8px 0 0; cursor: pointer; font-size: 14px; color: #666; transition: all 0.3s; }
        .tab.active { background: #007bff; color: white; }
        
        .content { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        
        .package-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .package-card { border: 2px solid #e9ecef; border-radius: 12px; padding: 20px; transition: all 0.3s; cursor: pointer; }
        .package-card:hover { border-color: #007bff; box-shadow: 0 4px 12px rgba(0,123,255,0.15); }
        .package-card.selected { border-color: #007bff; background: #f8f9ff; }
        
        .package-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .package-title { font-size: 18px; font-weight: 600; color: #333; }
        .package-badge { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; }
        .badge-standard { background: #d4edda; color: #155724; }
        .badge-special { background: #fff3cd; color: #856404; }
        .badge-custom { background: #f8d7da; color: #721c24; }
        
        .package-desc { color: #666; font-size: 14px; margin-bottom: 15px; line-height: 1.5; }
        
        .fee-structure { display: flex; gap: 15px; }
        .fee-column { flex: 1; }
        .fee-column-title { font-size: 14px; font-weight: 600; color: #333; margin-bottom: 8px; text-align: center; }
        .fee-items { display: flex; flex-direction: column; gap: 6px; }
        .fee-item { display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px; }
        .fee-item-info { display: flex; flex-direction: column; }
        .fee-item-name { color: #333; font-weight: 500; }
        .fee-item-type { color: #666; font-size: 11px; }
        .commission-rate { color: #007bff; font-weight: 600; }
        .rebate-rate { color: #28a745; font-weight: 600; }
        
        .actions { display: flex; gap: 10px; margin-top: 20px; }
        .btn { padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s; }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-outline { background: white; color: #007bff; border: 1px solid #007bff; }
        .btn-outline:hover { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; }
        
        .create-section { border: 2px dashed #dee2e6; border-radius: 12px; padding: 40px; text-align: center; color: #6c757d; }
        .create-section:hover { border-color: #007bff; color: #007bff; cursor: pointer; }
        .create-icon { font-size: 48px; margin-bottom: 15px; }
        
        .assignment-section { margin-top: 30px; padding-top: 30px; border-top: 1px solid #dee2e6; }
        .assignment-title { font-size: 18px; font-weight: 600; color: #333; margin-bottom: 20px; }
        
        .agent-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
        .agent-card { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; }
        .agent-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .agent-name { font-weight: 600; color: #333; }
        .agent-level { padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 600; }
        .level-a { background: #007bff; color: white; }
        .level-b { background: #28a745; color: white; }
        .level-c { background: #ffc107; color: #212529; }
        
        .current-package { color: #666; font-size: 13px; margin-bottom: 10px; }
        .package-select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>代理商政策包管理</h1>
            <p>管理代理商政策包，包含8个产品的完整计费配置，支持标准政策包和特殊政策包</p>
            <div style="margin-top: 15px;">
                <button class="btn btn-outline" onclick="demonstratePackageLogic()">查看政策包应用逻辑</button>
                <button class="btn btn-success" onclick="createPackage()">新增政策包</button>
            </div>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="switchTab('packages')">政策包管理</button>
            <button class="tab" onclick="switchTab('assignment')">政策包分配</button>
            <button class="tab" onclick="switchTab('special')">特殊政策包</button>
        </div>
        
        <div class="content">
            <!-- 政策包管理 -->
            <div id="packages-content">
                <div class="package-grid">
                    <!-- A级代理商政策包 -->
                    <div class="package-card" onclick="selectPackage('a-level')">
                        <div class="package-header">
                            <div class="package-title">A级代理商政策包</div>
                            <span class="package-badge badge-standard">标准政策</span>
                        </div>
                        <div class="package-desc">适用于A级代理商及其所有商户，费率相对较低，适合大型代理商</div>
                        <div class="fee-structure">
                            <div class="fee-column">
                                <div class="fee-column-title">交易分佣</div>
                                <div class="fee-items">
                                    <div class="fee-item">
                                        <div class="fee-item-info">
                                            <span class="fee-item-name">外贸收款</span>
                                            <span class="fee-item-type">底价分佣</span>
                                        </div>
                                        <span class="commission-rate">0.8%</span>
                                    </div>
                                    <div class="fee-item">
                                        <div class="fee-item-info">
                                            <span class="fee-item-name">跨境电商</span>
                                            <span class="fee-item-type">底价分佣</span>
                                        </div>
                                        <span class="commission-rate">0.9%</span>
                                    </div>
                                    <div class="fee-item">
                                        <div class="fee-item-info">
                                            <span class="fee-item-name">数字货币</span>
                                            <span class="fee-item-type">固定比例</span>
                                        </div>
                                        <span class="commission-rate">1.5%</span>
                                    </div>
                                    <div class="fee-item">
                                        <div class="fee-item-info">
                                            <span class="fee-item-name">外汇交易</span>
                                            <span class="fee-item-type">底价分佣</span>
                                        </div>
                                        <span class="commission-rate">0.7%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="fee-column">
                                <div class="fee-column-title">汇率返点</div>
                                <div class="fee-items">
                                    <div class="fee-item">
                                        <div class="fee-item-info">
                                            <span class="fee-item-name">USD/CNY</span>
                                            <span class="fee-item-type">底价汇率</span>
                                        </div>
                                        <span class="rebate-rate">+0.05</span>
                                    </div>
                                    <div class="fee-item">
                                        <div class="fee-item-info">
                                            <span class="fee-item-name">EUR/CNY</span>
                                            <span class="fee-item-type">底价汇率</span>
                                        </div>
                                        <span class="rebate-rate">+0.08</span>
                                    </div>
                                    <div class="fee-item">
                                        <div class="fee-item-info">
                                            <span class="fee-item-name">GBP/CNY</span>
                                            <span class="fee-item-type">固定返点</span>
                                        </div>
                                        <span class="rebate-rate">0.15%</span>
                                    </div>
                                    <div class="fee-item">
                                        <div class="fee-item-info">
                                            <span class="fee-item-name">JPY/CNY</span>
                                            <span class="fee-item-type">底价汇率</span>
                                        </div>
                                        <span class="rebate-rate">+0.03</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="actions">
                            <button class="btn btn-outline">编辑</button>
                            <button class="btn btn-outline">复制</button>
                        </div>
                    </div>
                    
                    <!-- 新增政策包 -->
                    <div class="create-section" onclick="createPackage()">
                        <div class="create-icon">+</div>
                        <div>新增政策包</div>
                        <div style="font-size: 12px; margin-top: 5px;">创建新的标准或特殊政策包</div>
                    </div>
                </div>
            </div>

            <!-- 政策包分配 -->
            <div id="assignment-content" style="display: none;">
                <div class="assignment-section">
                    <div class="assignment-title">代理商政策包分配</div>
                    <div class="agent-list">
                        <div class="agent-card">
                            <div class="agent-header">
                                <span class="agent-name">上海润泽科技有限公司</span>
                                <span class="agent-level level-a">A级</span>
                            </div>
                            <div class="current-package">当前政策包：A级代理商政策包</div>
                            <select class="package-select">
                                <option value="a-level" selected>A级代理商政策包</option>
                                <option value="b-level">B级代理商政策包</option>
                                <option value="c-level">C级代理商政策包</option>
                            </select>
                            <div class="actions" style="margin-top: 10px;">
                                <button class="btn btn-primary" style="font-size: 12px; padding: 6px 12px;">更新</button>
                                <button class="btn btn-outline" style="font-size: 12px; padding: 6px 12px;">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 特殊政策包 -->
            <div id="special-content" style="display: none;">
                <div class="package-grid">
                    <div class="create-section" onclick="createSpecialPackage()">
                        <div class="create-icon">+</div>
                        <div>新增特殊政策包</div>
                        <div style="font-size: 12px; margin-top: 5px;">为特定代理商或商户创建定制政策包</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tab) {
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');

            document.getElementById('packages-content').style.display = 'none';
            document.getElementById('assignment-content').style.display = 'none';
            document.getElementById('special-content').style.display = 'none';

            if (tab === 'assignment') {
                document.getElementById('assignment-content').style.display = 'block';
            } else if (tab === 'special') {
                document.getElementById('special-content').style.display = 'block';
            } else {
                document.getElementById('packages-content').style.display = 'block';
            }
        }

        function selectPackage(packageId) {
            document.querySelectorAll('.package-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');
            console.log('选中政策包:', packageId);
        }

        function createPackage() {
            const packageName = prompt('请输入新政策包名称:');
            if (packageName) {
                alert(`创建政策包: ${packageName}\n跳转到政策包配置页面`);
            }
        }

        function createSpecialPackage() {
            const packageName = prompt('请输入特殊政策包名称:');
            if (packageName) {
                alert(`创建特殊政策包: ${packageName}\n跳转到特殊政策包配置页面`);
            }
        }

        function demonstratePackageLogic() {
            alert('政策包应用逻辑演示功能');
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('代理商政策包管理系统已加载');
        });
    </script>
</body>
</html>