<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增汇率返点</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: #f0f2f5;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
        }

        /* 页面头部 */
        .page-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e8e8e8;
        }

        .breadcrumb {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            margin-bottom: 8px;
        }

        .breadcrumb a {
            color: #1890ff;
            text-decoration: none;
        }

        .page-title {
            font-size: 24px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: 600;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
        }

        /* 表单区域 */
        .form-section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 16px;
            color: rgba(0, 0, 0, 0.85);
        }

        .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
            align-items: center;
        }

        .form-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-item label {
            min-width: 120px;
            text-align: right;
            color: rgba(0, 0, 0, 0.85);
        }

        .form-item input, .form-item select, .form-item textarea {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-item input, .form-item select {
            width: 200px;
        }

        .form-item textarea {
            width: 400px;
            min-height: 60px;
            resize: vertical;
        }

        /* 信息提示框 */
        .info-notice {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            padding: 16px;
            margin-bottom: 24px;
            font-size: 14px;
            color: #1890ff;
        }

        .info-notice > div:first-child {
            font-weight: 600;
            margin-bottom: 8px;
        }

        /* 单选按钮组 */
        .radio-group {
            display: flex;
            gap: 16px;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        .radio-item input[type="radio"] {
            margin: 0;
        }

        .radio-text {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            border: none;
            font-size: 14px;
            transition: all 0.3s;
        }

        .btn-primary {
            background-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: rgba(0, 0, 0, 0.65);
            border: 1px solid #d9d9d9;
        }

        .btn-secondary:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        /* 汇率配置表格 */
        .commission-table {
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 16px;
        }

        .table-header {
            background: #fafafa;
            padding: 12px 16px;
            border-bottom: 1px solid #e8e8e8;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 2fr 100px;
            gap: 16px;
            align-items: center;
            font-weight: 500;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
        }

        .commission-row {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 2fr 100px;
            gap: 16px;
            align-items: center;
        }

        .commission-row:last-child {
            border-bottom: none;
        }

        .fixed-row {
            background: #fafafa;
        }

        .currency-display {
            padding: 6px 12px;
            background: #f5f5f5;
            border-radius: 4px;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            text-align: center;
        }

        .rate-input {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .rate-input input {
            flex: 1;
            padding: 6px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }

        .rate-input input:focus {
            outline: none;
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .commission-remark {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }

        .commission-remark:focus {
            outline: none;
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .btn-group {
            display: flex;
            gap: 4px;
            justify-content: center;
        }

        /* 操作按钮区域 */
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e8e8e8;
        }

        .btn-large {
            padding: 10px 24px;
            font-size: 14px;
            min-width: 80px;
        }

        /* 配置标题区域 */
        .config-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .config-title {
            font-size: 16px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
        }

        /* 字符计数 */
        .char-count {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <div class="breadcrumb">
                <a href="计费管理.html">代理商管理</a> / <a href="commission-management.html">汇率返点管理</a> / 配置分佣参数
            </div>
            <div class="page-title">配置汇率返点</div>
            <div class="page-subtitle">设置代理商汇率返点的相关参数和规则</div>
        </div>

        <!-- 配置简介 -->
        <div class="form-section">
            <div class="info-notice" style="background: #e6f7ff; border: 1px solid #91d5ff; border-radius: 4px; padding: 16px; margin-bottom: 24px; font-size: 14px; color: #1890ff;">
                <div style="font-weight: 600; margin-bottom: 8px;">政策简介：</div>
                <div style="line-height: 1.6;">
                    返点是代理商从商户换汇交易中获得收益的机制。系统支持两种分佣方式：<br>
                    <strong>底价返点</strong>：代理商可以在底价汇率基础上加点，赚取返点收益；<br>
                    <strong>固定返点</strong>：商户汇率报价基础上直接给代理商直接返点。<br>
                </div>
            </div>
        </div>


        <!-- 基本信息 -->
        <div class="form-section">
            <div class="section-title">基本信息</div>

            <!-- 政策版本 -->
            <div class="form-row">
                <div class="form-item">
                    <label>返点简介：</label>
                    <input type="text" id="policy-version" placeholder="简介" style="width: 300px;">
                </div>
            </div>

            <!-- 分佣类型选择 -->
            <div class="form-row">
                <div class="form-item">
                    <label>返点类型：</label>
                    <div class="radio-group">
                        <label class="radio-item">
                            <input type="radio" name="commission-type" value="base-price" onchange="toggleCommissionFields()">
                            <span class="radio-text">底价汇率</span>
                        </label>
                        <label class="radio-item">
                            <input type="radio" name="commission-type" value="fixed-ratio" onchange="toggleCommissionFields()">
                            <span class="radio-text">固定返点</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- 底价类型选择（仅底价汇率时显示） -->
            <div class="form-row" id="base-price-type-row" style="display: none;">
                <div class="form-item">
                    <label>底价类型：</label>
                    <div class="radio-group">
                        <label class="radio-item">
                            <input type="radio" name="base-price-type" value="standard" onchange="togglePriceTypeFields()">
                            <span class="radio-text">标准底价汇率</span>
                        </label>
                        <label class="radio-item">
                            <input type="radio" name="base-price-type" value="special" onchange="togglePriceTypeFields()">
                            <span class="radio-text">特殊底价汇率</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- 返点类型选择（仅固定返点时显示） -->
            <div class="form-row" id="ratio-type-row" style="display: none;">
                <div class="form-item">
                    <label>返点类型：</label>
                    <div class="radio-group">
                        <label class="radio-item">
                            <input type="radio" name="ratio-type" value="standard" onchange="toggleRatioTypeFields()">
                            <span class="radio-text">标准返点</span>
                        </label>
                        <label class="radio-item">
                            <input type="radio" name="ratio-type" value="special" onchange="toggleRatioTypeFields()">
                            <span class="radio-text">特殊返点</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- 汇率返点配置 -->
        <div class="form-section">
            <div class="config-header">
                <div class="section-title">汇率返点配置</div>
                <button class="btn btn-primary btn-sm" onclick="addCommissionRow()">+ 新增配置</button>
            </div>

            <div class="commission-table">
                <div class="table-header">
                    <div>卖出币种</div>
                    <div>买入币种</div>
                    <div>返点</div>
                    <div>备注</div>
                    <div>操作</div>
                </div>

                <div id="commissionRows">
                    <!-- 固定的"其他→其他"行 -->
                    <div class="commission-row fixed-row">
                        <div class="currency-display">其他</div>
                        <div class="currency-display">其他</div>
                        <div class="rate-input">
                            <input type="number" placeholder="50" min="0" max="100" step="0.1">
                            <span style="color: rgba(0, 0, 0, 0.45); font-size: 14px;">%</span>
                        </div>
                        <input type="text" class="commission-remark" placeholder="请输入备注">
                        <div style="color: rgba(0, 0, 0, 0.45); font-size: 12px; text-align: center;">固定</div>
                    </div>

                    <!-- 可编辑配置行 -->
                    <div class="commission-row">
                        <select class="form-control">
                            <option value="">请选择</option>
                            <option value="USD">USD</option>
                            <option value="EUR">EUR</option>
                            <option value="HKD">HKD</option>
                            <option value="CNH">CNH</option>
                            <option value="GBP">GBP</option>
                            <option value="JPY">JPY</option>
                        </select>
                        <select class="form-control">
                            <option value="">请选择</option>
                            <option value="USD">USD</option>
                            <option value="EUR">EUR</option>
                            <option value="HKD">HKD</option>
                            <option value="CNH">CNH</option>
                            <option value="GBP">GBP</option>
                            <option value="JPY">JPY</option>
                        </select>
                        <div class="rate-input">
                            <input type="number" placeholder="50" min="0" max="100" step="0.1">
                            <span style="color: rgba(0, 0, 0, 0.45); font-size: 14px;">%</span>
                        </div>
                        <input type="text" class="commission-remark" placeholder="请输入备注">
                        <div class="btn-group">
                            <button class="btn btn-sm btn-primary" onclick="addCommissionRow()" title="新增">➕</button>
                            <button class="btn btn-sm btn-secondary" onclick="deleteCommissionRow(this)" title="删除">🗑️</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="btn btn-secondary btn-large" onclick="goBack()">取消</button>
            <button class="btn btn-primary btn-large" onclick="saveCommissionConfig()">保存</button>
        </div>
            </div>
        </div>
    </div>

    <script>
        // 切换返点类型显示
        function toggleCommissionFields() {
            const commissionType = document.querySelector('input[name="commission-type"]:checked');
            const basePriceRow = document.getElementById('base-price-type-row');
            const ratioTypeRow = document.getElementById('ratio-type-row');

            // 隐藏所有子选项
            basePriceRow.style.display = 'none';
            ratioTypeRow.style.display = 'none';

            if (commissionType) {
                if (commissionType.value === 'base-price') {
                    // 显示底价类型选择
                    basePriceRow.style.display = 'flex';
                } else if (commissionType.value === 'fixed-ratio') {
                    // 显示返点类型选择
                    ratioTypeRow.style.display = 'flex';
                }
            }
        }

        // 切换底价类型字段
        function togglePriceTypeFields() {
            // 这里可以添加底价类型切换的逻辑
            console.log('底价类型已切换');
        }

        // 切换返点类型字段
        function toggleRatioTypeFields() {
            // 这里可以添加返点类型切换的逻辑
            console.log('返点类型已切换');
        }

        // 新增配置行
        function addCommissionRow() {
            const commissionRows = document.getElementById('commissionRows');
            const newRow = document.createElement('div');
            newRow.className = 'commission-row';

            newRow.innerHTML = `
                <select class="sell-currency form-control">
                    <option value="">请选择</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="HKD">HKD</option>
                    <option value="CNH">CNH</option>
                    <option value="GBP">GBP</option>
                    <option value="JPY">JPY</option>
                </select>
                <select class="buy-currency form-control">
                    <option value="">请选择</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="HKD">HKD</option>
                    <option value="CNH">CNH</option>
                    <option value="GBP">GBP</option>
                    <option value="JPY">JPY</option>
                </select>
                <div class="rate-input">
                    <input type="number" class="commission-rate" placeholder="50" min="0" max="100" step="0.1">
                    <span style="color: #666; font-size: 14px;">%</span>
                </div>
                <input type="text" class="commission-remark form-control" placeholder="请输入备注">
                <div class="btn-group">
                    <button class="btn btn-sm" onclick="addCommissionRow()" style="background: #28a745; color: white;" title="新增">➕</button>
                    <button class="btn btn-sm" onclick="deleteCommissionRow(this)" style="background: #dc3545; color: white;" title="删除">🗑️</button>
                </div>
            `;

            commissionRows.appendChild(newRow);
        }

        // 删除配置行
        function deleteCommissionRow(button) {
            const row = button.closest('.commission-row');
            
            // 检查是否是固定行
            if (row.classList.contains('fixed-row')) {
                alert('固定行不能删除');
                return;
            }
            
            const rows = document.querySelectorAll('.commission-row:not(.fixed-row)');
            if (rows.length > 1) {
                row.remove();
            } else {
                alert('至少需要保留一个配置行');
            }
        }

        // 保存配置
        function saveCommissionConfig() {
            // 验证代理商等级
            const agentLevel = document.getElementById('agentLevel').value;
            if (!agentLevel) {
                alert('请选择代理商等级');
                return;
            }
            
            // 验证固定行的返点
            const fixedRow = document.querySelector('.fixed-row');
            const fixedRate = fixedRow.querySelector('.commission-rate').value;
            if (!fixedRate) {
                alert('请输入"其他→其他"的返点百分比');
                return;
            }
            
            // 验证其他行
            const rows = document.querySelectorAll('.commission-row:not(.fixed-row)');
            let valid = true;
            
            rows.forEach(row => {
                const sellCurrency = row.querySelector('.sell-currency').value;
                const buyCurrency = row.querySelector('.buy-currency').value;
                const rate = row.querySelector('.commission-rate').value;
                
                if (!sellCurrency || !buyCurrency || !rate) {
                    valid = false;
                }
            });
            
            if (!valid) {
                alert('请完善所有货币对的返点配置');
                return;
            }
            
            // 收集数据
            const data = {
                agentLevel: agentLevel,
                remark: document.getElementById('remark').value,
                commissions: []
            };
            
            // 收集固定行数据
            const fixedRemark = fixedRow.querySelector('.commission-remark').value;
            data.commissions.push({
                sellCurrency: '其他',
                buyCurrency: '其他',
                rate: fixedRate,
                remark: fixedRemark,
                isFixed: true
            });
            
            // 收集其他行数据
            rows.forEach(row => {
                const sellCurrency = row.querySelector('.sell-currency').value;
                const buyCurrency = row.querySelector('.buy-currency').value;
                const rate = row.querySelector('.commission-rate').value;
                const remark = row.querySelector('.commission-remark').value;
                
                if (sellCurrency && buyCurrency && rate) {
                    data.commissions.push({
                        sellCurrency: sellCurrency,
                        buyCurrency: buyCurrency,
                        rate: rate,
                        remark: remark,
                        isFixed: false
                    });
                }
            });
            
            console.log('保存的数据:', data);
            alert('保存成功！');
            goBack();
        }

        // 返回
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.close();
            }
        }
    </script>
</body>
</html>
