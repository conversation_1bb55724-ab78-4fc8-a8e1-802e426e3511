<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增汇率返点</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 24px 30px;
            border-radius: 12px;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-left: 4px solid #007bff;
        }

        .breadcrumb {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 12px;
        }

        .breadcrumb a {
            color: #007bff;
            text-decoration: none;
            transition: color 0.3s;
        }

        .breadcrumb a:hover {
            color: #0056b3;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 16px;
            color: #6c757d;
            line-height: 1.5;
        }

        /* 表单区域 */
        .form-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 24px 0;
            padding-bottom: 12px;
            border-bottom: 3px solid #007bff;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #007bff, #28a745);
            border-radius: 2px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            align-items: center;
        }

        .form-item {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .form-item label {
            min-width: 120px;
            text-align: right;
            color: #495057;
            font-weight: 500;
            font-size: 14px;
        }

        .form-control {
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        /* 按钮样式 */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #545b62);
            color: white;
            box-shadow: 0 4px 15px rgba(108,117,125,0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108,117,125,0.4);
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 12px;
        }

        .commission-table {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .table-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 2fr 100px;
            gap: 15px;
            align-items: center;
            font-weight: 600;
            font-size: 14px;
        }

        .commission-row {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 2fr 100px;
            gap: 15px;
            align-items: center;
        }

        .fixed-row {
            background: #f8f9fa;
        }

        .currency-display {
            padding: 8px 12px;
            background: #e9ecef;
            border-radius: 4px;
            font-size: 14px;
            color: #6c757d;
        }

        .rate-input {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .rate-input input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn-group {
            display: flex;
            gap: 5px;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }

        .btn-large {
            padding: 12px 30px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <div class="breadcrumb">
                <a href="计费管理.html">代理商管理</a> / <a href="commission-management.html">汇率返点管理</a> / 配置分佣参数
            </div>
            <div class="page-title">配置汇率返点</div>
            <div class="page-subtitle">设置代理商汇率返点的相关参数和规则</div>
        </div>

        <!-- 配置简介 -->
        <div class="form-section">
            <div class="info-notice" style="background: #e6f7ff; border: 1px solid #91d5ff; border-radius: 4px; padding: 16px; margin-bottom: 24px; font-size: 14px; color: #1890ff;">
                <div style="font-weight: 600; margin-bottom: 8px;">政策简介：</div>
                <div style="line-height: 1.6;">
                    返点是代理商从商户换汇交易中获得收益的机制。系统支持两种分佣方式：<br>
                    <strong>底价返点</strong>：代理商可以在底价汇率基础上加点，赚取返点收益；<br>
                    <strong>固定返点</strong>：商户汇率报价基础上直接给代理商直接返点。<br>
                </div>
            </div>
        </div>


        <!-- 基本信息 -->
        <div class="form-section">
            <div class="section-title">基本信息</div>

            <!-- 政策版本 -->
            <div class="form-row">
                <div class="form-item">
                    <label>分佣简介：</label>
                    <input type="text" id="policy-version" placeholder="简介" style="width: 300px;">
                </div>
            </div>

            <!-- 分佣类型选择 -->
            <div class="form-row">
                <div class="form-item">
                    <label>分佣类型：</label>
                    <div class="radio-group">
                        <label class="radio-item">
                            <input type="radio" name="commission-type" value="base-price" onchange="toggleCommissionFields()">
                            <span class="radio-text">底价分佣</span>
                        </label>
                        <label class="radio-item">
                            <input type="radio" name="commission-type" value="fixed-ratio" onchange="toggleCommissionFields()">
                            <span class="radio-text">手续费固定比例分佣</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- 底价类型选择（仅底价分佣时显示） -->
            <div class="form-row" id="base-price-type-row" style="display: none;">
                <div class="form-item">
                    <label>底价类型：</label>
                    <div class="radio-group">
                        <label class="radio-item">
                            <input type="radio" name="base-price-type" value="standard" onchange="togglePriceTypeFields()">
                            <span class="radio-text">标准底价</span>
                        </label>
                        <label class="radio-item">
                            <input type="radio" name="base-price-type" value="special" onchange="togglePriceTypeFields()">
                            <span class="radio-text">特殊底价</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- 比例类型选择（仅固定比例分佣时显示） -->
            <div class="form-row" id="ratio-type-row" style="display: none;">
                <div class="form-item">
                    <label>比例类型：</label>
                    <div class="radio-group">
                        <label class="radio-item">
                            <input type="radio" name="ratio-type" value="standard" onchange="toggleRatioTypeFields()">
                            <span class="radio-text">标准比例</span>
                        </label>
                        <label class="radio-item">
                            <input type="radio" name="ratio-type" value="special" onchange="toggleRatioTypeFields()">
                            <span class="radio-text">特殊比例</span>
                        </label>
                    </div>
                </div>
            </div>
                <!-- 汇率返点配置 -->
                <div style="margin-bottom: 40px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h4 style="margin: 0; font-size: 18px; font-weight: 600; color: #333; padding-bottom: 10px; border-bottom: 2px solid #28a745;">汇率返点配置</h4>
                        <button class="btn btn-primary btn-sm" onclick="addCommissionRow()">+ 新增配置</button>
                    </div>

                    <div class="commission-table">
                        <div class="table-header">
                            <div>卖出币种</div>
                            <div>买入币种</div>
                            <div>返点</div>
                            <div>备注</div>
                            <div>操作</div>
                        </div>

                        <div id="commissionRows">
                            <!-- 固定的"其他→其他"行 -->
                            <div class="commission-row fixed-row">
                                <div class="currency-display">其他</div>
                                <div class="currency-display">其他</div>
                                <div class="rate-input">
                                    <input type="number" class="commission-rate" placeholder="50" min="0" max="100" step="0.1">
                                    <span style="color: #666; font-size: 14px;">%</span>
                                </div>
                                <input type="text" class="commission-remark form-control" placeholder="请输入备注">
                                <div style="color: #6c757d; font-size: 12px; text-align: center;">固定</div>
                            </div>

                            <!-- 可编辑配置行 -->
                            <div class="commission-row">
                                <select class="sell-currency form-control">
                                    <option value="">请选择</option>
                                    <option value="USD">USD</option>
                                    <option value="EUR">EUR</option>
                                    <option value="HKD">HKD</option>
                                    <option value="CNH">CNH</option>
                                    <option value="GBP">GBP</option>
                                    <option value="JPY">JPY</option>
                                </select>
                                <select class="buy-currency form-control">
                                    <option value="">请选择</option>
                                    <option value="USD">USD</option>
                                    <option value="EUR">EUR</option>
                                    <option value="HKD">HKD</option>
                                    <option value="CNH">CNH</option>
                                    <option value="GBP">GBP</option>
                                    <option value="JPY">JPY</option>
                                </select>
                                <div class="rate-input">
                                    <input type="number" class="commission-rate" placeholder="50" min="0" max="100" step="0.1">
                                    <span style="color: #666; font-size: 14px;">%</span>
                                </div>
                                <input type="text" class="commission-remark form-control" placeholder="请输入备注">
                                <div class="btn-group">
                                    <button class="btn btn-sm" onclick="addCommissionRow()" style="background: #28a745; color: white;" title="新增">➕</button>
                                    <button class="btn btn-sm" onclick="deleteCommissionRow(this)" style="background: #dc3545; color: white;" title="删除">🗑️</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="btn btn-secondary btn-large" onclick="goBack()">取消</button>
                    <button class="btn btn-primary btn-large" onclick="saveCommissionConfig()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 新增配置行
        function addCommissionRow() {
            const commissionRows = document.getElementById('commissionRows');
            const newRow = document.createElement('div');
            newRow.className = 'commission-row';

            newRow.innerHTML = `
                <select class="sell-currency form-control">
                    <option value="">请选择</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="HKD">HKD</option>
                    <option value="CNH">CNH</option>
                    <option value="GBP">GBP</option>
                    <option value="JPY">JPY</option>
                </select>
                <select class="buy-currency form-control">
                    <option value="">请选择</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="HKD">HKD</option>
                    <option value="CNH">CNH</option>
                    <option value="GBP">GBP</option>
                    <option value="JPY">JPY</option>
                </select>
                <div class="rate-input">
                    <input type="number" class="commission-rate" placeholder="50" min="0" max="100" step="0.1">
                    <span style="color: #666; font-size: 14px;">%</span>
                </div>
                <input type="text" class="commission-remark form-control" placeholder="请输入备注">
                <div class="btn-group">
                    <button class="btn btn-sm" onclick="addCommissionRow()" style="background: #28a745; color: white;" title="新增">➕</button>
                    <button class="btn btn-sm" onclick="deleteCommissionRow(this)" style="background: #dc3545; color: white;" title="删除">🗑️</button>
                </div>
            `;

            commissionRows.appendChild(newRow);
        }

        // 删除配置行
        function deleteCommissionRow(button) {
            const row = button.closest('.commission-row');
            
            // 检查是否是固定行
            if (row.classList.contains('fixed-row')) {
                alert('固定行不能删除');
                return;
            }
            
            const rows = document.querySelectorAll('.commission-row:not(.fixed-row)');
            if (rows.length > 1) {
                row.remove();
            } else {
                alert('至少需要保留一个配置行');
            }
        }

        // 保存配置
        function saveCommissionConfig() {
            // 验证代理商等级
            const agentLevel = document.getElementById('agentLevel').value;
            if (!agentLevel) {
                alert('请选择代理商等级');
                return;
            }
            
            // 验证固定行的返点
            const fixedRow = document.querySelector('.fixed-row');
            const fixedRate = fixedRow.querySelector('.commission-rate').value;
            if (!fixedRate) {
                alert('请输入"其他→其他"的返点百分比');
                return;
            }
            
            // 验证其他行
            const rows = document.querySelectorAll('.commission-row:not(.fixed-row)');
            let valid = true;
            
            rows.forEach(row => {
                const sellCurrency = row.querySelector('.sell-currency').value;
                const buyCurrency = row.querySelector('.buy-currency').value;
                const rate = row.querySelector('.commission-rate').value;
                
                if (!sellCurrency || !buyCurrency || !rate) {
                    valid = false;
                }
            });
            
            if (!valid) {
                alert('请完善所有货币对的返点配置');
                return;
            }
            
            // 收集数据
            const data = {
                agentLevel: agentLevel,
                remark: document.getElementById('remark').value,
                commissions: []
            };
            
            // 收集固定行数据
            const fixedRemark = fixedRow.querySelector('.commission-remark').value;
            data.commissions.push({
                sellCurrency: '其他',
                buyCurrency: '其他',
                rate: fixedRate,
                remark: fixedRemark,
                isFixed: true
            });
            
            // 收集其他行数据
            rows.forEach(row => {
                const sellCurrency = row.querySelector('.sell-currency').value;
                const buyCurrency = row.querySelector('.buy-currency').value;
                const rate = row.querySelector('.commission-rate').value;
                const remark = row.querySelector('.commission-remark').value;
                
                if (sellCurrency && buyCurrency && rate) {
                    data.commissions.push({
                        sellCurrency: sellCurrency,
                        buyCurrency: buyCurrency,
                        rate: rate,
                        remark: remark,
                        isFixed: false
                    });
                }
            });
            
            console.log('保存的数据:', data);
            alert('保存成功！');
            goBack();
        }

        // 返回
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.close();
            }
        }
    </script>
</body>
</html>
