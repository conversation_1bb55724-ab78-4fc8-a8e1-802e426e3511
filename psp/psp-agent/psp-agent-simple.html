<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PSP代理商管理系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; display: flex; min-height: 100vh; }

        /* 左侧菜单 */
        .sidebar { width: 280px; background: #2c3e50; color: white; position: fixed; height: 100vh; overflow-y: auto; }
        .sidebar-header { padding: 20px; border-bottom: 1px solid #34495e; background: #1a252f; }
        .sidebar-title { font-size: 18px; font-weight: 600; color: #ecf0f1; margin-bottom: 5px; }
        .sidebar-subtitle { font-size: 12px; color: #95a5a6; }
        .nav-section { padding: 20px 0; }
        .nav-main-item { margin-bottom: 5px; }
        .nav-main-link { display: flex; align-items: center; justify-content: space-between; padding: 12px 20px; color: #ecf0f1; text-decoration: none; font-weight: 500; transition: all 0.3s; border-left: 3px solid transparent; }
        .nav-main-link:hover, .nav-main-link.active { background: #34495e; border-left-color: #3498db; color: #3498db; }
        .nav-arrow { font-size: 12px; transition: transform 0.3s; }
        .nav-submenu { max-height: 0; overflow: hidden; transition: max-height 0.3s ease; background: #1a252f; }
        .nav-submenu.active { max-height: 500px; }
        .nav-sub-link { display: block; padding: 10px 20px 10px 40px; color: #bdc3c7; text-decoration: none; font-size: 14px; transition: all 0.3s; border-left: 3px solid transparent; }
        .nav-sub-link:hover, .nav-sub-link.active { background: #34495e; color: #3498db; border-left-color: #3498db; }

        /* 主内容区 */
        .main-content { margin-left: 280px; flex: 1; padding: 20px; min-height: 100vh; }
        .page-content { display: none; }
        .page-content.active { display: block; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; margin-bottom: 20px; }
        .card-header { background: #f8f9fa; padding: 20px; border-bottom: 1px solid #dee2e6; display: flex; justify-content: space-between; align-items: center; }
        .card-title { font-size: 20px; font-weight: 600; color: #333; margin: 0; }
        .card-body { padding: 20px; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; text-decoration: none; display: inline-block; text-align: center; transition: all 0.3s; margin-right: 10px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-success { background: #28a745; color: white; }
        .btn-sm { padding: 6px 12px; font-size: 12px; }
        
        /* 政策包卡片样式 */
        .package-card {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            background: white;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .package-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
        }
        
        .package-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .package-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .package-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .badge-standard {
            background: #d4edda;
            color: #155724;
        }
        
        .badge-special {
            background: #fff3cd;
            color: #856404;
        }
        
        .package-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .fee-structure {
            display: flex;
            gap: 20px;
        }
        
        .fee-column {
            flex: 1;
        }
        
        .fee-column-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            text-align: center;
            padding-bottom: 8px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .fee-items {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .fee-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 12px;
            background: #f8f9fa;
            border-radius: 6px;
            font-size: 13px;
        }
        
        .fee-item-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .fee-item-name {
            color: #333;
            font-weight: 500;
        }
        
        .fee-item-type {
            color: #666;
            font-size: 11px;
        }
        
        .commission-rate {
            color: #007bff;
            font-weight: 600;
            font-size: 14px;
        }
        
        .rebate-rate {
            color: #28a745;
            font-weight: 600;
            font-size: 14px;
        }
        
        .actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }
        
        .btn-outline {
            background: white;
            color: #007bff;
            border: 1px solid #007bff;
        }
        
        .btn-outline:hover {
            background: #007bff;
            color: white;
        }

        /* Tab样式 */
        .tab-container { margin-bottom: 20px; }
        .tab-nav { display: flex; border-bottom: 2px solid #dee2e6; margin-bottom: 20px; }
        .tab-item { padding: 12px 24px; background: #f8f9fa; border: 1px solid #dee2e6; border-bottom: none; cursor: pointer; font-weight: 500; color: #6c757d; transition: all 0.3s; margin-right: 2px; }
        .tab-item.active { background: white; color: #007bff; border-color: #007bff; border-bottom: 2px solid white; margin-bottom: -2px; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }

        /* 搜索区域 */
        .search-section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .search-grid { display: flex; gap: 15px; align-items: end; flex-wrap: wrap; margin-bottom: 15px; }
        .search-item { display: flex; flex-direction: column; min-width: 150px; }
        .search-item label { font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px; }
        .search-item input, .search-item select { padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; }

        /* 表格样式 */
        .table-container { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .data-table { width: 100%; border-collapse: collapse; table-layout: fixed; }
        .data-table th { background: #f8f9fa; padding: 16px 12px; text-align: left; font-weight: 600; color: #333; border-bottom: 1px solid #dee2e6; font-size: 14px; }
        .data-table td { padding: 16px 12px; border-bottom: 1px solid #eee; font-size: 14px; vertical-align: top; }
        .data-table tr:hover { background: #f8f9fa; }

        /* 操作链接 */
        .action-link { color: #007bff; text-decoration: none; margin-right: 8px; font-size: 13px; transition: color 0.3s; }
        .action-link:hover { color: #0056b3; }
        .action-link.danger { color: #dc3545; }
        .action-link.danger:hover { color: #c82333; }

        /* 分页样式 */
        .pagination-container { display: flex; justify-content: space-between; align-items: center; margin-top: 20px; margin-bottom: 40px; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .pagination-info { color: #666; font-size: 14px; }
        .pagination-controls { display: flex; gap: 5px; }
        .pagination-controls button { padding: 8px 12px; border: 1px solid #ddd; background: white; color: #666; border-radius: 4px; cursor: pointer; transition: all 0.3s; }
        .pagination-controls button:hover { background: #f8f9fa; }
        .pagination-controls button.active { background: #007bff; color: white; border-color: #007bff; }

        /* 多行内容样式 */
        .multi-line-content { font-size: 13px; line-height: 1.6; max-width: 100%; }
        .multi-line-content .line-item { margin-bottom: 2px; }
        .multi-line-content .more-indicator { color: #6c757d; font-size: 12px; }

        /* 状态标签 */
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600; white-space: nowrap; }
        .status-active { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        
        /* 分佣管理和汇率返点专属样式 */
        .commission-section .container,
        .exchange-section .container {
            max-width: 1400px;
            margin: 0;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
        }
        .commission-section .page-header,
        .exchange-section .page-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e8e8e8;
        }
        .commission-section .page-title,
        .exchange-section .page-title {
            font-size: 24px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: 600;
            margin-bottom: 8px;
        }
        .commission-section .page-subtitle,
        .exchange-section .page-subtitle {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
        }
        .commission-section .breadcrumb,
        .exchange-section .breadcrumb {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            margin-bottom: 8px;
        }
        .commission-section .breadcrumb a,
        .exchange-section .breadcrumb a {
            color: #1890ff;
            text-decoration: none;
        }
        
        /* Tab样式重写 */
        .commission-section .tab-container,
        .exchange-section .tab-container {
            margin-bottom: 24px;
        }
        .commission-section .tab-header,
        .exchange-section .tab-header {
            display: flex;
            border-bottom: 1px solid #e8e8e8;
            margin-bottom: 16px;
        }
        .commission-section .tab-item,
        .exchange-section .tab-item {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: rgba(0, 0, 0, 0.65);
            transition: all 0.3s;
        }
        .commission-section .tab-item:hover,
        .exchange-section .tab-item:hover {
            color: #1890ff;
        }
        .commission-section .tab-item.active,
        .exchange-section .tab-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            font-weight: 500;
        }
        .commission-section .tab-content,
        .exchange-section .tab-content {
            display: none;
        }
        .commission-section .tab-content.active,
        .exchange-section .tab-content.active {
            display: block;
        }
        
        /* 操作栏样式 */
        .commission-section .action-bar,
        .exchange-section .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .commission-section .search-box,
        .exchange-section .search-box {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        .commission-section .search-box input,
        .exchange-section .search-box input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            width: 200px;
        }
        .commission-section .btn,
        .exchange-section .btn {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            border: none;
            font-size: 14px;
            transition: all 0.3s;
        }
        .commission-section .btn-primary,
        .exchange-section .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        .commission-section .btn-primary:hover,
        .exchange-section .btn-primary:hover {
            background-color: #40a9ff;
        }
        .commission-section .btn-default,
        .exchange-section .btn-default {
            background-color: #fff;
            border: 1px solid #d9d9d9;
            color: rgba(0, 0, 0, 0.65);
        }
        .commission-section .btn-default:hover,
        .exchange-section .btn-default:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }
        
        /* 表格样式重写 */
        .commission-section .table-container,
        .exchange-section .table-container {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            overflow: hidden;
        }
        .commission-section .table,
        .exchange-section .table {
            width: 100%;
            border-collapse: collapse;
        }
        .commission-section .table th,
        .exchange-section .table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            border-bottom: 1px solid #e8e8e8;
        }
        .commission-section .table td,
        .exchange-section .table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            color: rgba(0, 0, 0, 0.85);
        }
        .commission-section .table tbody tr:hover,
        .exchange-section .table tbody tr:hover {
            background: #f5f5f5;
        }
        .commission-section .table tbody tr:last-child td,
        .exchange-section .table tbody tr:last-child td {
            border-bottom: none;
        }
        
        /* 状态标签重写 */
        .commission-section .status-tag,
        .exchange-section .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .commission-section .status-standard,
        .exchange-section .status-standard {
            background: #e6f7ff;
            color: #1890ff;
        }
        .commission-section .status-special,
        .exchange-section .status-special {
            background: #fff2e8;
            color: #fa8c16;
        }
        
        /* 操作按钮 */
        .commission-section .action-buttons,
        .exchange-section .action-buttons {
            display: flex;
            gap: 8px;
        }
        .commission-section .btn-link,
        .exchange-section .btn-link {
            background: none;
            border: none;
            color: #1890ff;
            cursor: pointer;
            padding: 4px 8px;
            font-size: 12px;
        }
        .commission-section .btn-link:hover,
        .exchange-section .btn-link:hover {
            color: #40a9ff;
        }
        .commission-section .btn-danger,
        .exchange-section .btn-danger {
            color: #ff4d4f;
        }
        .commission-section .btn-danger:hover,
        .exchange-section .btn-danger:hover {
            color: #ff7875;
        }
        
        /* 分佣简介样式 */
        .commission-section .commission-intro,
        .exchange-section .commission-intro {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 14px;
            color: #52c41a;
        }
        
        /* 菜单收起功能样式 */
        .sidebar.collapsed {
            width: 60px;
        }
        
        .sidebar.collapsed .sidebar-title,
        .sidebar.collapsed .sidebar-subtitle {
            display: none;
        }
        
        .sidebar.collapsed .nav-main-link {
            text-align: center;
            padding: 12px 8px;
            font-size: 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .main-content {
            transition: margin-left 0.3s ease;
        }
        
        .main-content.expanded {
            margin-left: 60px;
        }
        
        .toggle-btn {
            position: absolute;
            top: 15px;
            right: -15px;
            width: 30px;
            height: 30px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            z-index: 1001;
            transition: all 0.3s ease;
        }
        
        .toggle-btn:hover {
            background: #0056b3;
        }
        
        .sidebar {
            transition: width 0.3s ease;
            z-index: 1000;
        }
        
        /* 分页按钮样式 */
        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .pagination-btn:hover {
            background: #f5f5f5;
            border-color: #007bff;
        }
        
        .pagination-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .pagination-btn:disabled {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
            border-color: #dee2e6;
        }
        
        .pagination-btn:disabled:hover {
            background: #f8f9fa;
            border-color: #dee2e6;
        }
    </style>
</head>
<body>
    <!-- 左侧菜单 -->
    <div class="sidebar" id="sidebar">
        <button class="toggle-btn" onclick="toggleSidebar()" id="toggleBtn">«</button>
        <div class="sidebar-header">
            <div class="sidebar-title">代理商管理</div>
            <div class="sidebar-subtitle">Agent Management System</div>
        </div>
        
        <nav class="nav-section">
            <div class="nav-main-item">
                <a class="nav-main-link active" href="#" onclick="showPage('agent-list')" id="menu-agent-list">代理商签约信息</a>
            </div>
            <div class="nav-main-item">
                <a class="nav-main-link" href="#" onclick="showPage('commission-config')" id="menu-commission-config">交易分佣配置</a>
            </div>
            <div class="nav-main-item">
                <a class="nav-main-link" href="#" onclick="showPage('exchange-rate-config')" id="menu-exchange-rate-config">汇率返点配置</a>
            </div>
            <div class="nav-main-item">
                <a class="nav-main-link" href="#" onclick="showPage('policy-package')" id="menu-policy-package">代理政策管理</a>
            </div>
        </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content" id="mainContent">
        <!-- 代理商签约信息页面 -->
        <div id="agent-list" class="page-content active">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题和操作按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <h3 style="font-size: 20px; font-weight: 600; color: #333; margin: 0;">代理商签约信息</h3>
                    <div style="display: flex; gap: 10px;">
                        <a href="psp-agent-create-simple.html" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer; text-decoration: none; display: inline-block;">新增</a>
                        <button style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">导出</button>
                    </div>
                </div>

                <!-- 搜索筛选区域 -->
                <div style="margin-bottom: 30px;">
                    <!-- 第一行搜索条件 -->
                    <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px; flex-wrap: wrap;">
                        <div style="display: flex; flex-direction: column; min-width: 200px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">代理商名称：</label>
                            <input type="text" placeholder="请输入代理商名称" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 200px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">代理商号码：</label>
                            <input type="text" placeholder="请输入代理商号码" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">状态：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部状态</option>
                                <option>已激活</option>
                                <option>未激活</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">激活状态：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部状态</option>
                                <option>已激活</option>
                                <option>未激活</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">销售：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部销售</option>
                                <option>张三</option>
                                <option>李四</option>
                            </select>
                        </div>
                    </div>

                    <!-- 第二行搜索条件 -->
                    <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">创建时间：</label>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <input type="date" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <span style="color: #666;">至</span>
                                <input type="date" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                            </div>
                        </div>
                    </div>

                    <!-- 查询按钮 -->
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">查询</button>
                        <button style="background: #6c757d; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">重置</button>
                    </div>
                </div>

                <!-- 表格 -->
                <div style="border: 1px solid #e0e0e0; border-radius: 8px; overflow-x: auto; overflow-y: hidden;">
                    <table style="min-width: 1600px; border-collapse: collapse; table-layout: fixed;">
                        <colgroup>
                            <col style="width: 250px;"> <!-- 代理商信息 -->
                            <col style="width: 220px;"> <!-- 管理员信息 -->
                            <col style="width: 220px;"> <!-- 签约信息 -->
                            <col style="width: 320px;"> <!-- 激活信息 -->
                            <col style="width: 200px;"> <!-- 结算信息 -->
                            <col style="width: 150px;"> <!-- 所属销售 -->
                            <col style="width: 240px;"> <!-- 操作 -->
                        </colgroup>
                        <thead>
                            <tr style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">代理商信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">管理员信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">签约信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">激活信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">结算信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">所属销售</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">时间</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">EUREWAX001</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">欧瑞华科技有限公司</div>
                                    <div style="margin-top: 4px; display: flex; align-items: center; gap: 6px;">
                                        <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">企业</span>
                                        <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">A级</span>
                                    </div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">名称：张三</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">邮箱：<EMAIL></div>
                                    <div style="color: #6c757d; font-size: 12px;">手机：+86 138****8888</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">签约截止：2025-01-15</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">合同编号：*********</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">生效中</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; min-width: 200px; max-width: 280px;">
                                    <div style="display: flex; align-items: center; margin-bottom: 2px; white-space: nowrap;">
                                        <span style="color: #666; font-size: 11px; margin-right: 4px;">代理商登录：</span>
                                        <span style="color: #007bff; font-size: 11px; overflow: hidden; text-overflow: ellipsis; max-width: 140px; cursor: pointer;" title="https://agent.eurewax.com/register?code=EUREWAX001" onclick="window.open('https://agent.eurewax.com/register?code=EUREWAX001')">https://agent.eurewax.com/register?code=EUREWAX001</span>
                                        <button onclick="copyToClipboard('https://agent.eurewax.com/register?code=EUREWAX001')" style="margin-left: 4px; padding: 1px 4px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 3px; font-size: 10px; color: #6c757d; cursor: pointer;" title="复制链接">复制</button>
                                    </div>
                                    <div style="display: flex; align-items: center; margin-bottom: 2px; white-space: nowrap;">
                                        <span style="color: #666; font-size: 11px; margin-right: 4px;">商户注册：</span>
                                        <span style="color: #007bff; font-size: 11px; overflow: hidden; text-overflow: ellipsis; max-width: 140px; cursor: pointer;" title="https://merchant.eurewax.com/register?agent=EUREWAX001" onclick="window.open('https://merchant.eurewax.com/register?agent=EUREWAX001')">https://merchant.eurewax.com/register?agent=EUREWAX001</span>
                                        <button onclick="copyToClipboard('https://merchant.eurewax.com/register?agent=EUREWAX001')" style="margin-left: 4px; padding: 1px 4px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 3px; font-size: 10px; color: #6c757d; cursor: pointer;" title="复制链接">复制</button>
                                    </div>
                                    <div style="margin-top: 2px;">
                                        <span style="display: inline-block; padding: 2px 6px; background: #d4edda; color: #155724; border-radius: 10px; font-size: 10px; font-weight: 600;">已激活</span>
                                    </div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">结算币种：USD</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">结汇日：每月20日</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">普通发票</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">李销售</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">创建时间：2024-01-01</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">更新时间：2024-01-01</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>

                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">EUREWAX002</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">深圳跨境电商有限公司</div>
                                    <div style="margin-top: 4px; display: flex; align-items: center; gap: 6px;">
                                        <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">企业</span>
                                        <span style="display: inline-block; padding: 2px 6px; background: #fff3e0; color: #f57c00; border-radius: 10px; font-size: 10px; font-weight: 600;">B级</span>
                                    </div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">名称：王五</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">邮箱：<EMAIL></div>
                                    <div style="color: #6c757d; font-size: 12px;">手机：+86 139****9999</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">签约截止：2025-02-20</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">合同编号：*********</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">已过期</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; min-width: 200px; max-width: 280px;">
                                    <div style="display: flex; align-items: center; margin-bottom: 2px; white-space: nowrap;">
                                        <span style="color: #666; font-size: 11px; margin-right: 4px;">代理商登录：</span>
                                        <span style="color: #007bff; font-size: 11px; overflow: hidden; text-overflow: ellipsis; max-width: 140px; cursor: pointer;" title="https://agent.eurewax.com/register?code=EUREWAX002" onclick="window.open('https://agent.eurewax.com/register?code=EUREWAX002')">https://agent.eurewax.com/register?code=EUREWAX002</span>
                                        <button onclick="copyToClipboard('https://agent.eurewax.com/register?code=EUREWAX002')" style="margin-left: 4px; padding: 1px 4px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 3px; font-size: 10px; color: #6c757d; cursor: pointer;" title="复制链接">复制</button>
                                    </div>
                                    <div style="display: flex; align-items: center; margin-bottom: 2px; white-space: nowrap;">
                                        <span style="color: #666; font-size: 11px; margin-right: 4px;">商户注册：</span>
                                        <span style="color: #007bff; font-size: 11px; overflow: hidden; text-overflow: ellipsis; max-width: 140px; cursor: pointer;" title="https://merchant.eurewax.com/register?agent=EUREWAX002" onclick="window.open('https://merchant.eurewax.com/register?agent=EUREWAX002')">https://merchant.eurewax.com/register?agent=EUREWAX002</span>
                                        <button onclick="copyToClipboard('https://merchant.eurewax.com/register?agent=EUREWAX002')" style="margin-left: 4px; padding: 1px 4px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 3px; font-size: 10px; color: #6c757d; cursor: pointer;" title="复制链接">复制</button>
                                    </div>
                                    <div style="margin-top: 2px;">
                                        <span style="display: inline-block; padding: 2px 6px; background: #fff3cd; color: #856404; border-radius: 10px; font-size: 10px; font-weight: 600;">待激活</span>
                                    </div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">结算币种：EUR</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">结汇日：每月15日</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">无发票</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">赵销售</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">创建时间：2024-01-01</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">更新时间：2024-01-01</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页控件 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 0 20px;">
                    <div style="color: #666; font-size: 14px;">
                        显示第 <span id="currentStart">1</span>-<span id="currentEnd">3</span> 条，共 <span id="totalRecords">15</span> 条记录
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <button class="pagination-btn" onclick="goToPage(1)" id="firstBtn">
                            <span>首页</span>
                        </button>
                        <button class="pagination-btn" onclick="previousPage()" id="prevBtn">
                            <span>上一页</span>
                        </button>
                        <div style="display: flex; gap: 5px;" id="pageNumbers">
                            <button class="pagination-btn active" onclick="goToPage(1)">1</button>
                            <button class="pagination-btn" onclick="goToPage(2)">2</button>
                            <button class="pagination-btn" onclick="goToPage(3)">3</button>
                            <button class="pagination-btn" onclick="goToPage(4)">4</button>
                            <button class="pagination-btn" onclick="goToPage(5)">5</button>
                        </div>
                        <button class="pagination-btn" onclick="nextPage()" id="nextBtn">
                            <span>下一页</span>
                        </button>
                        <button class="pagination-btn" onclick="goToPage(5)" id="lastBtn">
                            <span>末页</span>
                        </button>
                        <div style="display: flex; align-items: center; gap: 5px; margin-left: 15px;">
                            <span style="color: #666; font-size: 14px;">每页</span>
                            <select style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;" onchange="changePageSize(this.value)">
                                <option value="10">10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span style="color: #666; font-size: 14px;">条</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 底价分佣配置页面 -->
        <div id="commission-config" class="page-content commission-section" style="display:none;">
            <div class="container">
                <div class="page-header">
                    <div class="breadcrumb">
                        <a href="#">代理商管理</a> / 分佣管理
                    </div>
                    <div class="page-title">分佣管理</div>
                    <div class="page-subtitle">管理代理商的交易分佣配置和规则</div>
                </div>
                
                <div class="tab-container">
                    <div class="tab-header">
                        <div class="tab-item active" onclick="switchTab('commission', 'base-price')">底价分佣</div>
                        <div class="tab-item" onclick="switchTab('commission', 'fixed-ratio')">固定比例分佣</div>
                    </div>
                    
                    <!-- 底价分佣Tab -->
                    <div id="commission-base-price" class="tab-content active">
                        <div class="commission-intro">
                            <strong>底价分佣简介：</strong>代理商可以在系统设定的底价基础上加点，向商户收取更高费用，差价部分作为代理商的分佣收益。适用于代理商有定价自主权的业务场景。
                        </div>
                        
                        <div class="action-bar">
                            <div class="search-box">
                                <input type="text" placeholder="搜索产品名称">
                                <button class="btn btn-default">搜索</button>
                            </div>
                            <button class="btn btn-primary" onclick="addCommission('base-price')">新增底价分佣</button>
                        </div>
                        
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">分佣简介</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">类型</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">产品名称</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">底价</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">时间</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">A类代理商底价</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于A级代理商的标准底价政策</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">B2C-电商收款</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">电商平台收款业务</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">2.5% + $0.3</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">百分比 + 固定费用</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-15 12:22</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-15 12:22</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">B类代理商底价</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于B级代理商的特殊底价政策</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #fff3cd; color: #856404; border-radius: 12px; font-size: 11px; font-weight: 600;">特殊</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">B2B-外贸收款</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">外贸企业收款业务</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">2.0% + $0.2</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">比例费 + 固定费</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-14 15:20</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-14 15:20</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">C类代理商底价</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于C级代理商的标准底价政策</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">全球付款</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">全球跨境付款业务</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">3.0% </div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">比例费</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-13 12:22</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-13 12:22</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页控件 -->
                        <div class="pagination-container" style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 12px 0; border-top: 1px solid #f1f3f4;">
                            <div style="color: #6c757d; font-size: 13px;">
                                显示第 1-3 条，共 3 条记录
                            </div>
                            <div class="pagination" style="display: flex; align-items: center; gap: 8px;">
                                <select style="padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 12px; color: #495057;">
                                    <option value="10">10条/页</option>
                                    <option value="20">20条/页</option>
                                    <option value="50">50条/页</option>
                                </select>
                                <button style="padding: 6px 12px; border: 1px solid #ddd; background: #f8f9fa; color: #6c757d; border-radius: 4px; font-size: 12px; cursor: not-allowed;" disabled>上一页</button>
                                <span style="display: flex; gap: 4px;">
                                    <button style="padding: 6px 10px; border: 1px solid #007bff; background: #007bff; color: white; border-radius: 4px; font-size: 12px;">1</button>
                                </span>
                                <button style="padding: 6px 12px; border: 1px solid #ddd; background: #f8f9fa; color: #6c757d; border-radius: 4px; font-size: 12px; cursor: not-allowed;" disabled>下一页</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 固定比例分佣Tab -->
                    <div id="commission-fixed-ratio" class="tab-content">
                        <div class="commission-intro">
                            <strong>固定比例分佣简介：</strong>代理商按照固定比例从商户交易中获得分佣，比例由系统预设，适用于标准化分佣场景。
                        </div>
                        
                        <div class="action-bar">
                            <div class="search-box">
                                <input type="text" placeholder="搜索产品名称">
                                <button class="btn btn-default">搜索</button>
                            </div>
                            <button class="btn btn-primary" onclick="addCommission('fixed-ratio')">新增固定比例分佣</button>
                        </div>
                        
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">分佣简介</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">类型</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">产品名称</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">分佣比例</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">时间</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">标准代理商分佣</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于标准代理商的固定比例分佣</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">B2C-电商收款</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">电商平台收款业务</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">15%</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">固定比例</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-15 10:30</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-15 10:30</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">高级代理商分佣</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于高级代理商的特殊比例分佣</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #fff3cd; color: #856404; border-radius: 12px; font-size: 11px; font-weight: 600;">特殊</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">B2B-外贸收款</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">外贸企业收款业务</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">20%</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">固定比例</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-14 15:20</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-14 15:20</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- 分页控件 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 15px 0; border-top: 1px solid #e9ecef;">
                            <div style="color: #6c757d; font-size: 13px;">
                                显示第 1-2 条，共 2 条记录
                            </div>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span style="color: #6c757d; font-size: 13px;">每页显示</span>
                                    <select style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px; background: white;">
                                        <option value="10" selected>10</option>
                                        <option value="20">20</option>
                                        <option value="50">50</option>
                                    </select>
                                    <span style="color: #6c757d; font-size: 13px;">条</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <button style="padding: 6px 12px; border: 1px solid #ddd; background: #f8f9fa; color: #6c757d; border-radius: 4px; font-size: 13px; cursor: not-allowed;" disabled>
                                        上一页
                                    </button>
                                    <button style="padding: 6px 12px; border: 1px solid #007bff; background: #007bff; color: white; border-radius: 4px; font-size: 13px; cursor: pointer;">
                                        1
                                    </button>
                                    <button style="padding: 6px 12px; border: 1px solid #ddd; background: #f8f9fa; color: #6c757d; border-radius: 4px; font-size: 13px; cursor: not-allowed;" disabled>
                                        下一页
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 汇率返点配置页面 -->
        <div id="exchange-rate-config" class="page-content commission-section" style="display:none;">
            <div class="container">
                <div class="page-header">
                    <div class="breadcrumb">
                        <a href="#">代理商管理</a> / 汇率返点管理
                    </div>
                    <div class="page-title">汇率返点管理</div>
                    <div class="page-subtitle">管理代理商的汇率加点和返点配置</div>
                </div>
                
                <div class="tab-container">
                    <div class="tab-header">
                        <div class="tab-item active" onclick="switchTab('exchange', 'base-rate')">底价汇率</div>
                        <div class="tab-item" onclick="switchTab('exchange', 'fixed-rebate')">固定返点</div>
                    </div>
                    
                    <!-- 底价汇率Tab -->
                    <div id="exchange-base-rate" class="tab-content active">
                        <div class="commission-intro">
                            <strong>底价汇率简介：</strong>代理商可以在系统底价汇率基础上加点，向商户提供汇率服务并获得差价收益。加点越高，代理商收益越大，但商户成本也越高。
                        </div>
                        
                        <div class="action-bar">
                            <div class="search-box">
                                <input type="text" placeholder="搜索货币对">
                                <button class="btn btn-default">搜索</button>
                            </div>
                            <button class="btn btn-primary" onclick="addExchangeRate('base-rate')">新增底价汇率</button>
                        </div>
                        
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr style="background: #f8f9fa; border-bottom: 2px solid #e9ecef;">
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: left;">返点简介</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: center;">类型</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: left;">货币对</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: left;">加点</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: left;">更新时间</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: center;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">A类代理返点</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于A级代理商的汇率返点</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">USD/CNY</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">美元对人民币</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">+0.05</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">加点比例</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-15 10:30</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-15 10:30</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">B类代理返点</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于B级代理商的特殊返点</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #fff3cd; color: #856404; border-radius: 12px; font-size: 11px; font-weight: 600;">特殊</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">EUR/CNY</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">欧元对人民币</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">+0.08</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">加点比例</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-14 15:20</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-14 15:20</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">C类代理返点</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于C级代理商的标准返点</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">GBP/CNY</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">英镑对人民币</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">+0.10</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">加点比例</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-13 09:15</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-13 09:15</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- 分页控件 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 15px 0; border-top: 1px solid #e9ecef;">
                            <div style="color: #6c757d; font-size: 13px;">
                                显示第 1-3 条，共 3 条记录
                            </div>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span style="color: #6c757d; font-size: 13px;">每页显示</span>
                                    <select style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px; background: white;">
                                        <option value="10" selected>10</option>
                                        <option value="20">20</option>
                                        <option value="50">50</option>
                                    </select>
                                    <span style="color: #6c757d; font-size: 13px;">条</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <button style="padding: 6px 12px; border: 1px solid #ddd; background: #f8f9fa; color: #6c757d; border-radius: 4px; font-size: 13px; cursor: not-allowed;" disabled>
                                        上一页
                                    </button>
                                    <button style="padding: 6px 12px; border: 1px solid #007bff; background: #007bff; color: white; border-radius: 4px; font-size: 13px; cursor: pointer;">
                                        1
                                    </button>
                                    <button style="padding: 6px 12px; border: 1px solid #ddd; background: #f8f9fa; color: #6c757d; border-radius: 4px; font-size: 13px; cursor: not-allowed;" disabled>
                                        下一页
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 固定返点Tab -->
                    <div id="exchange-fixed-rebate" class="tab-content">
                        <div class="commission-intro">
                            <strong>固定返点简介：</strong>代理商按照预设的固定返点比例从商户交易中获得返点，无加点权。适用于标准化运营和批量管理的业务场景。
                        </div>
                        
                        <div class="action-bar">
                            <div class="search-box">
                                <input type="text" placeholder="搜索货币对">
                                <button class="btn btn-default">搜索</button>
                            </div>
                            <button class="btn btn-primary" onclick="addExchangeRate('fixed-rebate')">新增固定返点</button>
                        </div>
                        
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr style="background: #f8f9fa; border-bottom: 2px solid #e9ecef;">
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: left;">返点简介</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: center;">类型</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: left;">货币对</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: left;">返点比例</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: left;">更新时间</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: center;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">A类代理返点</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于A级代理商的固定返点</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">USD/CNY</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">美元对人民币</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">0.10%</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">返点比例</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-15 10:30</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-15 10:30</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">B类代理返点</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于B级代理商的特殊返点</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #fff3cd; color: #856404; border-radius: 12px; font-size: 11px; font-weight: 600;">特殊</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">EUR/CNY</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">欧元对人民币</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">0.08%</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">返点比例</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-14 15:20</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-14 15:20</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- 分页控件 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 15px 0; border-top: 1px solid #e9ecef;">
                            <div style="color: #6c757d; font-size: 13px;">
                                显示第 1-2 条，共 2 条记录
                            </div>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span style="color: #6c757d; font-size: 13px;">每页显示</span>
                                    <select style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px; background: white;">
                                        <option value="10" selected>10</option>
                                        <option value="20">20</option>
                                        <option value="50">50</option>
                                    </select>
                                    <span style="color: #6c757d; font-size: 13px;">条</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <button style="padding: 6px 12px; border: 1px solid #ddd; background: #f8f9fa; color: #6c757d; border-radius: 4px; font-size: 13px; cursor: not-allowed;" disabled>
                                        上一页
                                    </button>
                                    <button style="padding: 6px 12px; border: 1px solid #007bff; background: #007bff; color: white; border-radius: 4px; font-size: 13px; cursor: pointer;">
                                        1
                                    </button>
                                    <button style="padding: 6px 12px; border: 1px solid #ddd; background: #f8f9fa; color: #6c757d; border-radius: 4px; font-size: 13px; cursor: not-allowed;" disabled>
                                        下一页
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 底价代理政策页面 -->
        <div id="base-price-policy" class="page-content">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题和操作按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <h3 style="font-size: 20px; font-weight: 600; color: #333; margin: 0;">底价代理政策</h3>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">新增政策</button>
                        <button style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">导出</button>
                    </div>
                </div>

                <!-- 搜索筛选区域 -->
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px; flex-wrap: wrap;">
                        <div style="display: flex; flex-direction: column; min-width: 200px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">政策名称：</label>
                            <input type="text" placeholder="请输入政策名称" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">政策类型：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部类型</option>
                                <option>标准政策</option>
                                <option>特殊政策</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">产品名称：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部产品</option>
                                <option>电商收款</option>
                                <option>外贸收款</option>
                                <option>物流收款</option>
                                <option>广告联盟收款</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">查询</button>
                        <button style="background: #6c757d; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">重置</button>
                    </div>
                </div>

                <!-- 表格 -->
                <div style="border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden; background: white;">
                    <table style="width: 100%; border-collapse: collapse; table-layout: fixed;">
                        <colgroup>
                            <col style="width: 200px;"> <!-- 政策名称 -->
                            <col style="width: 250px;"> <!-- 政策简介 -->
                            <col style="width: 120px;"> <!-- 政策类型 -->
                            <col style="width: 150px;"> <!-- 产品名称 -->
                            <col style="width: 300px;"> <!-- 产品底价 -->
                            <col style="width: 150px;"> <!-- 更新时间 -->
                            <col style="width: 120px;"> <!-- 操作 -->
                        </colgroup>
                        <thead>
                            <tr style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策名称</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策简介</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策类型</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">产品名称</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">产品底价</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">更新时间</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">A级电商收款标准政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">适用于A级代理商的电商收款业务标准底价政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">电商收款</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">全球收款→HKD→DBS: 0.20% | 本地收款→HKD→其他: 0.45%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">默认最高2条，微信...</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-15</div>
                                    <div style="color: #6c757d; font-size: 11px;">10:30</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">B级外贸收款标准政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">适用于B级代理商的外贸收款业务标准底价政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">外贸收款</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">全球收款→HKD→DBS: 0.25% | 本地收款→HKD→其他: 0.5%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">默认最高2条，微信...</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-14</div>
                                    <div style="color: #6c757d; font-size: 11px;">09:20</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">上海润泽科技特殊政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">专为上海润泽科技定制的电商收款特殊底价政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #fff3cd; color: #856404; border-radius: 12px; font-size: 11px; font-weight: 600;">特殊政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">电商收款</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">全球收款→HKD→DBS: 0.15% | 本地收款→HKD→其他: 0.35%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">特殊优惠价格</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-10</div>
                                    <div style="color: #6c757d; font-size: 11px;">14:25</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


        <!-- 汇率返点代理政策页面 -->
        <div id="exchange-rebate-policy" class="page-content">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题和操作按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <h3 style="font-size: 20px; font-weight: 600; color: #333; margin: 0;">汇率返点代理政策</h3>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">新增政策</button>
                        <button style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">导出</button>
                    </div>
                </div>

                <!-- 搜索筛选区域 -->
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px; flex-wrap: wrap;">
                        <div style="display: flex; flex-direction: column; min-width: 200px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">政策名称：</label>
                            <input type="text" placeholder="请输入政策名称" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">政策类型：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部类型</option>
                                <option>标准政策</option>
                                <option>特殊政策</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">货币对：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部货币对</option>
                                <option>USD→CNH</option>
                                <option>EUR→USD</option>
                                <option>GBP→USD</option>
                                <option>USD→HKD</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">查询</button>
                        <button style="background: #6c757d; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">重置</button>
                    </div>
                </div>

                <!-- 表格 -->
                <div style="border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden; background: white;">
                    <table style="width: 100%; border-collapse: collapse; table-layout: fixed;">
                        <colgroup>
                            <col style="width: 200px;"> <!-- 政策名称 -->
                            <col style="width: 250px;"> <!-- 政策简介 -->
                            <col style="width: 120px;"> <!-- 政策类型 -->
                            <col style="width: 300px;"> <!-- 汇率返点 -->
                            <col style="width: 150px;"> <!-- 更新时间 -->
                            <col style="width: 120px;"> <!-- 操作 -->
                        </colgroup>
                        <thead>
                            <tr style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策名称</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策简介</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策类型</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">汇率返点</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">更新时间</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">A级标准汇率返点政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">适用于A级代理商的标准汇率返点政策，返点比例50%</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">USD→CNH: 50% | EUR→USD: 50% | GBP→USD: 50%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">标准返点比例</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-15</div>
                                    <div style="color: #6c757d; font-size: 11px;">10:30</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>

                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">B级标准汇率返点政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">适用于B级代理商的标准汇率返点政策，返点比例40%</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">USD→CNH: 40% | EUR→USD: 40% | GBP→USD: 40%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">标准返点比例</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-14</div>
                                    <div style="color: #6c757d; font-size: 11px;">09:20</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">上海润泽科技特殊返点政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">专为上海润泽科技定制的USD→CNH特殊汇率返点政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #fff3cd; color: #856404; border-radius: 12px; font-size: 11px; font-weight: 600;">特殊政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">USD→CNH: 65% | EUR→USD: 55%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">特殊优惠返点</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-10</div>
                                    <div style="color: #6c757d; font-size: 11px;">14:25</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>



        <!-- 混合代理政策页面 -->
        <div id="hybrid-policy" class="page-content">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题和操作按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <h3 style="font-size: 20px; font-weight: 600; color: #333; margin: 0;">混合代理政策</h3>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">新增政策</button>
                        <button style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">导出</button>
                    </div>
                </div>
                <div style="text-align: center; padding: 40px; color: #666;">
                    <div style="font-size: 16px; margin-bottom: 10px;">混合代理政策</div>
                    <div style="font-size: 14px;">同时使用底价和汇率返点的混合政策模式</div>
                </div>
            </div>
        </div>

        <!-- 代理政策管理页面 -->
        <div id="policy-package" class="page-content" style="display:none;">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题 -->
                <div style="margin-bottom: 30px;">
                    <h3 style="font-size: 24px; font-weight: 600; color: #333; margin: 0 0 10px 0;">代理商政策包管理</h3>
                    <p style="color: #666; margin: 0 0 15px 0; font-size: 14px;">管理代理商政策包，包含8个产品的完整计费配置，支持标准政策包和特殊政策包</p>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: white; color: #007bff; border: 1px solid #007bff; padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer; transition: all 0.3s;" onclick="demonstratePackageLogic()">查看政策包应用逻辑</button>
                        <button style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer; transition: all 0.3s;" onclick="createPackage()">新增政策包</button>
                    </div>
                </div>
                
                <!-- 政策包管理内容 -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <!-- A级代理商政策包 -->
                    <div style="border: 2px solid #e9ecef; border-radius: 12px; padding: 20px; transition: all 0.3s; cursor: pointer;" onclick="selectPackage('a-level')" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 4px 12px rgba(0,123,255,0.15)';" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none';">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div style="font-size: 18px; font-weight: 600; color: #333;">A级代理商政策包</div>
                            <span style="padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; background: #d4edda; color: #155724;">标准政策</span>
                        </div>
                        <div style="color: #666; font-size: 14px; margin-bottom: 15px; line-height: 1.5;">适用于A级代理商及其所有商户，费率相对较低，适合大型代理商</div>
                        <div style="display: flex; gap: 15px;">
                            <div style="flex: 1;">
                                <div style="font-size: 14px; font-weight: 600; color: #333; margin-bottom: 8px; text-align: center;">交易分佣</div>
                                <div style="display: flex; flex-direction: column; gap: 6px;">
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">外贸收款</span>
                                            <span style="color: #666; font-size: 11px;">底价分佣</span>
                                        </div>
                                        <span style="color: #007bff; font-weight: 600;">0.8%</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">跨境电商</span>
                                            <span style="color: #666; font-size: 11px;">底价分佣</span>
                                        </div>
                                        <span style="color: #007bff; font-weight: 600;">0.9%</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">数字货币</span>
                                            <span style="color: #666; font-size: 11px;">固定比例</span>
                                        </div>
                                        <span style="color: #007bff; font-weight: 600;">1.5%</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">外汇交易</span>
                                            <span style="color: #666; font-size: 11px;">底价分佣</span>
                                        </div>
                                        <span style="color: #007bff; font-weight: 600;">0.7%</span>
                                    </div>
                                </div>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: 14px; font-weight: 600; color: #333; margin-bottom: 8px; text-align: center;">汇率返点</div>
                                <div style="display: flex; flex-direction: column; gap: 6px;">
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">USD/CNY</span>
                                            <span style="color: #666; font-size: 11px;">底价汇率</span>
                                        </div>
                                        <span style="color: #28a745; font-weight: 600;">+0.05</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">EUR/CNY</span>
                                            <span style="color: #666; font-size: 11px;">底价汇率</span>
                                        </div>
                                        <span style="color: #28a745; font-weight: 600;">+0.08</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">GBP/CNY</span>
                                            <span style="color: #666; font-size: 11px;">固定返点</span>
                                        </div>
                                        <span style="color: #28a745; font-weight: 600;">0.15%</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">JPY/CNY</span>
                                            <span style="color: #666; font-size: 11px;">底价汇率</span>
                                        </div>
                                        <span style="color: #28a745; font-weight: 600;">+0.03</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div style="display: flex; gap: 10px; margin-top: 20px;">
                            <button style="background: white; color: #007bff; border: 1px solid #007bff; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">编辑</button>
                            <button style="background: white; color: #007bff; border: 1px solid #007bff; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">复制</button>
                        </div>
                    </div>
                    
                    <!-- 新增政策包 -->
                    <div style="border: 2px dashed #dee2e6; border-radius: 12px; padding: 40px; text-align: center; color: #6c757d; cursor: pointer; transition: all 0.3s;" onclick="createPackage()" onmouseover="this.style.borderColor='#007bff'; this.style.color='#007bff';" onmouseout="this.style.borderColor='#dee2e6'; this.style.color='#6c757d';">
                        <div style="font-size: 48px; margin-bottom: 15px;">+</div>
                        <div>新增政策包</div>
                        <div style="font-size: 12px; margin-top: 5px;">创建新的标准或特殊政策包</div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script>
        // 页面切换功能
        function showPage(pageId) {
            console.log('Switching to page:', pageId);
            
            // 隐藏所有页面
            var pages = document.querySelectorAll('.page-content');
            pages.forEach(function(page) {
                page.classList.remove('active');
                page.style.display = 'none';
            });
            
            // 显示目标页面
            var targetPage = document.getElementById(pageId);
            if(targetPage) {
                targetPage.classList.add('active');
                targetPage.style.display = 'block';
                console.log('Page shown:', pageId);
            } else {
                console.error('Page not found:', pageId);
            }
            
            // 菜单高亮处理 - 移除所有活跃状态
            var allMenuLinks = document.querySelectorAll('.nav-main-link');
            allMenuLinks.forEach(function(link) {
                link.classList.remove('active');
            });
            
            // 设置当前菜单为活跃状态
            var menuMapping = {
                'agent-list': 'menu-agent-list',
                'commission-config': 'menu-commission-config',
                'exchange-rate-config': 'menu-exchange-rate-config',
                'policy-package': 'menu-policy-package'
            };
            
            var activeMenuId = menuMapping[pageId];
            if(activeMenuId) {
                var activeMenu = document.getElementById(activeMenuId);
                if(activeMenu) {
                    activeMenu.classList.add('active');
                    console.log('Menu activated:', activeMenuId);
                }
            }
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing page...');
            // 默认显示代理商签约信息页面
            showPage('agent-list');
        });

        // Tab切换功能
        function switchTab(module, tabName) {
            // 根据模块名找到对应的容器
            let container;
            if (module === 'commission') {
                container = document.querySelector('#commission-config');
            } else if (module === 'exchange') {
                container = document.querySelector('#exchange-rate-config');
            } else {
                return;
            }
            
            // 隐藏该模块下所有tab内容
            const tabContents = container.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除该模块下所有tab按钮的活跃状态
            const tabItems = container.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示选中的tab内容
            const targetTab = document.getElementById(module + '-' + tabName);
            if (targetTab) {
                targetTab.classList.add('active');
            }
            
            // 设置对应按钮为活跃状态
            event.target.classList.add('active');
        }
        
        // 新增分佣功能
        function addCommission(type) {
            if (type === 'base-price') {
                alert('新增底价分佣功能待开发');
            } else if (type === 'fixed-ratio') {
                alert('新增固定比例分佣功能待开发');
            }
        }
        
        // 新增汇率功能
        function addExchangeRate(type) {
            if (type === 'base-rate') {
                alert('新增底价汇率功能待开发');
            } else if (type === 'fixed-rebate') {
                alert('新增固定返点功能待开发');
            }
        }

        // 复制链接功能
        function copyToClipboard(text) {
            if (navigator.clipboard && window.isSecureContext) {
                // 使用现代 Clipboard API
                navigator.clipboard.writeText(text).then(function() {
                    // 显示复制成功提示
                    showCopySuccess();
                }).catch(function(err) {
                    console.error('复制失败:', err);
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                // 降级方案
                fallbackCopyTextToClipboard(text);
            }
        }

        // 降级复制方案
        function fallbackCopyTextToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            try {
                var successful = document.execCommand('copy');
                if (successful) {
                    showCopySuccess();
                } else {
                    alert('复制失败，请手动复制');
                }
            } catch (err) {
                alert('复制失败，请手动复制');
            }
            document.body.removeChild(textArea);
        }

        // 显示复制成功提示
        function showCopySuccess() {
            // 创建提示元素
            var toast = document.createElement('div');
            toast.textContent = '链接已复制到剪贴板';
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 10px 15px;
                border-radius: 4px;
                font-size: 14px;
                z-index: 10000;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                transition: opacity 0.3s ease;
            `;
            document.body.appendChild(toast);
            
            // 3秒后自动消失
            setTimeout(function() {
                toast.style.opacity = '0';
                setTimeout(function() {
                    if (toast.parentNode) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
        
        // 菜单收起功能
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const toggleBtn = document.getElementById('toggleBtn');
            
            if (sidebar.classList.contains('collapsed')) {
                // 展开菜单
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
                toggleBtn.innerHTML = '«';
            } else {
                // 收起菜单
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
                toggleBtn.innerHTML = '»';
            }
        }
        
        // 分页功能变量
        let currentPage = 1;
        let pageSize = 10;
        let totalRecords = 15; // 模拟总记录数
        let totalPages = Math.ceil(totalRecords / pageSize);
        
        // 跳转到指定页面
        function goToPage(page) {
            if (page < 1 || page > totalPages) return;
            
            currentPage = page;
            updatePagination();
            loadPageData();
        }
        
        // 上一页
        function previousPage() {
            if (currentPage > 1) {
                goToPage(currentPage - 1);
            }
        }
        
        // 下一页
        function nextPage() {
            if (currentPage < totalPages) {
                goToPage(currentPage + 1);
            }
        }
        
        // 更改每页显示数量
        function changePageSize(newSize) {
            pageSize = parseInt(newSize);
            totalPages = Math.ceil(totalRecords / pageSize);
            currentPage = 1;
            updatePagination();
            loadPageData();
        }
        
        // 更新分页显示
        function updatePagination() {
            // 更新记录信息
            const start = (currentPage - 1) * pageSize + 1;
            const end = Math.min(currentPage * pageSize, totalRecords);
            
            document.getElementById('currentStart').textContent = start;
            document.getElementById('currentEnd').textContent = end;
            document.getElementById('totalRecords').textContent = totalRecords;
            
            // 更新按钮状态
            document.getElementById('firstBtn').disabled = currentPage === 1;
            document.getElementById('prevBtn').disabled = currentPage === 1;
            document.getElementById('nextBtn').disabled = currentPage === totalPages;
            document.getElementById('lastBtn').disabled = currentPage === totalPages;
            
            // 更新页码按钮
            updatePageNumbers();
        }
        
        // 更新页码按钮
        function updatePageNumbers() {
            const pageNumbersDiv = document.getElementById('pageNumbers');
            pageNumbersDiv.innerHTML = '';
            
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
            
            // 调整起始页面
            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const btn = document.createElement('button');
                btn.className = 'pagination-btn' + (i === currentPage ? ' active' : '');
                btn.textContent = i;
                btn.onclick = () => goToPage(i);
                pageNumbersDiv.appendChild(btn);
            }
        }
        
        // 加载页面数据（模拟）
        function loadPageData() {
            // 这里可以添加实际的数据加载逻辑
            console.log(`加载第 ${currentPage} 页数据，每页 ${pageSize} 条`);
        }
        
        // 初始化分页
        document.addEventListener('DOMContentLoaded', function() {
            updatePagination();
        });
        
        // 代理政策管理相关函数
        function selectPackage(packageId) {
            // 移除所有选中状态
            document.querySelectorAll('.package-card').forEach(card => {
                card.style.borderColor = '#e9ecef';
                card.style.background = 'white';
            });
            
            // 设置当前选中的卡片样式
            event.currentTarget.style.borderColor = '#007bff';
            event.currentTarget.style.background = '#f8f9ff';
            
            console.log('选中政策包:', packageId);
        }
        
        function createPackage() {
            const packageName = prompt('请输入新政策包名称:');
            if (packageName) {
                alert(`创建政策包: ${packageName}\n跳转到政策包配置页面`);
            }
        }
        
        function demonstratePackageLogic() {
            alert('政策包应用逻辑演示功能');
        }
    </script>
</body>
</html>
