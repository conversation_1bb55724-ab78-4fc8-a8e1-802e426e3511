<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PSP代理商管理系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; display: flex; min-height: 100vh; }

        /* 左侧菜单 */
        .sidebar { width: 280px; background: #2c3e50; color: white; position: fixed; height: 100vh; overflow-y: auto; }
        .sidebar-header { padding: 20px; border-bottom: 1px solid #34495e; background: #1a252f; }
        .sidebar-title { font-size: 18px; font-weight: 600; color: #ecf0f1; margin-bottom: 5px; }
        .sidebar-subtitle { font-size: 12px; color: #95a5a6; }
        .nav-section { padding: 20px 0; }
        .nav-main-item { margin-bottom: 5px; }
        .nav-main-link { display: flex; align-items: center; justify-content: space-between; padding: 12px 20px; color: #ecf0f1; text-decoration: none; font-weight: 500; transition: all 0.3s; border-left: 3px solid transparent; }
        .nav-main-link:hover, .nav-main-link.active { background: #34495e; border-left-color: #3498db; color: #3498db; }
        .nav-arrow { font-size: 12px; transition: transform 0.3s; }
        .nav-submenu { max-height: 0; overflow: hidden; transition: max-height 0.3s ease; background: #1a252f; }
        .nav-submenu.active { max-height: 500px; }
        .nav-sub-link { display: block; padding: 10px 20px 10px 40px; color: #bdc3c7; text-decoration: none; font-size: 14px; transition: all 0.3s; border-left: 3px solid transparent; }
        .nav-sub-link:hover, .nav-sub-link.active { background: #34495e; color: #3498db; border-left-color: #3498db; }

        /* 主内容区 */
        .main-content { margin-left: 280px; flex: 1; padding: 20px; min-height: 100vh; }
        .page-content { display: none; }
        .page-content.active { display: block; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; margin-bottom: 20px; }
        .card-header { background: #f8f9fa; padding: 20px; border-bottom: 1px solid #dee2e6; display: flex; justify-content: space-between; align-items: center; }
        .card-title { font-size: 20px; font-weight: 600; color: #333; margin: 0; }
        .card-body { padding: 20px; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; text-decoration: none; display: inline-block; text-align: center; transition: all 0.3s; margin-right: 10px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-success { background: #28a745; color: white; }
        .btn-sm { padding: 6px 12px; font-size: 12px; }

        /* Tab样式 */
        .tab-container { margin-bottom: 20px; }
        .tab-nav { display: flex; border-bottom: 2px solid #dee2e6; margin-bottom: 20px; }
        .tab-item { padding: 12px 24px; background: #f8f9fa; border: 1px solid #dee2e6; border-bottom: none; cursor: pointer; font-weight: 500; color: #6c757d; transition: all 0.3s; margin-right: 2px; }
        .tab-item.active { background: white; color: #007bff; border-color: #007bff; border-bottom: 2px solid white; margin-bottom: -2px; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }

        /* 搜索区域 */
        .search-section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .search-grid { display: flex; gap: 15px; align-items: end; flex-wrap: wrap; margin-bottom: 15px; }
        .search-item { display: flex; flex-direction: column; min-width: 150px; }
        .search-item label { font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px; }
        .search-item input, .search-item select { padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; }

        /* 表格样式 */
        .table-container { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .data-table { width: 100%; border-collapse: collapse; table-layout: fixed; }
        .data-table th { background: #f8f9fa; padding: 16px 12px; text-align: left; font-weight: 600; color: #333; border-bottom: 1px solid #dee2e6; font-size: 14px; }
        .data-table td { padding: 16px 12px; border-bottom: 1px solid #eee; font-size: 14px; vertical-align: top; }
        .data-table tr:hover { background: #f8f9fa; }

        /* 操作链接 */
        .action-link { color: #007bff; text-decoration: none; margin-right: 8px; font-size: 13px; transition: color 0.3s; }
        .action-link:hover { color: #0056b3; }
        .action-link.danger { color: #dc3545; }
        .action-link.danger:hover { color: #c82333; }

        /* 分页样式 */
        .pagination-container { display: flex; justify-content: space-between; align-items: center; margin-top: 20px; margin-bottom: 40px; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .pagination-info { color: #666; font-size: 14px; }
        .pagination-controls { display: flex; gap: 5px; }
        .pagination-controls button { padding: 8px 12px; border: 1px solid #ddd; background: white; color: #666; border-radius: 4px; cursor: pointer; transition: all 0.3s; }
        .pagination-controls button:hover { background: #f8f9fa; }
        .pagination-controls button.active { background: #007bff; color: white; border-color: #007bff; }

        /* 多行内容样式 */
        .multi-line-content { font-size: 13px; line-height: 1.6; max-width: 100%; }
        .multi-line-content .line-item { margin-bottom: 2px; }
        .multi-line-content .more-indicator { color: #6c757d; font-size: 12px; }

        /* 状态标签 */
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600; white-space: nowrap; }
        .status-active { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-inactive { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <!-- 左侧菜单 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">PSP代理商管理</div>
            <div class="sidebar-subtitle">Agent Management System</div>
        </div>
        
        <nav class="nav-section">
            <div class="nav-main-item">
                <a href="#" class="nav-main-link active" onclick="toggleSubmenu(this)">
                    <span>代理商管理</span>
                    <span class="nav-arrow">▼</span>
                </a>
                <div class="nav-submenu active">
                    <a href="#" class="nav-sub-link" onclick="showPage('add-agent')">新增代理商</a>
                    <a href="#" class="nav-sub-link active" onclick="showPage('agent-list')">代理商签约信息</a>
                    <a href="#" class="nav-sub-link" onclick="showPage('base-price-policy')">底价代理政策</a>
                    <a href="#" class="nav-sub-link" onclick="showPage('exchange-rebate-policy')">汇率返点代理政策</a>
                    <a href="#" class="nav-sub-link" onclick="showPage('hybrid-policy')">混合代理政策</a>
                </div>
            </div>
        </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 代理商签约信息页面 -->
        <div id="agent-list" class="page-content active">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题和操作按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <h3 style="font-size: 20px; font-weight: 600; color: #333; margin: 0;">代理商签约信息</h3>
                    <div style="display: flex; gap: 10px;">
                        <a href="psp-agent-create.html" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer; text-decoration: none; display: inline-block;">新增</a>
                        <button style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">导出</button>
                    </div>
                </div>

                <!-- 搜索筛选区域 -->
                <div style="margin-bottom: 30px;">
                    <!-- 第一行搜索条件 -->
                    <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px; flex-wrap: wrap;">
                        <div style="display: flex; flex-direction: column; min-width: 200px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">代理商名称：</label>
                            <input type="text" placeholder="请输入代理商名称" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 200px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">代理商号码：</label>
                            <input type="text" placeholder="请输入代理商号码" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">状态：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部状态</option>
                                <option>已激活</option>
                                <option>未激活</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">激活状态：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部状态</option>
                                <option>已激活</option>
                                <option>未激活</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">销售：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部销售</option>
                                <option>张三</option>
                                <option>李四</option>
                            </select>
                        </div>
                    </div>

                    <!-- 第二行搜索条件 -->
                    <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">创建时间：</label>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <input type="date" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <span style="color: #666;">至</span>
                                <input type="date" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                            </div>
                        </div>
                    </div>

                    <!-- 查询按钮 -->
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">查询</button>
                        <button style="background: #6c757d; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">重置</button>
                    </div>
                </div>

                <!-- 表格 -->
                <div style="border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden;">
                    <table style="width: 100%; border-collapse: collapse; table-layout: fixed;">
                        <colgroup>
                            <col style="width: 200px;"> <!-- 代理商信息 -->
                            <col style="width: 200px;"> <!-- 管理员信息 -->
                            <col style="width: 200px;"> <!-- 签约信息 -->
                            <col style="width: 120px;"> <!-- 激活状态 -->
                            <col style="width: 150px;"> <!-- 结算信息 -->
                            <col style="width: 120px;"> <!-- 所属销售 -->
                            <col style="width: 150px;"> <!-- 时间 -->
                            <col style="width: 120px;"> <!-- 操作 -->
                        </colgroup>
                        <thead>
                            <tr style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">代理商信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">管理员信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">签约信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">激活状态</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">结算信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">所属销售</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">时间</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="border-bottom: 1px solid #eee;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="font-weight: 600; color: #333; margin-bottom: 3px;">显示简称</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-bottom: 3px;">AG202501001</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #007bff; color: white; border-radius: 10px; font-size: 11px; font-weight: 600;">A级</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="font-weight: 600; color: #333; margin-bottom: 3px;">张经理</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-bottom: 2px;"><EMAIL></div>
                                    <div style="color: #6c757d; font-size: 12px;">+86 13888888666</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; margin-bottom: 3px;">2025-01-01 ~ 2025-12-31</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #d4edda; color: #155724; border-radius: 10px; font-size: 11px; font-weight: 600;">合约生效中</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #007bff; color: white; border-radius: 12px; font-size: 11px; font-weight: 600;">已激活</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; margin-bottom: 2px;">每月20日 USD</div>
                                    <div style="color: #6c757d; font-size: 12px;">****1234</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; margin-bottom: 3px;">李销售</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #28a745; color: white; border-radius: 10px; font-size: 11px; font-weight: 600;">开发客户</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; margin-bottom: 2px;">创建: 2025-01-01 10:30</div>
                                    <div style="color: #333; font-size: 12px;">更新: 2025-01-10 16:20</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px; transition: background 0.2s;">详情</a>
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px; transition: background 0.2s;">编辑</a>
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="font-weight: 600; color: #333; margin-bottom: 3px;">显示简称</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-bottom: 3px;">AG202501003</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #007bff; color: white; border-radius: 10px; font-size: 11px; font-weight: 600;">B级</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="font-weight: 600; color: #333; margin-bottom: 3px;">王总</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-bottom: 2px;"><EMAIL></div>
                                    <div style="color: #6c757d; font-size: 12px;">+86 13888888888</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; margin-bottom: 3px;">2024-12-01 ~ 2025-11-30</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #fff3cd; color: #856404; border-radius: 10px; font-size: 11px; font-weight: 600;">未激活</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #ffc107; color: #212529; border-radius: 12px; font-size: 11px; font-weight: 600;">未激活</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; margin-bottom: 2px;">每月20日 CNY</div>
                                    <div style="color: #6c757d; font-size: 12px;">****5678</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; margin-bottom: 3px;">张销售</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #17a2b8; color: white; border-radius: 10px; font-size: 11px; font-weight: 600;">无客户</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; margin-bottom: 2px;">创建: 2024-12-15 14:20</div>
                                    <div style="color: #333; font-size: 12px;">更新: 2025-01-08 09:15</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px; transition: background 0.2s;">详情</a>
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px; transition: background 0.2s;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px; transition: background 0.2s;">激活</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 新增代理商页面 -->
        <div id="add-agent" class="page-content">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">新增代理商</h3>
                    <button class="btn" style="background: #6c757d; color: white;" onclick="showPage('agent-list')">返回列表</button>
                </div>
                <div class="card-body">
                    <div style="max-width: 800px;">
                        <h4 style="margin-bottom: 20px; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">基本信息</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">代理商名称 <span style="color: red;">*</span></label>
                                <input type="text" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" placeholder="请输入代理商名称">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">代理商等级 <span style="color: red;">*</span></label>
                                <select style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="">请选择代理商等级</option>
                                    <option value="A">A级（高等级）</option>
                                    <option value="B">B级（中等级）</option>
                                    <option value="C">C级（低等级）</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">联系人 <span style="color: red;">*</span></label>
                                <input type="text" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" placeholder="请输入联系人姓名">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">联系电话 <span style="color: red;">*</span></label>
                                <input type="tel" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" placeholder="请输入联系电话">
                            </div>
                            <div style="grid-column: 1 / -1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">备注</label>
                                <textarea style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; height: 80px;" placeholder="请输入备注信息"></textarea>
                            </div>
                        </div>
                        
                        <div style="text-align: center; padding-top: 20px; border-top: 1px solid #eee;">
                            <button class="btn" style="background: #6c757d; color: white; margin-right: 20px;" onclick="showPage('agent-list')">取消</button>
                            <button class="btn btn-primary">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底价代理政策页面 -->
        <div id="base-price-policy" class="page-content">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题和操作按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <h3 style="font-size: 20px; font-weight: 600; color: #333; margin: 0;">底价代理政策</h3>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">新增政策</button>
                        <button style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">导出</button>
                    </div>
                </div>

                <!-- 搜索筛选区域 -->
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px; flex-wrap: wrap;">
                        <div style="display: flex; flex-direction: column; min-width: 200px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">政策名称：</label>
                            <input type="text" placeholder="请输入政策名称" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">政策类型：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部类型</option>
                                <option>标准政策</option>
                                <option>特殊政策</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">产品名称：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部产品</option>
                                <option>电商收款</option>
                                <option>外贸收款</option>
                                <option>物流收款</option>
                                <option>广告联盟收款</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">查询</button>
                        <button style="background: #6c757d; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">重置</button>
                    </div>
                </div>

                <!-- 表格 -->
                <div style="border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden; background: white;">
                    <table style="width: 100%; border-collapse: collapse; table-layout: fixed;">
                        <colgroup>
                            <col style="width: 200px;"> <!-- 政策名称 -->
                            <col style="width: 250px;"> <!-- 政策简介 -->
                            <col style="width: 120px;"> <!-- 政策类型 -->
                            <col style="width: 150px;"> <!-- 产品名称 -->
                            <col style="width: 300px;"> <!-- 产品底价 -->
                            <col style="width: 150px;"> <!-- 更新时间 -->
                            <col style="width: 120px;"> <!-- 操作 -->
                        </colgroup>
                        <thead>
                            <tr style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策名称</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策简介</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策类型</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">产品名称</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">产品底价</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">更新时间</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">A级电商收款标准政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">适用于A级代理商的电商收款业务标准底价政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">电商收款</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">全球收款→HKD→DBS: 0.20% | 本地收款→HKD→其他: 0.45%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">默认最高2条，微信...</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-15</div>
                                    <div style="color: #6c757d; font-size: 11px;">10:30</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">详情</a>
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">删除</a>
                                    </div>
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">B级外贸收款标准政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">适用于B级代理商的外贸收款业务标准底价政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">外贸收款</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">全球收款→HKD→DBS: 0.25% | 本地收款→HKD→其他: 0.5%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">默认最高2条，微信...</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-14</div>
                                    <div style="color: #6c757d; font-size: 11px;">09:20</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">详情</a>
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">删除</a>
                                    </div>
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">上海润泽科技特殊政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">专为上海润泽科技定制的电商收款特殊底价政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #fff3cd; color: #856404; border-radius: 12px; font-size: 11px; font-weight: 600;">特殊政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">电商收款</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">全球收款→HKD→DBS: 0.15% | 本地收款→HKD→其他: 0.35%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">特殊优惠价格</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-10</div>
                                    <div style="color: #6c757d; font-size: 11px;">14:25</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">详情</a>
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">删除</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


        <!-- 汇率返点代理政策页面 -->
        <div id="exchange-rebate-policy" class="page-content">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题和操作按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <h3 style="font-size: 20px; font-weight: 600; color: #333; margin: 0;">汇率返点代理政策</h3>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">新增政策</button>
                        <button style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">导出</button>
                    </div>
                </div>

                <!-- 搜索筛选区域 -->
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px; flex-wrap: wrap;">
                        <div style="display: flex; flex-direction: column; min-width: 200px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">政策名称：</label>
                            <input type="text" placeholder="请输入政策名称" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">政策类型：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部类型</option>
                                <option>标准政策</option>
                                <option>特殊政策</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">货币对：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部货币对</option>
                                <option>USD→CNH</option>
                                <option>EUR→USD</option>
                                <option>GBP→USD</option>
                                <option>USD→HKD</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">查询</button>
                        <button style="background: #6c757d; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">重置</button>
                    </div>
                </div>

                <!-- 表格 -->
                <div style="border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden; background: white;">
                    <table style="width: 100%; border-collapse: collapse; table-layout: fixed;">
                        <colgroup>
                            <col style="width: 200px;"> <!-- 政策名称 -->
                            <col style="width: 250px;"> <!-- 政策简介 -->
                            <col style="width: 120px;"> <!-- 政策类型 -->
                            <col style="width: 300px;"> <!-- 汇率返点 -->
                            <col style="width: 150px;"> <!-- 更新时间 -->
                            <col style="width: 120px;"> <!-- 操作 -->
                        </colgroup>
                        <thead>
                            <tr style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策名称</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策简介</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策类型</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">汇率返点</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">更新时间</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">A级标准汇率返点政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">适用于A级代理商的标准汇率返点政策，返点比例50%</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">USD→CNH: 50% | EUR→USD: 50% | GBP→USD: 50%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">标准返点比例</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-15</div>
                                    <div style="color: #6c757d; font-size: 11px;">10:30</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">详情</a>
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">删除</a>
                                    </div>
                                </td>
                            </tr>

                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">B级标准汇率返点政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">适用于B级代理商的标准汇率返点政策，返点比例40%</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">USD→CNH: 40% | EUR→USD: 40% | GBP→USD: 40%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">标准返点比例</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-14</div>
                                    <div style="color: #6c757d; font-size: 11px;">09:20</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">详情</a>
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">删除</a>
                                    </div>
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">上海润泽科技特殊返点政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">专为上海润泽科技定制的USD→CNH特殊汇率返点政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #fff3cd; color: #856404; border-radius: 12px; font-size: 11px; font-weight: 600;">特殊政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">USD→CNH: 65% | EUR→USD: 55%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">特殊优惠返点</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-10</div>
                                    <div style="color: #6c757d; font-size: 11px;">14:25</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">详情</a>
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 2px 4px; border-radius: 3px;">删除</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


        <!-- 混合代理政策页面 -->
        <div id="hybrid-policy" class="page-content">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题和操作按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <h3 style="font-size: 20px; font-weight: 600; color: #333; margin: 0;">混合代理政策</h3>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">新增政策</button>
                        <button style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">导出</button>
                    </div>
                </div>
                <div style="text-align: center; padding: 40px; color: #666;">
                    <div style="font-size: 16px; margin-bottom: 10px;">混合代理政策</div>
                    <div style="font-size: 14px;">同时使用底价和汇率返点的混合政策模式</div>
                </div>
            </div>
        </div>

    </div>

    <script>
        // 页面切换功能
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => {
                page.classList.remove('active');
            });

            // 隐藏所有导航项的active状态
            const navLinks = document.querySelectorAll('.nav-link, .nav-sub-link');
            navLinks.forEach(link => {
                link.classList.remove('active');
            });

            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }

            // 设置对应导航项为active状态
            const targetNavLink = document.querySelector(`[onclick="showPage('${pageId}')"]`);
            if (targetNavLink) {
                targetNavLink.classList.add('active');
            }
        }

        // Tab切换功能
        function switchTab(prefix, tabName) {
            // 隐藏所有tab内容
            const tabContents = document.querySelectorAll(`[id^="${prefix}-"]`);
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有tab导航的active状态
            const tabNavs = document.querySelectorAll('.tab-item');
            tabNavs.forEach(nav => {
                nav.classList.remove('active');
            });

            // 显示目标tab内容
            const targetContent = document.getElementById(`${prefix}-${tabName}`);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        }

    </script>
</body>
</html>
