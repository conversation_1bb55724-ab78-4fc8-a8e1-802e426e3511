<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汇率返点管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        body {
            background-color: #f0f2f5;
            color: #333;
            padding: 20px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
        }
        .page-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e8e8e8;
        }
        .page-title {
            font-size: 24px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: 600;
            margin-bottom: 8px;
        }
        .page-subtitle {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
        }
        .breadcrumb {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            margin-bottom: 8px;
        }
        .breadcrumb a {
            color: #1890ff;
            text-decoration: none;
        }
        
        /* Tab样式 */
        .tab-container {
            margin-bottom: 24px;
        }
        .tab-header {
            display: flex;
            border-bottom: 1px solid #e8e8e8;
            margin-bottom: 16px;
        }
        .tab-item {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: rgba(0, 0, 0, 0.65);
            transition: all 0.3s;
        }
        .tab-item:hover {
            color: #1890ff;
        }
        .tab-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            font-weight: 500;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        
        /* 操作栏样式 */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .search-box {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        .search-box input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            width: 200px;
        }
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            border: none;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        .btn-default {
            background-color: #fff;
            border: 1px solid #d9d9d9;
            color: rgba(0, 0, 0, 0.65);
        }
        .btn-default:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }
        
        /* 表格样式 */
        .table-container {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            overflow: hidden;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        .table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            border-bottom: 1px solid #e8e8e8;
        }
        .table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            color: rgba(0, 0, 0, 0.85);
        }
        .table tbody tr:hover {
            background: #f5f5f5;
        }
        .table tbody tr:last-child td {
            border-bottom: none;
        }
        
        /* 状态标签 */
        .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-standard {
            background: #e6f7ff;
            color: #1890ff;
        }
        .status-special {
            background: #fff2e8;
            color: #fa8c16;
        }
        
        /* 操作按钮 */
        .action-buttons {
            display: flex;
            gap: 8px;
        }
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        .btn-link {
            background: none;
            border: none;
            color: #1890ff;
            cursor: pointer;
            padding: 4px 8px;
            font-size: 12px;
        }
        .btn-link:hover {
            color: #40a9ff;
        }
        .btn-danger {
            color: #ff4d4f;
        }
        .btn-danger:hover {
            color: #ff7875;
        }
        
        /* 汇率简介样式 */
        .exchange-intro {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 14px;
            color: #fa8c16;
        }
        
        /* 货币对样式 */
        .currency-pair {
            font-weight: 500;
            color: #1890ff;
        }
        
        /* 加点/返点样式 */
        .rate-value {
            font-weight: 500;
        }
        .rate-positive {
            color: #52c41a;
        }
        .rate-negative {
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <div class="breadcrumb">
                <a href="计费管理.html">代理商管理</a> / 汇率返点管理
            </div>
            <div class="page-title">汇率返点管理</div>
            <div class="page-subtitle">管理代理商的汇率加点和返点配置</div>
        </div>

        <!-- Tab容器 -->
        <div class="tab-container">
            <div class="tab-header">
                <div class="tab-item active" onclick="switchTab('base-rate')">底价汇率</div>
                <div class="tab-item" onclick="switchTab('fixed-rebate')">固定返点</div>
            </div>

            <!-- 底价汇率Tab -->
            <div id="base-rate-tab" class="tab-content active">
                <div class="exchange-intro">
                    <strong>底价汇率简介：</strong>代理商可以在系统底价汇率基础上加点，向商户提供汇率服务并获得差价收益。加点越高，代理商收益越大，但商户成本也越高。
                </div>
                
                <div class="action-bar">
                    <div class="search-box">
                        <input type="text" placeholder="搜索货币对">
                        <button class="btn btn-default">搜索</button>
                    </div>
                    <button class="btn btn-primary" onclick="addExchangeRate('base-rate')">新增底价汇率</button>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>返点简介</th>
                                <th>类型</th>
                                <th>货币对</th>
                                <th>加点</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="base-rate-table-body">
                            <tr>
                                <td><span class="status-tag status-standard">A类代理返点</span></td>
                                <td><span class="status-tag status-standard">标准</span></td>
                                <td><span class="currency-pair">USD/CNY</span></td>
                                <td><span class="rate-value rate-negative">-0.15%</span></td>
                                <td>2024-01-15 10:30:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="status-tag status-special">B类代理返点</span></td>
                                <td><span class="status-tag status-special">特殊</span></td>
                                <td><span class="currency-pair">EUR/CNY</span></td>
                                <td><span class="rate-value rate-negative">-0.15%</span></td>
                                <td>2024-01-14 15:20:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="status-tag status-special">001特殊代理返点</span></td>
                                <td><span class="status-tag status-standard">标准</span></td>
                                <td><span class="currency-pair">GBP/CNY</span></td>
                                <td><span class="rate-value rate-negative">-0.12%</span></td>
                                <td>2024-01-13 09:15:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="status-tag status-special">004特殊代理返点</span></td>
                                <td><span class="status-tag status-special">特殊</span></td>
                                <td><span class="currency-pair">JPY/CNY</span></td>
                                <td><span class="rate-value rate-negative">-0.12%</span></td>
                                <td>2024-01-12 14:45:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 固定返点Tab -->
            <div id="fixed-rebate-tab" class="tab-content">
                <div class="exchange-intro">
                    <strong>固定返点简介：</strong>代理商按照固定返点比例从汇率差价中获得分成，无汇率定价权。适用于标准化运营，系统统一管理汇率价格。
                </div>
                
                <div class="action-bar">
                    <div class="search-box">
                        <input type="text" placeholder="搜索货币对">
-                        <button class="btn btn-default">搜索</button>
                    </div>
                    <button class="btn btn-primary" onclick="addExchangeRate('fixed-rebate')">新增固定返点</button>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>返点简介</th>
                                <th>类型</th>
                                <th>货币对</th>
                                <th>返点比例</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="fixed-rebate-table-body">
                            <tr>
                                <td><span class="status-tag status-special">A类代理返点</span></td>
                                <td><span class="status-tag status-standard">标准</span></td>
                                <td><span class="currency-pair">USD/CNY</span></td>
                                <td><span class="rate-value rate-positive">600bps</span></td>
                                <td>2024-01-15 10:30:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="status-tag status-special">B类代理返点</span></td>
                                <td><span class="status-tag status-special">特殊</span></td>
                                <td><span class="currency-pair">EUR/CNY</span></td>
                                <td><span class="rate-value rate-positive">750bps</span></td>
                                <td>2024-01-14 15:20:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="status-tag status-special">001代理返点</span></td>
                                <td><span class="status-tag status-standard">标准</span></td>
                                <td><span class="currency-pair">GBP/CNY</span></td>
                                <td><span class="rate-value rate-positive">500bps</span></td>
                                <td>2024-01-13 09:15:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="status-tag status-special">002代理返点</span></td>
                                <td><span class="status-tag status-special">特殊</span></td>
                                <td><span class="currency-pair">JPY/CNY</span></td>
                                <td><span class="rate-value rate-positive">800bps</span></td>
                                <td>2024-01-12 14:45:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab切换功能
        function switchTab(tabName) {
            // 隐藏所有tab内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有tab项的active状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示选中的tab内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活选中的tab项
            event.target.classList.add('active');
        }
        
        // 新增汇率配置
        function addExchangeRate(type) {
            // 这里可以跳转到汇率配置页面
            window.location.href = `exchange-rate-config.html?type=${type}`;
        }
        
        // 编辑汇率配置
        function editExchangeRate(id, type) {
            window.location.href = `exchange-rate-config.html?id=${id}&type=${type}`;
        }
        
        // 删除汇率配置
        function deleteExchangeRate(id) {
            if (confirm('确定要删除这个汇率配置吗？')) {
                // 这里添加删除逻辑
                console.log('删除汇率配置:', id);
            }
        }
    </script>
</body>
</html>
