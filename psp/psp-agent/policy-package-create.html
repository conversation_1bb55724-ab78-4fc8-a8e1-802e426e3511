<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增政策包</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .breadcrumb { font-size: 14px; color: #666; margin-bottom: 10px; }
        .breadcrumb a { color: #007bff; text-decoration: none; }
        .page-title { font-size: 24px; font-weight: 600; color: #333; margin-bottom: 8px; }
        .page-desc { color: #666; font-size: 14px; }
        
        .form-section { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .section-title { font-size: 18px; font-weight: 600; color: #333; margin-bottom: 20px; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        
        .form-row { display: flex; gap: 20px; margin-bottom: 20px; align-items: center; }
        .form-group { display: flex; flex-direction: column; min-width: 200px; }
        .form-group label { font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px; }
        .form-group input, .form-group select, .form-group textarea { padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; }
        .form-group textarea { min-height: 80px; resize: vertical; }
        
        .product-section { margin-bottom: 30px; }
        .product-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .product-title { font-size: 16px; font-weight: 600; color: #333; }
        .product-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; }
        .status-enabled { background: #d4edda; color: #155724; }
        .status-disabled { background: #f8d7da; color: #721c24; }
        
        .config-list { border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden; }
        .config-header { background: #f8f9fa; padding: 12px 15px; font-weight: 600; color: #495057; border-bottom: 1px solid #e9ecef; display: flex; justify-content: space-between; align-items: center; }
        .config-item { padding: 15px; border-bottom: 1px solid #f1f3f4; display: flex; justify-content: space-between; align-items: center; }
        .config-item:last-child { border-bottom: none; }
        .config-item:hover { background: #f8f9fa; }
        
        .config-details { flex: 1; }
        .config-name { font-weight: 500; color: #333; margin-bottom: 4px; }
        .config-desc { font-size: 12px; color: #666; }
        .config-rate { font-weight: 600; color: #007bff; margin-right: 15px; }
        
        .config-actions { display: flex; gap: 8px; }
        .btn { padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; transition: all 0.3s; }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-outline { background: white; color: #007bff; border: 1px solid #007bff; }
        .btn-outline:hover { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-danger:hover { background: #c82333; }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; }
        
        .add-config-section { padding: 20px; text-align: center; border: 2px dashed #dee2e6; border-radius: 8px; color: #6c757d; cursor: pointer; transition: all 0.3s; }
        .add-config-section:hover { border-color: #007bff; color: #007bff; }
        .add-icon { font-size: 24px; margin-bottom: 8px; }
        
        .actions-section { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: right; }
        .btn-large { padding: 12px 24px; font-size: 14px; margin-left: 10px; }
        
        .product-toggle { display: flex; align-items: center; gap: 10px; }
        .toggle-switch { position: relative; width: 44px; height: 24px; background: #ccc; border-radius: 12px; cursor: pointer; transition: background 0.3s; }
        .toggle-switch.active { background: #007bff; }
        .toggle-slider { position: absolute; top: 2px; left: 2px; width: 20px; height: 20px; background: white; border-radius: 50%; transition: transform 0.3s; }
        .toggle-switch.active .toggle-slider { transform: translateX(20px); }
        
        .empty-state { text-align: center; padding: 40px; color: #6c757d; }
        .empty-icon { font-size: 48px; margin-bottom: 15px; }
        
        .selected-config { background: #e3f2fd !important; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="breadcrumb">
                <a href="agent-policy-package.html">政策包管理</a> / 新增政策包
            </div>
            <div class="page-title">新增政策包</div>
            <div class="page-desc">创建包含8个产品完整计费配置的政策包</div>
        </div>
        
        <!-- 基本信息 -->
        <div class="form-section">
            <div class="section-title">基本信息</div>
            <div class="form-row">
                <div class="form-group">
                    <label>政策包名称 <span style="color: red;">*</span></label>
                    <input type="text" placeholder="请输入政策包名称" required>
                </div>
                <div class="form-group">
                    <label>政策包类型 <span style="color: red;">*</span></label>
                    <select required>
                        <option value="">请选择政策包类型</option>
                        <option value="standard">标准政策包</option>
                        <option value="special">特殊政策包</option>
                        <option value="custom">定制政策包</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>适用代理商等级</label>
                    <select>
                        <option value="">请选择代理商等级</option>
                        <option value="A">A级代理商</option>
                        <option value="B">B级代理商</option>
                        <option value="C">C级代理商</option>
                        <option value="all">所有等级</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group" style="flex: 1;">
                    <label>政策包描述</label>
                    <textarea placeholder="请输入政策包描述..."></textarea>
                </div>
            </div>
        </div>
        
        <!-- 产品配置 -->
        <div class="form-section">
            <div class="section-title">产品配置 (8个产品)</div>
            
            <!-- 外贸收款 -->
            <div class="product-section">
                <div class="product-header">
                    <div class="product-title">1. 外贸收款</div>
                    <div class="product-toggle">
                        <span>启用</span>
                        <div class="toggle-switch active" onclick="toggleProduct(this, 'trade')">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                </div>
                <div id="trade-configs" class="config-list">
                    <div class="config-header">
                        <span>已选择的计费配置</span>
                        <button class="btn btn-primary" onclick="selectConfig('trade')">选择配置</button>
                    </div>
                    <div class="config-item selected-config">
                        <div class="config-details">
                            <div class="config-name">全球收款→HKD→DBS银行</div>
                            <div class="config-desc">适用于全球范围的外贸收款，结算到香港DBS银行</div>
                        </div>
                        <div class="config-rate">0.8% + $0.3</div>
                        <div class="config-actions">
                            <button class="btn btn-outline" onclick="editConfig(this)">编辑</button>
                            <button class="btn btn-danger" onclick="removeConfig(this)">移除</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 电商收款 -->
            <div class="product-section">
                <div class="product-header">
                    <div class="product-title">2. 电商收款</div>
                    <div class="product-toggle">
                        <span>启用</span>
                        <div class="toggle-switch active" onclick="toggleProduct(this, 'ecommerce')">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                </div>
                <div id="ecommerce-configs" class="config-list">
                    <div class="config-header">
                        <span>已选择的计费配置</span>
                        <button class="btn btn-primary" onclick="selectConfig('ecommerce')">选择配置</button>
                    </div>
                    <div class="add-config-section" onclick="selectConfig('ecommerce')">
                        <div class="add-icon">+</div>
                        <div>点击选择电商收款计费配置</div>
                    </div>
                </div>
            </div>
            
            <!-- 物流收款 -->
            <div class="product-section">
                <div class="product-header">
                    <div class="product-title">3. 物流收款</div>
                    <div class="product-toggle">
                        <span>启用</span>
                        <div class="toggle-switch" onclick="toggleProduct(this, 'logistics')">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                </div>
                <div id="logistics-configs" class="config-list" style="display: none;">
                    <div class="empty-state">
                        <div class="empty-icon">📦</div>
                        <div>物流收款已禁用</div>
                    </div>
                </div>
            </div>
            
            <!-- 其他产品类似结构... -->
            <div class="product-section">
                <div class="product-header">
                    <div class="product-title">4. 开发者平台收款</div>
                    <div class="product-toggle">
                        <span>启用</span>
                        <div class="toggle-switch" onclick="toggleProduct(this, 'developer')">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                </div>
                <div id="developer-configs" class="config-list" style="display: none;">
                    <div class="empty-state">
                        <div class="empty-icon">💻</div>
                        <div>开发者平台收款已禁用</div>
                    </div>
                </div>
            </div>
            
            <!-- 继续其他4个产品... -->
            <div style="text-align: center; margin: 30px 0; color: #666;">
                <p>还有4个产品配置：广告联盟收款、全球支付、本地支付、结算服务</p>
                <button class="btn btn-outline" onclick="expandAllProducts()">展开所有产品</button>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="actions-section">
            <a href="agent-policy-package.html" class="btn btn-outline btn-large">取消</a>
            <button class="btn btn-outline btn-large" onclick="saveDraft()">保存草稿</button>
            <button class="btn btn-primary btn-large" onclick="savePackage()">创建政策包</button>
        </div>
    </div>
    
    <script>
        function toggleProduct(toggle, productType) {
            toggle.classList.toggle('active');
            const configSection = document.getElementById(productType + '-configs');
            
            if (toggle.classList.contains('active')) {
                configSection.style.display = 'block';
            } else {
                configSection.style.display = 'none';
            }
        }
        
        function selectConfig(productType) {
            // 跳转到配置选择页面
            window.open(`config-selector.html?product=${productType}`, '_blank', 'width=1000,height=600');
        }
        
        function editConfig(btn) {
            // 编辑配置
            alert('编辑配置功能');
        }
        
        function removeConfig(btn) {
            if (confirm('确定要移除这个配置吗？')) {
                btn.closest('.config-item').remove();
            }
        }
        
        function expandAllProducts() {
            alert('展开所有8个产品配置');
        }
        
        function saveDraft() {
            alert('政策包草稿已保存');
        }
        
        function savePackage() {
            alert('政策包创建成功！');
            window.location.href = 'agent-policy-package.html';
        }
    </script>
</body>
</html>
