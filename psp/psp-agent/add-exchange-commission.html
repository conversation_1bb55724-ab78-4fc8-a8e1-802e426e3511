<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增汇率返点</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .card-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .card-body {
            padding: 30px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control {
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            width: 100%;
        }

        .required {
            color: #dc3545;
        }

        .commission-table {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .table-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 2fr 100px;
            gap: 15px;
            align-items: center;
            font-weight: 600;
            font-size: 14px;
        }

        .commission-row {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 2fr 100px;
            gap: 15px;
            align-items: center;
        }

        .fixed-row {
            background: #f8f9fa;
        }

        .currency-display {
            padding: 8px 12px;
            background: #e9ecef;
            border-radius: 4px;
            font-size: 14px;
            color: #6c757d;
        }

        .rate-input {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .rate-input input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn-group {
            display: flex;
            gap: 5px;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }

        .btn-large {
            padding: 12px 30px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">新增汇率返点</h3>
                <button class="btn btn-secondary btn-sm" onclick="goBack()">返回</button>
            </div>
            <div class="card-body">
                <!-- 基本信息 -->
                <div style="margin-bottom: 40px;">
                    <h4 class="section-title">基本信息</h4>
                    
                    <!-- 返点简介输入 -->
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label class="form-label">返点简介：</label>
                        <textarea id="rebate-intro" placeholder="请输入返点简介，最多50个字" maxlength="50" style="width: 100%; height: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"></textarea>
                        <div class="char-count" style="font-size: 12px; color: #999; margin-top: 4px; text-align: right;">0/50</div>
                    </div>
                    
                    <!-- 返点类型选择 -->
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label class="form-label">返点类型：</label>
                        <div style="display: flex; gap: 20px; margin-top: 8px;">
                            <label style="display: flex; align-items: center; cursor: pointer;">
                                <input type="radio" name="rebate-type" value="base-rate" onchange="toggleRebateFields()" style="margin-right: 8px;">
                                <span>底价汇率</span>
                            </label>
                            <label style="display: flex; align-items: center; cursor: pointer;">
                                <input type="radio" name="rebate-type" value="fixed-rebate" onchange="toggleRebateFields()" style="margin-right: 8px;">
                                <span>固定返点</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 底价类型选择（仅底价汇率时显示） -->
                    <div class="form-group" id="base-rate-type-row" style="display: none; margin-bottom: 20px;">
                        <label class="form-label">底价类型：</label>
                        <div style="display: flex; gap: 20px; margin-top: 8px;">
                            <label style="display: flex; align-items: center; cursor: pointer;">
                                <input type="radio" name="base-rate-type" value="standard" onchange="toggleAgentIdField()" style="margin-right: 8px;">
                                <span>标准底价</span>
                            </label>
                            <label style="display: flex; align-items: center; cursor: pointer;">
                                <input type="radio" name="base-rate-type" value="special" onchange="toggleAgentIdField()" style="margin-right: 8px;">
                                <span>特殊底价</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 返点类型选择（仅固定返点时显示） -->
                    <div class="form-group" id="fixed-rebate-type-row" style="display: none; margin-bottom: 20px;">
                        <label class="form-label">返点类型：</label>
                        <div style="display: flex; gap: 20px; margin-top: 8px;">
                            <label style="display: flex; align-items: center; cursor: pointer;">
                                <input type="radio" name="fixed-rebate-type" value="standard" onchange="toggleAgentIdField()" style="margin-right: 8px;">
                                <span>标准返点</span>
                            </label>
                            <label style="display: flex; align-items: center; cursor: pointer;">
                                <input type="radio" name="fixed-rebate-type" value="special" onchange="toggleAgentIdField()" style="margin-right: 8px;">
                                <span>特殊返点</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 代理商ID输入（仅特殊类型时显示） -->
                    <div class="form-group" id="agent-id-row" style="display: none; margin-bottom: 20px;">
                        <label class="form-label">代理商ID：</label>
                        <input type="text" id="agent-account-number" class="form-control" placeholder="请输入代理商ID" onblur="loadAgentInfo()">
                    </div>
                    
                    <!-- 代理商信息显示（仅特殊类型时显示） -->
                    <div id="agent-info-display" style="display: none; background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 16px; margin-bottom: 20px;">
                        <div style="margin-bottom: 8px;">
                            <span style="color: #666; margin-right: 8px;">代理商名称：</span>
                            <span id="agent-name-display" style="color: #333; font-weight: 500;"></span>
                        </div>
                        <div>
                            <span style="color: #666; margin-right: 8px;">激活状态：</span>
                            <span id="agent-status" style="color: #333; font-weight: 500;"></span>
                        </div>
                    </div>
                    
                    <!-- 备注 -->
                    <div class="form-group">
                        <label class="form-label">备注：</label>
                        <input type="text" id="remark" class="form-control" placeholder="请输入备注信息">
                    </div>
                </div>

                <!-- 汇率返点配置 -->
                <div style="margin-bottom: 40px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h4 style="margin: 0; font-size: 18px; font-weight: 600; color: #333; padding-bottom: 10px; border-bottom: 2px solid #28a745;">汇率返点配置</h4>
                        <button class="btn btn-primary btn-sm" onclick="addCommissionRow()">+ 新增配置</button>
                    </div>

                    <div class="commission-table">
                        <div class="table-header">
                            <div>卖出币种</div>
                            <div>买入币种</div>
                            <div>返点</div>
                            <div>备注</div>
                            <div>操作</div>
                        </div>

                        <div id="commissionRows">
                            <!-- 固定的"其他→其他"行 -->
                            <div class="commission-row fixed-row">
                                <div class="currency-display">其他</div>
                                <div class="currency-display">其他</div>
                                <div class="rate-input">
                                    <input type="number" class="commission-rate" placeholder="50" min="0" max="100" step="0.1">
                                    <span style="color: #666; font-size: 14px;">%</span>
                                </div>
                                <input type="text" class="commission-remark form-control" placeholder="请输入备注">
                                <div style="color: #6c757d; font-size: 12px; text-align: center;">固定</div>
                            </div>

                            <!-- 可编辑配置行 -->
                            <div class="commission-row">
                                <select class="sell-currency form-control">
                                    <option value="">请选择</option>
                                    <option value="USD">USD</option>
                                    <option value="EUR">EUR</option>
                                    <option value="HKD">HKD</option>
                                    <option value="CNH">CNH</option>
                                    <option value="GBP">GBP</option>
                                    <option value="JPY">JPY</option>
                                </select>
                                <select class="buy-currency form-control">
                                    <option value="">请选择</option>
                                    <option value="USD">USD</option>
                                    <option value="EUR">EUR</option>
                                    <option value="HKD">HKD</option>
                                    <option value="CNH">CNH</option>
                                    <option value="GBP">GBP</option>
                                    <option value="JPY">JPY</option>
                                </select>
                                <div class="rate-input">
                                    <input type="number" class="commission-rate" placeholder="50" min="0" max="100" step="0.1">
                                    <span style="color: #666; font-size: 14px;">%</span>
                                </div>
                                <input type="text" class="commission-remark form-control" placeholder="请输入备注">
                                <div class="btn-group">
                                    <button class="btn btn-sm" onclick="addCommissionRow()" style="background: #28a745; color: white;" title="新增">➕</button>
                                    <button class="btn btn-sm" onclick="deleteCommissionRow(this)" style="background: #dc3545; color: white;" title="删除">🗑️</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="btn btn-secondary btn-large" onclick="goBack()">取消</button>
                    <button class="btn btn-primary btn-large" onclick="saveCommissionConfig()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 新增配置行
        function addCommissionRow() {
            const commissionRows = document.getElementById('commissionRows');
            const newRow = document.createElement('div');
            newRow.className = 'commission-row';

            newRow.innerHTML = `
                <select class="sell-currency form-control">
                    <option value="">请选择</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="HKD">HKD</option>
                    <option value="CNH">CNH</option>
                    <option value="GBP">GBP</option>
                    <option value="JPY">JPY</option>
                </select>
                <select class="buy-currency form-control">
                    <option value="">请选择</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="HKD">HKD</option>
                    <option value="CNH">CNH</option>
                    <option value="GBP">GBP</option>
                    <option value="JPY">JPY</option>
                </select>
                <div class="rate-input">
                    <input type="number" class="commission-rate" placeholder="50" min="0" max="100" step="0.1">
                    <span style="color: #666; font-size: 14px;">%</span>
                </div>
                <input type="text" class="commission-remark form-control" placeholder="请输入备注">
                <div class="btn-group">
                    <button class="btn btn-sm" onclick="addCommissionRow()" style="background: #28a745; color: white;" title="新增">➕</button>
                    <button class="btn btn-sm" onclick="deleteCommissionRow(this)" style="background: #dc3545; color: white;" title="删除">🗑️</button>
                </div>
            `;

            commissionRows.appendChild(newRow);
        }

        // 删除配置行
        function deleteCommissionRow(button) {
            const row = button.closest('.commission-row');
            
            // 检查是否是固定行
            if (row.classList.contains('fixed-row')) {
                alert('固定行不能删除');
                return;
            }
            
            const rows = document.querySelectorAll('.commission-row:not(.fixed-row)');
            if (rows.length > 1) {
                row.remove();
            } else {
                alert('至少需要保留一个配置行');
            }
        }

        // 保存配置
        function saveCommissionConfig() {
            // 验证代理商等级
            const agentLevel = document.getElementById('agentLevel').value;
            if (!agentLevel) {
                alert('请选择代理商等级');
                return;
            }
            
            // 验证固定行的返点
            const fixedRow = document.querySelector('.fixed-row');
            const fixedRate = fixedRow.querySelector('.commission-rate').value;
            if (!fixedRate) {
                alert('请输入"其他→其他"的返点百分比');
                return;
            }
            
            // 验证其他行
            const rows = document.querySelectorAll('.commission-row:not(.fixed-row)');
            let valid = true;
            
            rows.forEach(row => {
                const sellCurrency = row.querySelector('.sell-currency').value;
                const buyCurrency = row.querySelector('.buy-currency').value;
                const rate = row.querySelector('.commission-rate').value;
                
                if (!sellCurrency || !buyCurrency || !rate) {
                    valid = false;
                }
            });
            
            if (!valid) {
                alert('请完善所有货币对的返点配置');
                return;
            }
            
            // 收集数据
            const data = {
                agentLevel: agentLevel,
                remark: document.getElementById('remark').value,
                commissions: []
            };
            
            // 收集固定行数据
            const fixedRemark = fixedRow.querySelector('.commission-remark').value;
            data.commissions.push({
                sellCurrency: '其他',
                buyCurrency: '其他',
                rate: fixedRate,
                remark: fixedRemark,
                isFixed: true
            });
            
            // 收集其他行数据
            rows.forEach(row => {
                const sellCurrency = row.querySelector('.sell-currency').value;
                const buyCurrency = row.querySelector('.buy-currency').value;
                const rate = row.querySelector('.commission-rate').value;
                const remark = row.querySelector('.commission-remark').value;
                
                if (sellCurrency && buyCurrency && rate) {
                    data.commissions.push({
                        sellCurrency: sellCurrency,
                        buyCurrency: buyCurrency,
                        rate: rate,
                        remark: remark,
                        isFixed: false
                    });
                }
            });
            
            console.log('保存的数据:', data);
            alert('保存成功！');
            goBack();
        }

        // 返回
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.close();
            }
        }
    </script>
</body>
</html>
