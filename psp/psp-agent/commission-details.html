<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分佣金明细列表</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }

        /* Tab导航样式 */
        .tab-nav {
            display: flex;
            border-bottom: 2px solid #f1f3f4;
            margin-bottom: 20px;
        }

        .tab-item {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
        }

        .tab-item.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: rgba(0, 123, 255, 0.05);
        }

        .tab-item:hover {
            color: #007bff;
            background: rgba(0, 123, 255, 0.05);
        }

        /* 搜索筛选区域 */
        .filter-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .filter-row {
            display: flex;
            gap: 20px;
            align-items: end;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: 150px;
        }

        .filter-label {
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .filter-input, .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background: white;
        }

        .filter-input:focus, .filter-select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .data-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 12px 10px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            font-size: 13px;
        }

        .data-table td {
            padding: 12px 10px;
            border-bottom: 1px solid #f1f3f4;
            font-size: 13px;
            vertical-align: middle;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        /* Tab内容区域 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }

        .status-danger {
            background: #f8d7da;
            color: #721c24;
        }

        /* 金额样式 */
        .amount-positive {
            color: #28a745;
            font-weight: 600;
        }

        .amount-negative {
            color: #dc3545;
            font-weight: 600;
        }

        .amount-neutral {
            color: #333;
            font-weight: 500;
        }

        /* 操作链接 */
        .action-link {
            color: #007bff;
            text-decoration: none;
            font-size: 12px;
            padding: 2px 4px;
            border-radius: 3px;
            margin-right: 6px;
        }

        .action-link:hover {
            background: rgba(0, 123, 255, 0.1);
        }

        .action-link.danger {
            color: #dc3545;
        }

        .action-link.danger:hover {
            background: rgba(220, 53, 69, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">分佣金明细列表</h1>
            
            <!-- Tab导航 -->
            <div class="tab-nav">
                <div class="tab-item active" onclick="switchTab('transaction')">交易返点</div>
                <div class="tab-item" onclick="switchTab('exchange')">汇率返点</div>
                <div class="tab-item" onclick="switchTab('adjustment')">调整明细</div>
            </div>
        </div>

        <!-- 交易返点Tab -->
        <div id="transaction-tab" class="tab-content active">
            <!-- 搜索筛选 -->
            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-group">
                        <label class="filter-label">代理商名称：</label>
                        <select class="filter-select" style="width: 200px;">
                            <option>全部代理商</option>
                            <option>上海润泽科技有限公司</option>
                            <option>北京智汇通科技有限公司</option>
                            <option>广州环球贸易有限公司</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">分佣类型：</label>
                        <select class="filter-select" style="width: 150px;">
                            <option>全部类型</option>
                            <option>交易返点</option>
                            <option>汇率返点</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">商户名称：</label>
                        <input type="text" class="filter-input" placeholder="请输入商户名称" style="width: 200px;">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">交易时间：</label>
                        <input type="date" class="filter-input" style="width: 150px;">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">至：</label>
                        <input type="date" class="filter-input" style="width: 150px;">
                    </div>
                </div>
                <div class="filter-buttons">
                    <button class="btn btn-primary">查询</button>
                    <button class="btn btn-secondary">重置</button>
                </div>
            </div>

            <!-- 交易返点表格 -->
            <div class="table-container">
                <table class="data-table">
                    <colgroup>
                        <col style="width: 120px;"> <!-- 账单ID -->
                        <col style="width: 120px;"> <!-- 代理商ID -->
                        <col style="width: 150px;"> <!-- 代理商名称 -->
                        <col style="width: 100px;"> <!-- 产品 -->
                        <col style="width: 100px;"> <!-- 商户 -->
                        <col style="width: 100px;"> <!-- 状态 -->
                        <col style="width: 120px;"> <!-- 交易金额 -->
                        <col style="width: 120px;"> <!-- 交易费率 -->
                        <col style="width: 120px;"> <!-- 交易费用 -->
                        <col style="width: 120px;"> <!-- 分佣金额 -->
                        <col style="width: 100px;"> <!-- 备注 -->
                        <col style="width: 120px;"> <!-- 更新时间 -->
                    </colgroup>
                    <thead>
                        <tr>
                            <th>账单ID</th>
                            <th>代理商ID</th>
                            <th>代理商名称</th>
                            <th>产品</th>
                            <th>商户</th>
                            <th>状态</th>
                            <th>交易金额</th>
                            <th>交易费率</th>
                            <th>交易费用</th>
                            <th>分佣金额</th>
                            <th>备注</th>
                            <th>更新时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>BILL20240701</td>
                            <td>AGT001</td>
                            <td>上海润泽科技</td>
                            <td>跨境收款</td>
                            <td>2024-01</td>
                            <td><span class="status-badge status-success">正常</span></td>
                            <td>$10,000.00</td>
                            <td>¥ 365.00</td>
                            <td>¥ 245.00</td>
                            <td><span class="amount-positive">¥ 108.50</span></td>
                            <td>正常分佣</td>
                            <td>01-15 14:35</td>
                        </tr>
                        <tr>
                            <td>BILL20240702</td>
                            <td>AGT001</td>
                            <td>上海润泽科技</td>
                            <td>本地收款</td>
                            <td>2024-01</td>
                            <td><span class="status-badge status-success">正常</span></td>
                            <td>€8,800.00</td>
                            <td>¥ 685.00</td>
                            <td>¥ 414.50</td>
                            <td><span class="amount-positive">¥ 189.60</span></td>
                            <td>工商银行分佣</td>
                            <td>01-16 16:05</td>
                        </tr>
                        <tr>
                            <td>BILL20240703</td>
                            <td>AGT002</td>
                            <td>金融代理商</td>
                            <td>专业收款</td>
                            <td>2024-01</td>
                            <td><span class="status-badge status-success">正常</span></td>
                            <td>$20,000.00</td>
                            <td>¥ 1,400.00</td>
                            <td>¥ 1,050.00</td>
                            <td><span class="amount-positive">¥ 350.00</span></td>
                            <td>正常分佣</td>
                            <td>01-16 08:25</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 汇率返点Tab -->
        <div id="exchange-tab" class="tab-content">
            <!-- 搜索筛选 -->
            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-group">
                        <label class="filter-label">代理商名称：</label>
                        <select class="filter-select" style="width: 200px;">
                            <option>全部代理商</option>
                            <option>上海润泽科技有限公司</option>
                            <option>北京智汇通科技有限公司</option>
                            <option>广州环球贸易有限公司</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">账单周期：</label>
                        <select class="filter-select" style="width: 150px;">
                            <option>全部周期</option>
                            <option>2024-01</option>
                            <option>2024-02</option>
                            <option>2024-03</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">交易编号：</label>
                        <input type="text" class="filter-input" placeholder="请输入交易编号" style="width: 200px;">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">换汇时间：</label>
                        <input type="date" class="filter-input" style="width: 150px;">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">至：</label>
                        <input type="date" class="filter-input" style="width: 150px;">
                    </div>
                </div>
                <div class="filter-buttons">
                    <button class="btn btn-primary">查询</button>
                    <button class="btn btn-secondary">重置</button>
                </div>
            </div>

            <!-- 汇率返点表格 -->
            <div class="table-container">
                <table class="data-table">
                    <colgroup>
                        <col style="width: 120px;"> <!-- 账单ID -->
                        <col style="width: 150px;"> <!-- 代理商名称 -->
                        <col style="width: 100px;"> <!-- 账单周期 -->
                        <col style="width: 150px;"> <!-- 交易编号 -->
                        <col style="width: 150px;"> <!-- 换汇金额 -->
                        <col style="width: 100px;"> <!-- 交易汇率 -->
                        <col style="width: 100px;"> <!-- 政策返点 -->
                        <col style="width: 120px;"> <!-- 分佣金额 -->
                        <col style="width: 120px;"> <!-- 备注 -->
                        <col style="width: 120px;"> <!-- 更新时间 -->
                    </colgroup>
                    <thead>
                        <tr>
                            <th>账单ID</th>
                            <th>代理商名称</th>
                            <th>账单周期</th>
                            <th>交易编号</th>
                            <th>换汇金额</th>
                            <th>交易汇率</th>
                            <th>政策返点</th>
                            <th>分佣金额</th>
                            <th>备注</th>
                            <th>更新时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>BILL20240701</td>
                            <td>上海润泽科技有限公司</td>
                            <td>2024-01</td>
                            <td>TXN240701R001</td>
                            <td>100USD→723CNY</td>
                            <td>7.3</td>
                            <td>50%</td>
                            <td><span class="amount-positive">¥ 15.50</span></td>
                            <td>汇率返点</td>
                            <td>01-15 14:35</td>
                        </tr>
                        <tr>
                            <td>BILL20240702</td>
                            <td>北京智汇通科技有限公司</td>
                            <td>2024-01</td>
                            <td>TXN240702R002</td>
                            <td>500EUR→3850CNY</td>
                            <td>7.7</td>
                            <td>40%</td>
                            <td><span class="amount-positive">¥ 28.60</span></td>
                            <td>汇率返点</td>
                            <td>01-16 09:20</td>
                        </tr>
                        <tr>
                            <td>BILL20240703</td>
                            <td>广州环球贸易有限公司</td>
                            <td>2024-01</td>
                            <td>TXN240703R003</td>
                            <td>200GBP→1820CNY</td>
                            <td>9.1</td>
                            <td>35%</td>
                            <td><span class="amount-positive">¥ 22.40</span></td>
                            <td>汇率返点</td>
                            <td>01-16 11:45</td>
                        </tr>
                        <tr>
                            <td>BILL20240704</td>
                            <td>上海润泽科技有限公司</td>
                            <td>2024-01</td>
                            <td>TXN240704R004</td>
                            <td>1000USD→7280CNY</td>
                            <td>7.28</td>
                            <td>55%</td>
                            <td><span class="amount-positive">¥ 89.20</span></td>
                            <td>VIP汇率返点</td>
                            <td>01-17 15:30</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 调整明细Tab -->
        <div id="adjustment-tab" class="tab-content">
            <!-- 搜索筛选 -->
            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-group">
                        <label class="filter-label">代理商名称：</label>
                        <select class="filter-select" style="width: 200px;">
                            <option>全部代理商</option>
                            <option>上海润泽科技有限公司</option>
                            <option>北京智汇通科技有限公司</option>
                            <option>广州环球贸易有限公司</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">账单周期：</label>
                        <select class="filter-select" style="width: 150px;">
                            <option>全部周期</option>
                            <option>2024-01</option>
                            <option>2024-02</option>
                            <option>2024-03</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">调整类型：</label>
                        <select class="filter-select" style="width: 150px;">
                            <option>全部类型</option>
                            <option>正向调整</option>
                            <option>负向调整</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">调整时间：</label>
                        <input type="date" class="filter-input" style="width: 150px;">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">至：</label>
                        <input type="date" class="filter-input" style="width: 150px;">
                    </div>
                </div>
                <div class="filter-buttons">
                    <button class="btn btn-primary">查询</button>
                    <button class="btn btn-secondary">重置</button>
                </div>
            </div>

            <!-- 调整明细表格 -->
            <div class="table-container">
                <table class="data-table">
                    <colgroup>
                        <col style="width: 120px;"> <!-- 账单ID -->
                        <col style="width: 200px;"> <!-- 代理商名称 -->
                        <col style="width: 120px;"> <!-- 账单周期 -->
                        <col style="width: 150px;"> <!-- 调整金额 -->
                        <col style="width: 300px;"> <!-- 调整原因 -->
                        <col style="width: 150px;"> <!-- 调整时间 -->
                    </colgroup>
                    <thead>
                        <tr>
                            <th>账单ID</th>
                            <th>代理商名称</th>
                            <th>账单周期</th>
                            <th>调整金额</th>
                            <th>调整原因</th>
                            <th>调整时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>BILL20240701</td>
                            <td>上海润泽科技有限公司</td>
                            <td>2024-01</td>
                            <td><span class="amount-positive">+¥ 500.00</span></td>
                            <td>年度业绩奖励，超额完成目标给予额外分佣奖励</td>
                            <td>2024-02-01 10:30</td>
                        </tr>
                        <tr>
                            <td>BILL20240702</td>
                            <td>北京智汇通科技有限公司</td>
                            <td>2024-01</td>
                            <td><span class="amount-negative">-¥ 120.50</span></td>
                            <td>交易争议退款，扣除对应分佣金额</td>
                            <td>2024-02-03 14:20</td>
                        </tr>
                        <tr>
                            <td>BILL20240703</td>
                            <td>广州环球贸易有限公司</td>
                            <td>2024-01</td>
                            <td><span class="amount-positive">+¥ 200.00</span></td>
                            <td>新客户推荐奖励，成功推荐优质商户</td>
                            <td>2024-02-05 09:15</td>
                        </tr>
                        <tr>
                            <td>BILL20240704</td>
                            <td>上海润泽科技有限公司</td>
                            <td>2024-01</td>
                            <td><span class="amount-negative">-¥ 85.30</span></td>
                            <td>系统错误调整，重复计算分佣需要扣除</td>
                            <td>2024-02-08 16:45</td>
                        </tr>
                        <tr>
                            <td>BILL20240705</td>
                            <td>深圳创新支付有限公司</td>
                            <td>2024-01</td>
                            <td><span class="amount-positive">+¥ 1,000.00</span></td>
                            <td>季度优秀代理商奖励，业务增长突出</td>
                            <td>2024-02-10 11:20</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Tab切换功能
        function switchTab(tabName) {
            // 隐藏所有tab内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有tab导航的active状态
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.classList.remove('active');
            });

            // 显示目标tab内容
            const targetTab = document.getElementById(tabName + '-tab');
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // 设置对应tab导航为active状态
            const targetNavItem = event.target;
            if (targetNavItem) {
                targetNavItem.classList.add('active');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示第一个tab
            const firstTab = document.querySelector('.tab-content');
            const firstNavItem = document.querySelector('.tab-item');

            if (firstTab) {
                firstTab.classList.add('active');
            }
            if (firstNavItem) {
                firstNavItem.classList.add('active');
            }
        });
    </script>
</body>
</html>
