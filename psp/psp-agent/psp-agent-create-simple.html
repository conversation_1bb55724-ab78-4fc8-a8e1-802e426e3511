<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增代理商</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.5;
        }

        .container {
            min-height: 100vh;
            padding: 20px;
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .breadcrumb {
            color: #666;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #007bff;
            text-decoration: none;
        }

        .step-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .step-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .step-nav {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .step-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .step-item.active {
            background: #007bff;
            color: white;
        }

        .step-item.completed {
            background: #28a745;
            color: white;
        }

        .step-item.inactive {
            background: #e9ecef;
            color: #6c757d;
        }

        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }

        .step-content {
            padding: 30px;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .form-label.required::after {
            content: " *";
            color: #dc3545;
        }

        .form-input {
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .form-select {
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            background: white;
            transition: border-color 0.2s;
        }

        .form-select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .form-textarea {
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
            transition: border-color 0.2s;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .checkbox-group {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            margin-top: 30px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-outline {
            background: transparent;
            color: #007bff;
            border: 1px solid #007bff;
        }

        .btn-outline:hover {
            background: #007bff;
            color: white;
        }

        .phone-input-group {
            display: flex;
            gap: 10px;
        }

        .country-code {
            width: 100px;
        }

        .phone-number {
            flex: 1;
        }

        .help-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 4px;
        }

        .radio-card {
            flex: 1;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .radio-card:hover {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .radio-card input[type="radio"] {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 18px;
            height: 18px;
        }

        .radio-card.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .radio-card-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .radio-card-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        .checkbox-card {
            flex: 1;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .checkbox-card:hover {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .checkbox-card input[type="checkbox"] {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 18px;
            height: 18px;
        }

        .checkbox-card.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .checkbox-card-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .checkbox-card-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        .policy-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .policy-option:hover {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .policy-option.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .policy-option input[type="radio"] {
            width: 16px;
            height: 16px;
        }

        .policy-option label {
            font-size: 14px;
            color: #333;
            cursor: pointer;
            margin: 0;
        }

        .policy-subsection {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 15px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
            text-decoration: none;
            display: inline-block;
        }

        .back-button:hover {
            background: #0056b3;
            color: white;
        }
    </style>
</head>
<body>
    <a href="psp-agent-simple.html" class="back-button">← 返回</a>
    
    <div class="container">
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 页面头部 -->
            <div class="page-header">
                <h1 class="page-title">新增代理商</h1>
                <div class="breadcrumb">
                    <a href="psp-agent-simple.html">代理商管理</a> / 新增代理商
                </div>
            </div>

            <!-- 步骤容器 -->
            <div class="step-container">
                <!-- 步骤导航 -->
                <div class="step-header">
                    <div class="step-nav">
                        <div class="step-item active" id="step1">
                            <div class="step-number">1</div>
                            <span>代理商基本信息</span>
                        </div>
                        <div class="step-item inactive" id="step2">
                            <div class="step-number">2</div>
                            <span>选择代理商政策</span>
                        </div>
                    </div>
                </div>

                <!-- 步骤内容 -->
                <div class="step-content">
                    <!-- 第一步：代理商基本信息 -->
                    <div id="step1-content">
                        <form id="agentForm">
                            <!-- 代理商基本信息 -->
                            <div class="form-section">
                                <h3 class="section-title">代理商基本信息</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label required">代理商全称</label>
                                        <input type="text" class="form-input" placeholder="请输入代理商全称" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label required">代理商简称</label>
                                        <input type="text" class="form-input" placeholder="请输入代理商简称" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label required">合同编号</label>
                                        <input type="text" class="form-input" placeholder="请输入合同编号" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label required">代理商类型</label>
                                        <select class="form-select" required>
                                            <option value="">请选择代理商类型</option>
                                            <option value="individual">个人</option>
                                            <option value="enterprise">企业</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label required">代理商等级</label>
                                        <select class="form-select" required>
                                            <option value="">请选择代理商等级</option>
                                            <option value="A">A级 - 高等级</option>
                                            <option value="B">B级 - 中等级</option>
                                            <option value="C">C级 - 低等级</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label required">合约期限</label>
                                        <div style="display: flex; gap: 10px; align-items: center;">
                                            <input type="date" class="form-input" style="flex: 1;" required>
                                            <span>至</span>
                                            <input type="date" class="form-input" style="flex: 1;" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">所属销售</label>
                                        <select class="form-select">
                                            <option value="">请选择所属销售</option>
                                            <option value="sales1">李销售</option>
                                            <option value="sales2">张销售</option>
                                            <option value="sales3">王销售</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">备注</label>
                                        <textarea class="form-textarea" placeholder="请输入备注信息"></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- 代理商管理员 -->
                            <div class="form-section">
                                <h3 class="section-title">代理商管理员</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label required">管理员姓名</label>
                                        <input type="text" class="form-input" placeholder="请输入管理员姓名" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label required">管理员手机</label>
                                        <div class="phone-input-group">
                                            <select class="form-select country-code">
                                                <option value="+86">+86</option>
                                                <option value="+1">+1</option>
                                                <option value="+44">+44</option>
                                                <option value="+852">+852</option>
                                            </select>
                                            <input type="tel" class="form-input phone-number" placeholder="请输入手机号码" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label required">管理员邮箱</label>
                                        <input type="email" class="form-input" placeholder="请输入管理员邮箱" required>
                                    </div>
                                </div>
                            </div>

                            <!-- 结算信息 -->
                            <div class="form-section">
                                <h3 class="section-title">结算信息</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">结算周期</label>
                                        <select class="form-select">
                                            <option value="">请选择结算周期</option>
                                            <option value="monthly">每月20日</option>
                                            <option value="quarterly">每季度末</option>
                                            <option value="custom">自定义</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">结算币种</label>
                                        <select class="form-select">
                                            <option value="">请选择结算币种</option>
                                            <option value="USD">USD - 美元</option>
                                            <option value="CNY">CNY - 人民币</option>
                                            <option value="EUR">EUR - 欧元</option>
                                            <option value="HKD">HKD - 港币</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">银行账户名</label>
                                        <input type="text" class="form-input" placeholder="请输入银行账户名">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">银行账号</label>
                                        <input type="text" class="form-input" placeholder="请输入银行账号">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">开户银行</label>
                                        <input type="text" class="form-input" placeholder="请输入开户银行">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">银行分支</label>
                                        <input type="text" class="form-input" placeholder="请输入银行分支">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">SWIFT Code</label>
                                        <input type="text" class="form-input" placeholder="请输入SWIFT Code">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">路由号码</label>
                                        <input type="text" class="form-input" placeholder="请输入路由号码">
                                    </div>
                                </div>
                            </div>

                            <!-- 其他信息 -->
                            <div class="form-section">
                                <h3 class="section-title">其他信息</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">开票能力</label>
                                        <div class="checkbox-group">
                                            <div class="checkbox-item">
                                                <input type="checkbox" id="invoice1" value="general">
                                                <label for="invoice1">普通发票</label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" id="invoice2" value="vat">
                                                <label for="invoice2">增值税发票</label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" id="invoice3" value="none">
                                                <label for="invoice3">无开票能力</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="form-actions">
                                <a href="psp-agent-simple.html" class="btn btn-outline">取消</a>
                                <button type="button" class="btn btn-primary" onclick="nextStep()">下一步</button>
                            </div>
                        </form>
                    </div>

                    <!-- 第二步：选择代理商政策 -->
                    <div id="step2-content" style="display: none;">
                        <form id="policyForm">
                            <!-- 合作模式选择 -->
                            <div class="form-section">
                                <h3 class="section-title">合作模式</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label required">选择合作模式（可多选）</label>
                                        <div style="display: flex; gap: 20px; margin-top: 10px;">
                                            <div class="checkbox-card" onclick="toggleMode('commission')">
                                                <input type="checkbox" name="cooperationMode" value="commission" id="mode-commission">
                                                <label for="mode-commission">
                                                    <div class="checkbox-card-title">手续费分佣</div>
                                                    <div class="checkbox-card-desc">代理商通过手续费差价获得收益</div>
                                                </label>
                                            </div>
                                            <div class="checkbox-card" onclick="toggleMode('rebate')">
                                                <input type="checkbox" name="cooperationMode" value="rebate" id="mode-rebate">
                                                <label for="mode-rebate">
                                                    <div class="checkbox-card-title">汇率返点</div>
                                                    <div class="checkbox-card-desc">代理商按照汇率返点比例获得收益</div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 手续费分佣政策选择 -->
                            <div id="commission-policy-section" class="form-section" style="display: none;">
                                <h3 class="section-title">手续费分佣政策</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label required">分佣模式</label>
                                        <div style="display: flex; gap: 15px; margin-top: 10px;">
                                            <div class="policy-option" onclick="selectCommissionPolicy('base')">
                                                <input type="radio" name="commissionPolicyType" value="base" id="commission-base">
                                                <label for="commission-base">底价模式</label>
                                            </div>
                                            <div class="policy-option" onclick="selectCommissionPolicy('fixed')">
                                                <input type="radio" name="commissionPolicyType" value="fixed" id="commission-fixed">
                                                <label for="commission-fixed">固定比例模式</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 底价模式政策模版选择 -->
                                <div id="commission-base-options" style="display: none;">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">选择政策模版</label>
                                            <select class="form-select">
                                                <option value="">请选择底价政策模版</option>
                                                <option value="policy1">A级-电商收款-标准底价</option>
                                                <option value="policy2">B级-外贸收款-标准底价</option>
                                                <option value="policy3">C级-物流收款-标准底价</option>
                                                <option value="special1">上海润泽科技-电商收款-特殊底价</option>
                                                <option value="special2">北京智汇通-外贸收款-特殊底价</option>
                                                <option value="new">+ 新增底价政策模版</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- 固定比例模式政策模版选择 -->
                                <div id="commission-fixed-options" style="display: none;">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">选择政策模版</label>
                                            <select class="form-select">
                                                <option value="">请选择固定比例政策模版</option>
                                                <option value="fixed1">A级-电商收款-30%分佣</option>
                                                <option value="fixed2">B级-外贸收款-25%分佣</option>
                                                <option value="fixed3">C级-物流收款-20%分佣</option>
                                                <option value="new">+ 新增固定比例政策模版</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 汇率返点政策选择 -->
                            <div id="rebate-policy-section" class="form-section" style="display: none;">
                                <h3 class="section-title">汇率返点政策</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">选择政策模版</label>
                                        <select class="form-select">
                                            <option value="">请选择汇率返点政策模版</option>
                                            <option value="rebate1">A级-标准汇率返点-50%</option>
                                            <option value="rebate2">B级-标准汇率返点-40%</option>
                                            <option value="rebate3">C级-标准汇率返点-30%</option>
                                            <option value="special1">上海润泽科技-USD→CNH-特殊返点</option>
                                            <option value="special2">北京智汇通-EUR→USD-特殊返点</option>
                                            <option value="new">+ 新增汇率返点政策模版</option>
                                        </select>
                                    </div>
                                </div>
                            </div>



                            <!-- 操作按钮 -->
                            <div class="form-actions">
                                <button type="button" class="btn btn-outline" onclick="prevStep()">上一步</button>
                                <button type="button" class="btn btn-secondary" onclick="saveDraft()">保存草稿</button>
                                <button type="button" class="btn btn-primary" onclick="submitForm()">提交</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function nextStep() {
            // 验证表单
            const form = document.getElementById('agentForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // 切换到第二步
            document.getElementById('step1').className = 'step-item completed';
            document.getElementById('step2').className = 'step-item active';
            document.getElementById('step1-content').style.display = 'none';
            document.getElementById('step2-content').style.display = 'block';
        }

        function prevStep() {
            document.getElementById('step1').className = 'step-item active';
            document.getElementById('step2').className = 'step-item inactive';
            document.getElementById('step1-content').style.display = 'block';
            document.getElementById('step2-content').style.display = 'none';
        }

        function toggleMode(mode) {
            const checkbox = document.getElementById('mode-' + mode);
            const card = event.currentTarget;

            // 切换选中状态
            if (checkbox.checked) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }

            // 显示/隐藏对应的政策选择区域
            if (mode === 'commission') {
                const section = document.getElementById('commission-policy-section');
                section.style.display = checkbox.checked ? 'block' : 'none';
            } else if (mode === 'rebate') {
                const section = document.getElementById('rebate-policy-section');
                section.style.display = checkbox.checked ? 'block' : 'none';
            }
        }

        function selectCommissionPolicy(type) {
            // 清除选中状态
            document.querySelectorAll('#commission-policy-section .policy-option').forEach(option => {
                option.classList.remove('selected');
            });

            // 选中当前选项
            event.currentTarget.classList.add('selected');

            // 隐藏所有选项
            document.getElementById('commission-base-options').style.display = 'none';
            document.getElementById('commission-fixed-options').style.display = 'none';

            // 显示对应选项
            if (type === 'base') {
                document.getElementById('commission-base-options').style.display = 'block';
            } else if (type === 'fixed') {
                document.getElementById('commission-fixed-options').style.display = 'block';
            }
        }



        function checkSpecialPolicy(select, type) {
            if (select.value === 'new') {
                let policyType = '';
                if (type === 'base') {
                    policyType = '底价';
                } else if (type === 'rebate') {
                    policyType = '汇率返点';
                } else if (type === 'hybrid-base') {
                    policyType = '混合模式底价';
                } else if (type === 'hybrid-rebate') {
                    policyType = '混合模式汇率返点';
                }

                if (confirm(`当前没有合适的特殊${policyType}政策，是否要新增特殊${policyType}政策？`)) {
                    // 这里可以跳转到新增特殊政策页面
                    alert(`跳转到新增特殊${policyType}政策页面`);
                } else {
                    select.value = '';
                }
            }
        }

        function saveDraft() {
            alert('草稿已保存');
        }

        function submitForm() {
            // 验证第二步表单
            const policyForm = document.getElementById('policyForm');
            const cooperationModes = document.querySelectorAll('input[name="cooperationMode"]:checked');

            if (cooperationModes.length === 0) {
                alert('请至少选择一种合作模式');
                return;
            }

            // 验证选中的合作模式对应的政策选择
            const selectedModes = Array.from(cooperationModes).map(mode => mode.value);

            if (selectedModes.includes('commission')) {
                const commissionPolicyType = document.querySelector('input[name="commissionPolicyType"]:checked');
                if (!commissionPolicyType) {
                    alert('请选择手续费分佣模式');
                    return;
                }
            }

            if (selectedModes.includes('rebate')) {
                const rebatePolicy = document.querySelector('#rebate-policy-section select');
                if (!rebatePolicy || !rebatePolicy.value) {
                    alert('请选择汇率返点政策模版');
                    return;
                }
            }

            // 提交表单
            alert('代理商创建成功！');
            // 这里可以跳转回列表页面
            window.location.href = 'psp-agent-simple.html';
        }
    </script>
</body>
</html>
