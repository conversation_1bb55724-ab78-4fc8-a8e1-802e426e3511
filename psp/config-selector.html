<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择产品配置</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        
        .container { max-width: 1000px; margin: 0 auto; padding: 20px; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .page-title { font-size: 20px; font-weight: 600; color: #333; margin-bottom: 8px; }
        .page-desc { color: #666; font-size: 14px; }
        
        .search-section { background: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .search-row { display: flex; gap: 15px; align-items: center; }
        .search-group { display: flex; flex-direction: column; min-width: 150px; }
        .search-group label { font-size: 12px; color: #666; margin-bottom: 4px; }
        .search-group input, .search-group select { padding: 8px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
        
        .config-list { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .config-header { background: #f8f9fa; padding: 15px 20px; border-bottom: 1px solid #e9ecef; font-weight: 600; color: #495057; }
        
        .config-item { padding: 20px; border-bottom: 1px solid #f1f3f4; cursor: pointer; transition: all 0.3s; }
        .config-item:last-child { border-bottom: none; }
        .config-item:hover { background: #f8f9fa; }
        .config-item.selected { background: #e3f2fd; border-left: 4px solid #007bff; }
        
        .config-main { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .config-name { font-size: 16px; font-weight: 600; color: #333; }
        .config-rate { font-size: 18px; font-weight: 600; color: #007bff; }
        
        .config-desc { color: #666; font-size: 14px; margin-bottom: 12px; line-height: 1.4; }
        
        .config-params { display: flex; gap: 12px; flex-wrap: wrap; }
        .param-tag { background: #f8f9fa; border: 1px solid #e9ecef; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .param-label { color: #666; }
        .param-value { color: #333; font-weight: 500; margin-left: 4px; }
        
        .config-details { margin-top: 12px; padding-top: 12px; border-top: 1px solid #f1f3f4; }
        .detail-row { display: flex; gap: 20px; margin-bottom: 8px; }
        .detail-item { font-size: 12px; }
        .detail-label { color: #666; }
        .detail-value { color: #333; font-weight: 500; margin-left: 4px; }
        
        .actions-section { background: white; padding: 15px 20px; border-radius: 8px; margin-top: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: right; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; transition: all 0.3s; margin-left: 10px; }
        .btn-outline { background: white; color: #007bff; border: 1px solid #007bff; }
        .btn-outline:hover { background: #007bff; color: white; }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-primary:disabled { background: #ccc; cursor: not-allowed; }
        
        .selected-count { background: #e3f2fd; padding: 10px 15px; border-radius: 4px; margin-bottom: 15px; font-size: 14px; color: #1976d2; }
        
        .empty-state { text-align: center; padding: 40px; color: #6c757d; }
        .filter-tags { display: flex; gap: 8px; margin-top: 10px; }
        .filter-tag { background: #007bff; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="page-title">选择外贸收款配置</div>
            <div class="page-desc">从现有的外贸收款配置中选择适合的计费规则</div>
        </div>
        
        <!-- 搜索筛选 -->
        <div class="search-section">
            <div class="search-row">
                <div class="search-group">
                    <label>收款类型</label>
                    <select onchange="filterConfigs()">
                        <option value="">全部类型</option>
                        <option value="global">全球收款</option>
                        <option value="local">本地收款</option>
                    </select>
                </div>
                <div class="search-group">
                    <label>结算币种</label>
                    <select onchange="filterConfigs()">
                        <option value="">全部币种</option>
                        <option value="USD">USD</option>
                        <option value="HKD">HKD</option>
                        <option value="EUR">EUR</option>
                        <option value="CNY">CNY</option>
                    </select>
                </div>
                <div class="search-group">
                    <label>结算银行</label>
                    <select onchange="filterConfigs()">
                        <option value="">全部银行</option>
                        <option value="DBS">DBS</option>
                        <option value="HSBC">HSBC</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="search-group">
                    <label>费率范围</label>
                    <select onchange="filterConfigs()">
                        <option value="">全部费率</option>
                        <option value="low">0.5% - 1.0%</option>
                        <option value="medium">1.0% - 1.5%</option>
                        <option value="high">1.5% 以上</option>
                    </select>
                </div>
            </div>
            <div class="filter-tags">
                <div class="filter-tag">外贸收款</div>
            </div>
        </div>
        
        <!-- 选择状态 -->
        <div class="selected-count">
            已选择 <strong id="selected-count">0</strong> 个配置（支持多选）
        </div>
        
        <!-- 配置列表 -->
        <div class="config-list">
            <div class="config-header">
                可用的外贸收款配置 (共 8 个)
            </div>
            
            <div class="config-item" onclick="selectConfig(this)">
                <div class="config-main">
                    <div class="config-name">全球收款→HKD→DBS银行</div>
                    <div class="config-rate">0.8% + $0.3</div>
                </div>
                <div class="config-desc">适用于全球范围的外贸收款，结算到香港DBS银行，支持多种支付方式</div>
                <div class="config-params">
                    <div class="param-tag">
                        <span class="param-label">收款类型:</span>
                        <span class="param-value">全球收款</span>
                    </div>
                    <div class="param-tag">
                        <span class="param-label">结算币种:</span>
                        <span class="param-value">HKD</span>
                    </div>
                    <div class="param-tag">
                        <span class="param-label">结算银行:</span>
                        <span class="param-value">DBS</span>
                    </div>
                    <div class="param-tag">
                        <span class="param-label">优先级:</span>
                        <span class="param-value">1</span>
                    </div>
                </div>
                <div class="config-details">
                    <div class="detail-row">
                        <div class="detail-item">
                            <span class="detail-label">最低费用:</span>
                            <span class="detail-value">$2.00</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">最高费用:</span>
                            <span class="detail-value">$500.00</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">支持地区:</span>
                            <span class="detail-value">全球</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">创建时间:</span>
                            <span class="detail-value">2025-01-15</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="config-item" onclick="selectConfig(this)">
                <div class="config-main">
                    <div class="config-name">本地收款→HKD→其他银行</div>
                    <div class="config-rate">1.0% + $0.3</div>
                </div>
                <div class="config-desc">适用于香港本地的外贸收款，结算到其他银行</div>
                <div class="config-params">
                    <div class="param-tag">
                        <span class="param-label">收款类型:</span>
                        <span class="param-value">本地收款</span>
                    </div>
                    <div class="param-tag">
                        <span class="param-label">结算币种:</span>
                        <span class="param-value">HKD</span>
                    </div>
                    <div class="param-tag">
                        <span class="param-label">结算银行:</span>
                        <span class="param-value">其他</span>
                    </div>
                    <div class="param-tag">
                        <span class="param-label">优先级:</span>
                        <span class="param-value">2</span>
                    </div>
                </div>
                <div class="config-details">
                    <div class="detail-row">
                        <div class="detail-item">
                            <span class="detail-label">最低费用:</span>
                            <span class="detail-value">$1.50</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">最高费用:</span>
                            <span class="detail-value">$300.00</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">支持地区:</span>
                            <span class="detail-value">香港</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">创建时间:</span>
                            <span class="detail-value">2025-01-10</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="config-item" onclick="selectConfig(this)">
                <div class="config-main">
                    <div class="config-name">全球收款→USD→HSBC银行</div>
                    <div class="config-rate">0.9% + $0.25</div>
                </div>
                <div class="config-desc">适用于全球范围的外贸收款，美元结算到HSBC银行</div>
                <div class="config-params">
                    <div class="param-tag">
                        <span class="param-label">收款类型:</span>
                        <span class="param-value">全球收款</span>
                    </div>
                    <div class="param-tag">
                        <span class="param-label">结算币种:</span>
                        <span class="param-value">USD</span>
                    </div>
                    <div class="param-tag">
                        <span class="param-label">结算银行:</span>
                        <span class="param-value">HSBC</span>
                    </div>
                    <div class="param-tag">
                        <span class="param-label">优先级:</span>
                        <span class="param-value">3</span>
                    </div>
                </div>
                <div class="config-details">
                    <div class="detail-row">
                        <div class="detail-item">
                            <span class="detail-label">最低费用:</span>
                            <span class="detail-value">$1.80</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">最高费用:</span>
                            <span class="detail-value">$400.00</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">支持地区:</span>
                            <span class="detail-value">全球</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">创建时间:</span>
                            <span class="detail-value">2025-01-08</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 更多配置项... -->
            <div style="text-align: center; padding: 20px; color: #666; border-top: 1px solid #f1f3f4;">
                还有 5 个配置项...
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="actions-section">
            <button class="btn btn-outline" onclick="window.close()">取消</button>
            <button class="btn btn-primary" id="confirm-btn" onclick="confirmSelection()" disabled>确认选择</button>
        </div>
    </div>
    
    <script>
        let selectedConfigs = [];
        
        function selectConfig(item) {
            item.classList.toggle('selected');
            
            const configName = item.querySelector('.config-name').textContent;
            const configRate = item.querySelector('.config-rate').textContent;
            
            if (item.classList.contains('selected')) {
                selectedConfigs.push({
                    name: configName,
                    rate: configRate,
                    element: item
                });
            } else {
                selectedConfigs = selectedConfigs.filter(config => config.name !== configName);
            }
            
            updateSelectedCount();
        }
        
        function updateSelectedCount() {
            const count = selectedConfigs.length;
            document.getElementById('selected-count').textContent = count;
            document.getElementById('confirm-btn').disabled = count === 0;
        }
        
        function filterConfigs() {
            // 实现筛选逻辑
            console.log('筛选配置');
        }
        
        function confirmSelection() {
            if (selectedConfigs.length === 0) {
                alert('请至少选择一个配置');
                return;
            }
            
            // 将选择的配置传回父窗口
            if (window.opener) {
                window.opener.receiveSelectedConfigs(selectedConfigs);
            }
            
            alert(`已选择 ${selectedConfigs.length} 个配置`);
            window.close();
        }
    </script>
</body>
</html>
