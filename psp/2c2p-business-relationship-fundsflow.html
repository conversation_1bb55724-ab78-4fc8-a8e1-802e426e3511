<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2C2P与EX业务关系和资金流</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 60px;
        }

        .section-title {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 3px solid #007bff;
        }

        .subsection {
            margin-bottom: 40px;
        }

        .subsection-title {
            font-size: 20px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 20px;
        }

        /* 业务关系图 */
        .business-relationship {
            position: relative;
            height: 500px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            margin: 30px 0;
            overflow: hidden;
        }

        .entity {
            position: absolute;
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .entity-title {
            font-size: 18px;
            margin-bottom: 5px;
        }

        .entity-subtitle {
            font-size: 12px;
            opacity: 0.9;
        }

        .merchant {
            top: 50px;
            left: 50px;
            width: 150px;
            height: 100px;
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }

        .c2p {
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            width: 180px;
            height: 120px;
            background: linear-gradient(135deg, #fd7e14, #e55a00);
        }

        .ex {
            top: 250px;
            left: 50%;
            transform: translateX(-50%);
            width: 160px;
            height: 100px;
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .ipl {
            bottom: 50px;
            left: 50px;
            width: 140px;
            height: 100px;
            background: linear-gradient(135deg, #6f42c1, #5a2d91);
        }

        .payee {
            bottom: 50px;
            right: 50px;
            width: 140px;
            height: 100px;
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        /* 连接线 */
        .connection {
            position: absolute;
            background: #007bff;
            border-radius: 2px;
            opacity: 0.7;
        }

        .conn1 {
            top: 100px;
            left: 200px;
            width: 150px;
            height: 4px;
        }

        .conn2 {
            top: 170px;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 80px;
        }

        .conn3 {
            bottom: 150px;
            left: 190px;
            width: 120px;
            height: 4px;
            transform: rotate(-30deg);
        }

        .conn4 {
            bottom: 150px;
            right: 190px;
            width: 120px;
            height: 4px;
            transform: rotate(30deg);
        }

        /* 资金流图 */
        .funds-flow {
            position: relative;
            height: 600px;
            background: linear-gradient(180deg, #e3f2fd 0%, #f8f9fa 50%, #e8f5e8 100%);
            border-radius: 15px;
            margin: 30px 0;
            overflow: hidden;
        }

        .flow-step {
            position: absolute;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            text-align: center;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            border: 3px solid white;
        }

        .step-title {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .step-amount {
            font-size: 14px;
            opacity: 0.9;
        }

        .step1 {
            top: 80px;
            left: 80px;
            width: 140px;
            height: 80px;
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }

        .step2 {
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            width: 140px;
            height: 80px;
            background: linear-gradient(135deg, #fd7e14, #e55a00);
        }

        .step3 {
            top: 80px;
            right: 80px;
            width: 140px;
            height: 80px;
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .step4 {
            top: 250px;
            left: 50%;
            transform: translateX(-50%);
            width: 160px;
            height: 100px;
            background: linear-gradient(135deg, #6f42c1, #5a2d91);
        }

        .step5 {
            bottom: 80px;
            left: 100px;
            width: 120px;
            height: 80px;
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .step6 {
            bottom: 80px;
            right: 100px;
            width: 120px;
            height: 80px;
            background: linear-gradient(135deg, #17a2b8, #138496);
        }

        /* 箭头 */
        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
        }

        .arrow-right {
            border-left: 15px solid #007bff;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
        }

        .arrow-down {
            border-top: 15px solid #007bff;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
        }

        .arr1 {
            top: 115px;
            left: 250px;
        }

        .arr2 {
            top: 115px;
            right: 250px;
        }

        .arr3 {
            top: 180px;
            left: 50%;
            transform: translateX(-50%);
        }

        .arr4 {
            bottom: 200px;
            left: 200px;
            transform: rotate(-45deg);
        }

        .arr5 {
            bottom: 200px;
            right: 200px;
            transform: rotate(45deg);
        }



        /* 响应式 */
        @media (max-width: 768px) {
            .business-relationship,
            .funds-flow {
                height: 400px;
            }

            .entity,
            .flow-step {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>2C2P与EX业务关系和资金流</h1>
            <p>Business Relationship & Funds Flow Analysis</p>
        </div>

        <div class="content">
            <!-- 业务关系部分 -->
            <div class="section">
                <h2 class="section-title">业务关系架构 (Business Relationship)</h2>

                <div class="business-relationship">
                    <div class="entity merchant">
                        <div class="entity-title">商户</div>
                        <div class="entity-subtitle">Merchant</div>
                    </div>

                    <div class="entity c2p">
                        <div class="entity-title">2C2P</div>
                        <div class="entity-subtitle">支付服务商</div>
                    </div>

                    <div class="entity ex">
                        <div class="entity-title">EX</div>
                        <div class="entity-subtitle">技术服务商</div>
                    </div>

                    <div class="entity ipl">
                        <div class="entity-title">IPL</div>
                        <div class="entity-subtitle">资金通道</div>
                    </div>

                    <div class="entity payee">
                        <div class="entity-title">收款人</div>
                        <div class="entity-subtitle">Payee</div>
                    </div>

                    <!-- 连接线 -->
                    <div class="connection conn1"></div>
                    <div class="connection conn2"></div>
                    <div class="connection conn3"></div>
                    <div class="connection conn4"></div>
                </div>
            </div>

            <!-- 资金流部分 -->
            <div class="section">
                <h2 class="section-title">资金流向 (Funds Flow)</h2>

                <div class="funds-flow">
                    <div class="flow-step step1">
                        <div class="step-title">商户转账</div>
                        <div class="step-amount">$10,000</div>
                    </div>

                    <div class="flow-step step2">
                        <div class="step-title">虚拟账户</div>
                        <div class="step-amount">VA托管</div>
                    </div>

                    <div class="flow-step step3">
                        <div class="step-title">IPL主账户</div>
                        <div class="step-amount">资金池化</div>
                    </div>

                    <div class="flow-step step4">
                        <div class="step-title">付款执行</div>
                        <div class="step-amount">$9,930</div>
                    </div>

                    <div class="flow-step step5">
                        <div class="step-title">2C2P收入</div>
                        <div class="step-amount">$30</div>
                    </div>

                    <div class="flow-step step6">
                        <div class="step-title">EX收入</div>
                        <div class="step-amount">$20</div>
                    </div>

                    <!-- 箭头 -->
                    <div class="arrow arrow-right arr1"></div>
                    <div class="arrow arrow-right arr2"></div>
                    <div class="arrow arrow-down arr3"></div>
                    <div class="arrow arrow-right arr4"></div>
                    <div class="arrow arrow-right arr5"></div>
                </div>
            </div>
        </div>
    </div>


</body>
</html>
