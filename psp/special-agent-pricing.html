<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特殊代理价管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            display: flex;
            min-height: 100vh;
        }

        /* 左侧菜单 */
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid #34495e;
            margin-bottom: 20px;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: #ecf0f1;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: block;
            padding: 12px 20px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }

        .nav-link:hover,
        .nav-link.active {
            background: #34495e;
            color: #3498db;
            border-left-color: #3498db;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 250px;
            flex: 1;
            padding: 20px;
            min-height: 100vh;
        }

        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .card-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .card-body {
            padding: 20px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 搜索区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .search-grid {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .search-item {
            display: flex;
            flex-direction: column;
            min-width: 150px;
        }

        .search-item label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }

        .search-item input,
        .search-item select {
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .data-table th {
            background: #f8f9fa;
            padding: 16px 12px;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #dee2e6;
            font-size: 14px;
        }

        .data-table td {
            padding: 16px 12px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
            vertical-align: top;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            margin-bottom: 40px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 5px;
        }

        .pagination-controls button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .pagination-controls button:hover {
            background: #f8f9fa;
        }

        .pagination-controls button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        /* 操作链接 */
        .action-link {
            color: #007bff;
            text-decoration: none;
            margin-right: 8px;
            font-size: 13px;
            transition: color 0.3s;
        }

        .action-link:hover {
            color: #0056b3;
        }

        .action-link.danger {
            color: #dc3545;
        }

        .action-link.danger:hover {
            color: #c82333;
        }

        /* 多行内容样式 */
        .multi-line-content {
            font-size: 13px;
            line-height: 1.6;
            max-width: 100%;
        }

        .multi-line-content .line-item {
            margin-bottom: 2px;
        }

        .multi-line-content .more-indicator {
            color: #6c757d;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- 左侧菜单 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">代理商管理</div>
        </div>
        <nav>
            <div class="nav-item">
                <a href="#" class="nav-link active">特殊代理价</a>
            </div>
        </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">特殊代理价</h3>
                <div style="display: flex; gap: 10px;">
                    <button class="btn btn-primary btn-sm" onclick="addSpecialPricing()">新增特殊代理价</button>
                    <button class="btn btn-success btn-sm" onclick="exportData()">导出</button>
                </div>
            </div>
            <div class="card-body">
                <!-- 搜索筛选 -->
                <div class="search-section">
                    <div class="search-grid">
                        <div class="search-item">
                            <label>代理商名称：</label>
                            <input type="text" id="agentName" placeholder="请输入代理商名称">
                        </div>
                        <div class="search-item">
                            <label>代理商ID：</label>
                            <input type="text" id="agentId" placeholder="请输入代理商ID">
                        </div>
                        <div class="search-item">
                            <label>更新时间：</label>
                            <input type="date" id="updateDate">
                        </div>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button class="btn btn-primary" onclick="searchData()">查询</button>
                        <button class="btn" style="background: #6c757d; color: white;" onclick="resetSearch()">重置</button>
                    </div>
                </div>

                <!-- 表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <colgroup>
                            <col style="width: 120px;"> <!-- 代理商ID -->
                            <col style="width: 180px;"> <!-- 代理商名称 -->
                            <col style="width: 300px;"> <!-- 特殊底价 -->
                            <col style="width: 300px;"> <!-- 特殊汇率返点 -->
                            <col style="width: 140px;"> <!-- 更新时间 -->
                            <col style="width: 150px;"> <!-- 操作 -->
                        </colgroup>
                        <thead>
                            <tr>
                                <th>代理商ID</th>
                                <th>代理商名称</th>
                                <th>特殊底价</th>
                                <th>特殊汇率返点</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                            <tr>
                                <td style="overflow: hidden; text-overflow: ellipsis;">AGT001</td>
                                <td style="overflow: hidden; text-overflow: ellipsis;" title="上海润泽科技有限公司">上海润泽科技有限公司</td>
                                <td>
                                    <div class="multi-line-content">
                                        <div class="line-item">电商收款: 0.18%</div>
                                        <div class="line-item">外贸收款: 0.22%</div>
                                        <div class="more-indicator">...</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="multi-line-content">
                                        <div class="line-item">USD→CNH: 55%</div>
                                        <div class="line-item">EUR→USD: 55%</div>
                                        <div class="more-indicator">...</div>
                                    </div>
                                </td>
                                <td style="white-space: nowrap;">
                                    <div>2025-03-15</div>
                                    <div style="color: #666; font-size: 12px;">10:30</div>
                                </td>
                                <td style="white-space: nowrap;">
                                    <a href="#" class="action-link" onclick="viewDetails('AGT001')">详情</a>
                                    <a href="#" class="action-link" onclick="editAgent('AGT001')">编辑</a>
                                    <a href="#" class="action-link danger" onclick="deleteAgent('AGT001')">删除</a>
                                </td>
                            </tr>
                            <tr>
                                <td style="overflow: hidden; text-overflow: ellipsis;">AGT002</td>
                                <td style="overflow: hidden; text-overflow: ellipsis;" title="北京智汇通科技有限公司">北京智汇通科技有限公司</td>
                                <td>
                                    <div class="multi-line-content">
                                        <div class="line-item">跨境收款: 0.25%</div>
                                        <div class="line-item">本地收款: 0.30%</div>
                                        <div class="more-indicator">...</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="multi-line-content">
                                        <div class="line-item">GBP→USD: 45%</div>
                                        <div class="line-item">HKD→CNH: 45%</div>
                                        <div class="more-indicator">...</div>
                                    </div>
                                </td>
                                <td style="white-space: nowrap;">
                                    <div>2025-03-14</div>
                                    <div style="color: #666; font-size: 12px;">09:20</div>
                                </td>
                                <td style="white-space: nowrap;">
                                    <a href="#" class="action-link" onclick="viewDetails('AGT002')">详情</a>
                                    <a href="#" class="action-link" onclick="editAgent('AGT002')">编辑</a>
                                    <a href="#" class="action-link danger" onclick="deleteAgent('AGT002')">删除</a>
                                </td>
                            </tr>
                            <tr>
                                <td style="overflow: hidden; text-overflow: ellipsis;">AGT003</td>
                                <td style="overflow: hidden; text-overflow: ellipsis;" title="广州环球贸易有限公司">广州环球贸易有限公司</td>
                                <td>
                                    <div class="multi-line-content">
                                        <div class="line-item">物流收款: 0.28%</div>
                                        <div class="line-item">平台收款: 0.32%</div>
                                        <div class="more-indicator">...</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="multi-line-content">
                                        <div class="line-item">JPY→USD: 35%</div>
                                        <div class="line-item">USD→HKD: 35%</div>
                                        <div class="more-indicator">...</div>
                                    </div>
                                </td>
                                <td style="white-space: nowrap;">
                                    <div>2025-03-12</div>
                                    <div style="color: #666; font-size: 12px;">15:45</div>
                                </td>
                                <td style="white-space: nowrap;">
                                    <a href="#" class="action-link" onclick="viewDetails('AGT003')">详情</a>
                                    <a href="#" class="action-link" onclick="editAgent('AGT003')">编辑</a>
                                    <a href="#" class="action-link danger" onclick="deleteAgent('AGT003')">删除</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        共 <span style="color: #333; font-weight: 600;">3</span> 条记录，每页显示 <span style="color: #333; font-weight: 600;">10</span> 条
                    </div>
                    <div class="pagination-controls">
                        <button onclick="prevPage()">上一页</button>
                        <button class="active">1</button>
                        <button onclick="nextPage()">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 新增特殊代理价
        function addSpecialPricing() {
            alert('跳转到新增特殊代理价页面');
        }

        // 导出数据
        function exportData() {
            alert('导出特殊代理价数据');
        }

        // 搜索数据
        function searchData() {
            const agentName = document.getElementById('agentName').value;
            const agentId = document.getElementById('agentId').value;
            const updateDate = document.getElementById('updateDate').value;
            
            console.log('搜索条件:', { agentName, agentId, updateDate });
            alert('执行搜索操作');
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('agentName').value = '';
            document.getElementById('agentId').value = '';
            document.getElementById('updateDate').value = '';
        }

        // 查看详情
        function viewDetails(agentId) {
            alert(`查看代理商 ${agentId} 的详情`);
        }

        // 编辑代理商
        function editAgent(agentId) {
            alert(`编辑代理商 ${agentId}`);
        }

        // 删除代理商
        function deleteAgent(agentId) {
            if (confirm(`确定要删除代理商 ${agentId} 吗？`)) {
                alert(`删除代理商 ${agentId}`);
            }
        }

        // 分页功能
        function prevPage() {
            alert('上一页');
        }

        function nextPage() {
            alert('下一页');
        }
    </script>
</body>
</html>
