<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EX运营后台 - 聚合支付管理系统</title>
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 主布局容器 */
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 左侧边栏 */
        .sidebar {
            width: 260px;
            background: linear-gradient(180deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .sidebar.collapsed {
            width: 70px;
        }

        /* 侧边栏头部 */
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 20px 10px;
            justify-content: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
        }

        .logo-text {
            font-size: 18px;
            font-weight: 600;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .logo-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        /* 用户信息 */
        .user-info {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidebar.collapsed .user-info {
            padding: 15px 10px;
            justify-content: center;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .user-details {
            flex: 1;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .user-details {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .user-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .user-role {
            font-size: 12px;
            opacity: 0.7;
        }

        /* 导航菜单 */
        .nav-menu {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 4px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s ease;
            position: relative;
        }

        .sidebar.collapsed .nav-link {
            padding: 12px 20px;
            justify-content: center;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border-right: 3px solid #6f42c1;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .sidebar.collapsed .nav-icon {
            margin-right: 0;
        }

        .nav-text {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .nav-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .nav-arrow {
            font-size: 12px;
            transition: transform 0.2s ease, opacity 0.3s ease;
        }

        .sidebar.collapsed .nav-arrow {
            opacity: 0;
        }

        /* 子菜单 */
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.1);
        }

        .nav-submenu.open {
            max-height: 800px;
        }

        .nav-submenu .nav-link {
            padding: 10px 20px 10px 52px;
            font-size: 13px;
        }

        .sidebar.collapsed .nav-submenu .nav-link {
            padding: 10px 20px;
        }

        .nav-submenu .nav-link:before {
            content: '•';
            margin-right: 8px;
            opacity: 0.6;
        }

        /* 三级菜单 */
        .nav-submenu .nav-submenu {
            background: rgba(0, 0, 0, 0.2);
        }

        .nav-submenu .nav-submenu .nav-link {
            padding: 8px 20px 8px 72px;
            font-size: 12px;
        }

        .sidebar.collapsed .nav-submenu .nav-submenu .nav-link {
            padding: 8px 20px;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 260px;
            transition: margin-left 0.3s ease;
            min-height: 100vh;
        }

        .sidebar.collapsed + .main-content {
            margin-left: 70px;
        }

        /* 顶部导航栏 */
        .top-navbar {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
        }

        .top-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        /* 内容区域 */
        .content-area {
            padding: 30px;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .stat-icon.primary {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        }

        .stat-icon.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .stat-icon.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .stat-icon.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
            margin-bottom: 12px;
        }

        .stat-change {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .stat-change.positive {
            background: #d4edda;
            color: #155724;
        }

        .stat-change.negative {
            background: #f8d7da;
            color: #721c24;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            margin-bottom: 24px;
            overflow: hidden;
        }

        .card-header {
            padding: 20px 24px;
            border-bottom: 1px solid #eee;
            background: #fafbfc;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .card-body {
            padding: 24px;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* Tab样式 */
        .tab-nav {
            display: flex;
            border-bottom: 1px solid #eee;
            background: white;
            border-radius: 8px 8px 0 0;
        }

        .tab-btn {
            padding: 15px 20px;
            border: none;
            background: none;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: #666;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .tab-btn.active {
            border-bottom-color: #6f42c1;
            color: #6f42c1;
            font-weight: 600;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 页面内容隐藏/显示 */
        .page-content {
            display: none;
        }

        .page-content.active {
            display: block;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                width: 260px;
                transform: translateX(-100%);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar.collapsed + .main-content {
                margin-left: 0;
            }

            .content-area {
                padding: 20px 15px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧边栏 -->
        <div class="sidebar" id="sidebar">
            <!-- 用户信息 -->
            <div class="user-info">
                <div class="user-avatar">E</div>
                <div class="user-details">
                    <div class="user-name">EX ADMIN</div>
                    <div class="user-role">EX聚合支付</div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="#" class="nav-link active" onclick="showPage('dashboard')">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">业务概览</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">租户管理</span>
                        <span class="nav-arrow">▼</span>
                    </a>
                    <div class="nav-submenu">
                        <!-- 租户管理 -->
                        <div class="nav-item">
                            <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                                <span class="nav-text">租户管理</span>
                                <span class="nav-arrow">▼</span>
                            </a>
                            <div class="nav-submenu">
                                <a href="#" class="nav-link" onclick="showPage('customer-info')">
                                    <span class="nav-text">租户基本信息</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('customer-products')">
                                    <span class="nav-text">租户签约产品</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('customer-rate-config')">
                                    <span class="nav-text">租户费率配置</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('rate-config-orders')">
                                    <span class="nav-text">费率配置工单</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('customer-exchange-config')">
                                    <span class="nav-text">租户汇率配置</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('exchange-config-orders')">
                                    <span class="nav-text">汇率配置工单</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('customer-rate-query')">
                                    <span class="nav-text">租户收费查询</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('customer-exchange-query')">
                                    <span class="nav-text">租户汇率查询</span>
                                </a>
                            </div>
                        </div>

                        <!-- 租户商户管理 -->
                        <div class="nav-item">
                            <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                                <span class="nav-text">租户商户管理</span>
                                <span class="nav-arrow">▼</span>
                            </a>
                            <div class="nav-submenu">
                                <a href="#" class="nav-link" onclick="showPage('merchant-info')">
                                    <span class="nav-text">商户查询</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('merchant-products-dev')">
                                    <span class="nav-text">商户签约产品</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('merchant-rate-dev')">
                                    <span class="nav-text">商户收费配置</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('merchant-exchange-dev')">
                                    <span class="nav-text">商户汇率配置</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('merchant-fee-dev')">
                                    <span class="nav-text">商户收费查询</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('merchant-exchange-query-dev')">
                                    <span class="nav-text">商户汇率查询</span>
                                </a>
                            </div>
                        </div>

                        <!-- 租户账单 -->
                        <div class="nav-item">
                            <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                                <span class="nav-text">租户账单</span>
                                <span class="nav-arrow">▼</span>
                            </a>
                            <div class="nav-submenu">
                                <a href="#" class="nav-link" onclick="showPage('tenant-bills')">
                                    <span class="nav-text">租户账单</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('tenant-bill-details')">
                                    <span class="nav-text">租户账单明细</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showPage('tenant-settlements')">
                                    <span class="nav-text">租户结算记录</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                        <span class="nav-icon">🏢</span>
                        <span class="nav-text">服务商管理</span>
                        <span class="nav-arrow">▼</span>
                    </a>
                    <div class="nav-submenu">
                        <a href="#" class="nav-link" onclick="showPage('provider-info')">
                            <span class="nav-text">服务商查询</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showPage('provider-bills')">
                            <span class="nav-text">服务商账单</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showPage('provider-bill-details')">
                            <span class="nav-text">服务商账单明细</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showPage('provider-settlements')">
                            <span class="nav-text">服务商结算记录</span>
                        </a>
                    </div>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                        <span class="nav-icon">👨‍💼</span>
                        <span class="nav-text">租户代理商</span>
                        <span class="nav-arrow">▼</span>
                    </a>
                    <div class="nav-submenu">
                        <a href="#" class="nav-link" onclick="showPage('tenant-agent-info')">
                            <span class="nav-text">代理商签约信息</span>
                        </a>
                        <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                            <span class="nav-text">分佣配置查询</span>
                            <span class="nav-arrow">▼</span>
                        </a>
                        <div class="nav-submenu">
                            <a href="#" class="nav-link" onclick="showPage('tenant-agent-standard-rates')">
                                <span class="nav-text">标准产品底价（废弃）</span>
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('tenant-agent-special-rates')">
                                <span class="nav-text">代理商特殊底价（废弃）</span>
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('tenant-agent-special-rates')">
                                <span class="nav-text">交易分佣配置查询</span>
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('tenant-agent-special-rates')">
                                <span class="nav-text">汇率返点配置查询</span>
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('tenant-agent-special-rates')">
                                <span class="nav-text">代理政策查询</span>
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('tenant-agent-special-rates')">
                                <span class="nav-text">政策分配查询</span>
                            </a>
                        </div>
                        <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                            <span class="nav-text">代理商账单</span>
                            <span class="nav-arrow">▼</span>
                        </a>
                        <div class="nav-submenu">
                            <a href="#" class="nav-link" onclick="showPage('tenant-agent-bill-records')">
                                <span class="nav-text">代理商账单记录</span>
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('tenant-agent-bill-details')">
                                <span class="nav-text">代理商账单明细</span>
                            </a>
                        </div>
                        <a href="#" class="nav-link" onclick="showPage('tenant-agent-settlement-records')">
                            <span class="nav-text">代理商结算记录</span>
                        </a>
                    </div>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                        <span class="nav-icon">🔍</span>
                        <span class="nav-text">交易查询</span>
                        <span class="nav-arrow">▼</span>
                    </a>
                    <div class="nav-submenu">
                        <a href="#" class="nav-link" onclick="showPage('receive-transactions')">
                            <span class="nav-text">收款交易查询</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showPage('payment-transactions')">
                            <span class="nav-text">付款交易查询</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showPage('settlement-transactions')">
                            <span class="nav-text">结汇交易查询</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showPage('exchange-transactions')">
                            <span class="nav-text">换汇交易查询</span>
                        </a>
                    </div>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showPage('reports')">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">报表分析</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showPage('settings')">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">系统设置</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="top-navbar">
                <h1 class="page-title" id="pageTitle">业务概览</h1>
            </div>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 1. 业务概览页面 -->
                <div id="dashboard" class="page-content active">
                    <!-- 核心业务指标 -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon primary">💰</div>
                            </div>
                            <div class="stat-value">$2.8M</div>
                            <div class="stat-label">本月交易总额</div>
                            <div class="stat-change positive">+15.2%</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon success">🏦</div>
                            </div>
                            <div class="stat-value">156</div>
                            <div class="stat-label">服务PSP数量</div>
                            <div class="stat-change positive">+3</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon warning">👥</div>
                            </div>
                            <div class="stat-value">2,847</div>
                            <div class="stat-label">活跃租户数</div>
                            <div class="stat-change positive">+45</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon info">📊</div>
                            </div>
                            <div class="stat-value">$45.6K</div>
                            <div class="stat-label">本月收入</div>
                            <div class="stat-change positive">+8.7%</div>
                        </div>
                    </div>

                    <!-- 今日交易概览 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">今日交易概览</h3>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">交易类型</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">交易笔数</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">交易金额</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">成功率</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">平均金额</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">收款交易</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">1,245</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$890,560</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">99.2%</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$715</td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">付款交易</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">456</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$234,890</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">98.9%</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$515</td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">结汇交易</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">89</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$145,670</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">100%</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$1,637</td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">换汇交易</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">234</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$567,340</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">99.6%</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$2,424</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 服务商概览 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">服务商概览</h3>
                            <button class="btn btn-primary btn-sm">查看全部</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">服务商名称</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">今日交易量</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">今日交易额</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">成功率</th>
                                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">Alpha Payment</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">456</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$234,560</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">99.1%</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                                <span style="padding: 4px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 12px;">正常</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">Beta Finance</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">234</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$156,780</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">98.7%</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                                <span style="padding: 4px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 12px;">正常</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">Gamma Pay</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">123</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">$89,670</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">97.2%</td>
                                            <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                                <span style="padding: 4px 8px; background: #fff3cd; color: #856404; border-radius: 12px; font-size: 12px;">异常</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 2. 租户管理模块 -->
                <!-- 2.1 租户基本信息 -->
                <div id="customer-info" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户ID</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入客户ID">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户名称</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入租户名称">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户状态</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部状态</option>
                                    <option value="active">活跃</option>
                                    <option value="inactive">非活跃</option>
                                    <option value="suspended">暂停</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                            </div>
                        </div>
                    </div>

                    <!-- 客户信息列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">客户信息列表</h3>
                            <button class="btn btn-primary btn-sm">新增客户</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">客户ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">客户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">业务类型</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">注册时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">联系人</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">联系方式</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">月交易额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">CUST_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC跨境电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">跨境电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-01-15</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">张经理</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">+86-138-0000-1234</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$234,560</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">活跃</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">CUST_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ国际贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">国际贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-02-20</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">李总监</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">+86-139-0000-5678</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$156,780</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">活跃</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">CUST_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">DEF科技服务</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">软件服务</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-03-10</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">王主管</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">+86-150-0000-9012</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$89,670</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">非活跃</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 2.2 客户签约产品 -->
                <div id="customer-products" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">客户名称</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入客户名称">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">产品类型</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部产品</option>
                                    <option value="receive">收款产品</option>
                                    <option value="payment">付款产品</option>
                                    <option value="settlement">结汇产品</option>
                                    <option value="exchange">换汇产品</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">签约状态</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部状态</option>
                                    <option value="active">生效中</option>
                                    <option value="pending">待生效</option>
                                    <option value="expired">已过期</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出签约</button>
                            </div>
                        </div>
                    </div>

                    <!-- 客户签约产品列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">客户签约产品</h3>
                            <button class="btn btn-primary btn-sm">新增签约</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">客户ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">客户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">产品类型</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">产品名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">签约时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">生效时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">到期时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">CUST_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC跨境电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">收款产品</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">跨境收款标准版</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-01-15</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-01-20</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2025-01-20</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">生效中</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">CUST_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC跨境电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">换汇产品</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">多币种换汇服务</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-02-01</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-02-05</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2025-02-05</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">生效中</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">CUST_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ国际贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">付款产品</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">国际付款专业版</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-03-01</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-03-10</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2025-03-10</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">待生效</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 2.5 服务商管理模块 -->
                <!-- 2.5.1 服务商查询 -->
                <div id="provider-info" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">服务商名称</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入服务商名称">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">服务商类型</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部类型</option>
                                    <option value="payment">支付服务商</option>
                                    <option value="risk">风控服务商</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">签约时间</label>
                                <input type="date" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" value="2024-12-20">
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出服务商</button>
                            </div>
                        </div>
                    </div>

                    <!-- 服务商信息列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">服务商信息列表</h3>
                            <button class="btn btn-primary btn-sm">新增服务商</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商类型</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商签约时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">联系人</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">联系方式</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">支付服务商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-01-15</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">张经理</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">+86-138-0000-1234</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">正常</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Beta Finance</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">支付服务商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-02-20</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">李总监</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">+86-139-0000-5678</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">正常</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Gamma Risk Control</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">风控服务商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-03-10</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">王主管</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">+86-150-0000-9012</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">暂停</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 2.6 租户商户管理模块 -->
                <!-- 2.6.1 商户信息查询 -->
                <div id="merchant-info" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">商户名称</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入商户名称">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">商户编号</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入商户编号">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户名称</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部租户</option>
                                    <option value="TENANT_001">ABC跨境电商</option>
                                    <option value="TENANT_002">XYZ国际贸易</option>
                                    <option value="TENANT_003">DEF科技服务</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">创建时间</label>
                                <input type="date" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" value="2024-12-20">
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出商户</button>
                            </div>
                        </div>
                    </div>

                    <!-- 商户信息列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">商户信息列表</h3>
                            <button class="btn btn-primary btn-sm">新增商户</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户编号</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户手机号</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户邮箱</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">租户ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">租户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">创建时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">星辰电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MCH_20241201_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">138****5678</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">star***@email.com</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC跨境电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-01 10:30</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">蓝海贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MCH_20241205_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">139****1234</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">blue***@trade.com</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ国际贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-05 14:20</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">智能科技</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MCH_20241210_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">150****9876</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">smart***@tech.com</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">DEF科技服务</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-10 09:15</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">绿洲商城</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MCH_20241215_004</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">186****4567</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">oasis***@mall.com</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC跨境电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-15 16:45</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">金融服务</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MCH_20241218_005</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">177****8901</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">fin***@service.com</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ国际贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-18 11:30</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 开发中页面 -->
                <div id="merchant-products-dev" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">商户签约产品查询</h3>
                        </div>
                        <div class="card-body">
                            <div style="text-align: center; padding: 60px 20px; color: #666;">
                                <div style="font-size: 48px; margin-bottom: 20px;">🚧</div>
                                <h3 style="margin-bottom: 10px; color: #333;">功能开发中</h3>
                                <p>该功能正在开发中，敬请期待...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="merchant-rate-dev" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">适用范围</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部范围</option>
                                    <option value="special_tenant_merchant">特殊租户商户</option>
                                    <option value="special_merchant">特殊商户</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户名称</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部租户</option>
                                    <option value="TENANT_001">ABC跨境电商</option>
                                    <option value="TENANT_002">XYZ国际贸易</option>
                                    <option value="TENANT_003">DEF科技服务</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">商户名称</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入商户名称">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">产品名称</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部产品</option>
                                    <option value="foreign_trade_receive">外贸收款</option>
                                    <option value="logistics_receive">物流收款</option>
                                    <option value="ecommerce_receive">电商平台收款</option>
                                    <option value="developer_receive">开发者平台收款</option>
                                    <option value="ad_alliance_receive">广告联盟收款</option>
                                    <option value="global_payment">全球付款</option>
                                    <option value="local_payment">本地付款</option>
                                    <option value="settlement">结汇</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出收费查询</button>
                            </div>
                        </div>
                    </div>

                    <!-- 租户商户收费查询列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">租户商户收费查询列表</h3>
                            <button class="btn btn-primary btn-sm">导出收费数据</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">适用范围</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">租户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">租户ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户账户号码</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">产品名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">更新时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #e7f3ff; color: #0066cc;">特殊租户商户</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC跨境电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">星辰电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MCH_20241201_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">外贸收款</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-01 10:30</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看收费详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">特殊商户</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ国际贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">蓝海贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MCH_20241205_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">电商平台收款</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-05 14:20</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看收费详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #e7f3ff; color: #0066cc;">特殊租户商户</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">DEF科技服务</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">智能科技</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MCH_20241210_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">开发者平台收款</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-10 09:15</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看收费详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">特殊商户</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC跨境电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">绿洲商城</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MCH_20241215_004</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-15 16:45</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">物流收款</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看收费详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #e7f3ff; color: #0066cc;">特殊租户商户</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ国际贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">广告有限公司</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MCH_20241218_005</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">广告联盟收款</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-18 11:30</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看收费详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">特殊商户</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">DEF科技服务</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">智能科技</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MCH_20241220_006</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">全球付款</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 14:45</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看收费详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #e7f3ff; color: #0066cc;">特殊租户商户</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC跨境电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">M0001商户</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MCH_20241222_007</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">本地付款</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-22 15:00</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看收费详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">特殊商户</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ国际贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">M0001商户</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MCH_20241223_008</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">结汇服务</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-23 10:15</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看收费详情</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="merchant-exchange-dev" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">商户汇率查询</h3>
                        </div>
                        <div class="card-body">
                            <div style="text-align: center; padding: 60px 20px; color: #666;">
                                <div style="font-size: 48px; margin-bottom: 20px;">🚧</div>
                                <h3 style="margin-bottom: 10px; color: #333;">功能开发中</h3>
                                <p>该功能正在开发中，敬请期待...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="merchant-fee-dev" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">商户收费查询</h3>
                        </div>
                        <div class="card-body">
                            <div style="text-align: center; padding: 60px 20px; color: #666;">
                                <div style="font-size: 48px; margin-bottom: 20px;">🚧</div>
                                <h3 style="margin-bottom: 10px; color: #333;">功能开发中</h3>
                                <p>该功能正在开发中，敬请期待...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="merchant-exchange-query-dev" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">商户汇率查询</h3>
                        </div>
                        <div class="card-body">
                            <div style="text-align: center; padding: 60px 20px; color: #666;">
                                <div style="font-size: 48px; margin-bottom: 20px;">🚧</div>
                                <h3 style="margin-bottom: 10px; color: #333;">功能开发中</h3>
                                <p>该功能正在开发中，敬请期待...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 3. 交易查询模块 -->
                <!-- 3.1 收款交易查询 -->
                <div id="receive-transactions" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">交易编号</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入交易编号">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">服务商名称</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部服务商</option>
                                    <option value="PSP_001">Alpha Payment</option>
                                    <option value="PSP_002">Beta Finance</option>
                                    <option value="PSP_003">Gamma Pay</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">交易状态</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部状态</option>
                                    <option value="success">成功</option>
                                    <option value="pending">处理中</option>
                                    <option value="failed">失败</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 150px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">交易时间</label>
                                <input type="date" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" value="2024-12-20">
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出交易</button>
                            </div>
                        </div>
                    </div>

                    <!-- 收款交易列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">收款交易查询</h3>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易编号</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户账户号码</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">汇款人名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">汇款人银行名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">收款账户</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易金额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易手续费</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易处理费</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">汇率</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商交易编号</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">更新时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">RCV_20241220_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC跨境电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ACC_001_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">John Smith</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Chase Bank</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">****1234</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$1,000 USD</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$3.00</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$2.50</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">7.2456</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">成功</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_TXN_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 10:30</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 10:35</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">RCV_20241220_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Beta Finance</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ国际贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ACC_002_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">David Johnson</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Bank of America</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">****5678</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€2,500 EUR</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€7.50</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€5.00</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">1.0856</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">处理中</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_TXN_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 11:15</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 11:20</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 3.2 付款交易查询 -->
                <div id="payment-transactions" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">交易编号</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入交易编号">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">服务商名称</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部服务商</option>
                                    <option value="PSP_001">Alpha Payment</option>
                                    <option value="PSP_002">Beta Finance</option>
                                    <option value="PSP_003">Gamma Pay</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">商户名称</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入商户名称">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">交易状态</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部状态</option>
                                    <option value="success">成功</option>
                                    <option value="pending">处理中</option>
                                    <option value="failed">失败</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出交易</button>
                            </div>
                        </div>
                    </div>

                    <!-- 付款交易列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">付款交易查询</h3>
                            <button class="btn btn-primary btn-sm">实时刷新</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易编号</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户账户号码</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">收款人账户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">收款人账户号码</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">付款金额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">预计到账金额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易手续费</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易处理费</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">汇率</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商交易编号</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">更新时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">主账户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">主账户账户号码</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PAY_20241220_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC电商有限公司</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ACC_001_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Global Supplier Ltd</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">****9876</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$5,000 USD</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$4,975 USD</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$15.00</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$10.00</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">1.0000</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">成功</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_PAY_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 14:30</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 14:35</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC主账户</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MAIN_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PAY_20241220_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Beta Finance</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ贸易公司</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ACC_002_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">European Trading Co</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">****5432</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€3,500 EUR</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€3,465 EUR</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€25.00</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€10.00</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">1.0856</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">处理中</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_PAY_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 15:45</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 15:50</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ主账户</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MAIN_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 3.4 换汇交易查询 -->
                <div id="exchange-transactions" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">交易ID</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入交易ID">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">服务商名称</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部服务商</option>
                                    <option value="PSP_001">Alpha Payment</option>
                                    <option value="PSP_002">Beta Finance</option>
                                    <option value="PSP_003">Gamma Pay</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">商户名称</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入商户名称">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">交易状态</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部状态</option>
                                    <option value="success">成功</option>
                                    <option value="pending">处理中</option>
                                    <option value="failed">失败</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出交易</button>
                            </div>
                        </div>
                    </div>

                    <!-- 换汇交易列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">换汇交易查询</h3>
                            <button class="btn btn-primary btn-sm">实时刷新</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户账户号码</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">商户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">卖出金额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">买入金额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">汇率</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">交易时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">更新时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">主账户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">主账户账户号码</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">EXC_20241220_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ACC_001_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC电商有限公司</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$5,000 USD</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€4,600 EUR</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">1.0870</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">成功</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 09:30</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 09:35</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC主账户</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MAIN_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">EXC_20241220_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Beta Finance</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ACC_002_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ贸易公司</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€3,000 EUR</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">¥23,500 CNY</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">7.8333</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">处理中</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 11:45</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 11:50</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ主账户</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MAIN_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">EXC_20241220_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Gamma Pay</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ACC_003_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">DEF科技服务</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">£2,000 GBP</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$2,540 USD</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">1.2700</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">成功</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 13:20</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 13:25</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">DEF主账户</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">MAIN_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">详情</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 4. 账单管理模块 -->
                <!-- 4.1 服务商账单 -->
                <div id="provider-bills" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">账单ID</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入账单ID">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">服务商名称</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部服务商</option>
                                    <option value="PSP_001">Alpha Payment</option>
                                    <option value="PSP_002">Beta Finance</option>
                                    <option value="PSP_003">Gamma Pay</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">账单周期</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部周期</option>
                                    <option value="2024-12">2024年12月</option>
                                    <option value="2024-11">2024年11月</option>
                                    <option value="2024-10">2024年10月</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出账单</button>
                            </div>
                        </div>
                    </div>

                    <!-- Tab导航 -->
                    <div class="tab-nav">
                        <button class="tab-btn active" onclick="switchTab('provider-pending')">待我核对</button>
                        <button class="tab-btn" onclick="switchTab('provider-settlement')">待服务商结算</button>
                        <button class="tab-btn" onclick="switchTab('provider-all')">全部</button>
                    </div>

                    <!-- 待我核对 Tab -->
                    <div id="provider-pending" class="tab-content active">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">待我核对账单</h3>
                                <button class="btn btn-primary btn-sm">批量核对</button>
                            </div>
                            <div class="card-body">
                                <div style="overflow-x: auto;">
                                    <table style="width: 100%; border-collapse: collapse;">
                                        <thead>
                                            <tr style="background: #f8f9fa;">
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单ID</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单周期</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单金额</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单状态</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单创建时间</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商编号</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商名称</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">BILL_PSP_202412_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024年12月</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$12,450 USD</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">待核对</span>
                                                </td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 10:00</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <button class="btn btn-primary btn-sm">核对</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">BILL_PSP_202412_002</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024年12月</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€8,750 EUR</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">待核对</span>
                                                </td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 11:30</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_002</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Beta Finance</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <button class="btn btn-primary btn-sm">核对</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 待服务商结算 Tab -->
                    <div id="provider-settlement" class="tab-content">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">待服务商结算账单</h3>
                                <button class="btn btn-primary btn-sm">提醒结算</button>
                            </div>
                            <div class="card-body">
                                <div style="overflow-x: auto;">
                                    <table style="width: 100%; border-collapse: collapse;">
                                        <thead>
                                            <tr style="background: #f8f9fa;">
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单ID</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单周期</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单金额</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单状态</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单创建时间</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商编号</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商名称</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">BILL_PSP_202411_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024年11月</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$15,680 USD</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d1ecf1; color: #0c5460;">待结算</span>
                                                </td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-11-30 18:00</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <button class="btn btn-primary btn-sm">查看</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 全部 Tab -->
                    <div id="provider-all" class="tab-content">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">全部服务商账单</h3>

                            </div>
                            <div class="card-body">
                                <div style="overflow-x: auto;">
                                    <table style="width: 100%; border-collapse: collapse;">
                                        <thead>
                                            <tr style="background: #f8f9fa;">
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单ID</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单周期</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单金额</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单状态</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单创建时间</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商编号</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商名称</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">BILL_PSP_202412_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024年12月</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$12,450 USD</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">待核对</span>
                                                </td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 10:00</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <button class="btn btn-primary btn-sm">查看</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">BILL_PSP_202411_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024年11月</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$15,680 USD</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">已结算</span>
                                                </td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-11-30 18:00</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <button class="btn btn-primary btn-sm">查看</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 4.2 租户对账单 -->
                <div id="tenant-bills" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">账单ID</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入账单ID">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户名称</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部租户</option>
                                    <option value="TENANT_001">ABC跨境电商</option>
                                    <option value="TENANT_002">XYZ国际贸易</option>
                                    <option value="TENANT_003">DEF科技服务</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">账单周期</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部周期</option>
                                    <option value="2024-12">2024年12月</option>
                                    <option value="2024-11">2024年11月</option>
                                    <option value="2024-10">2024年10月</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                            </div>
                        </div>
                    </div>

                    <!-- Tab导航 -->
                    <div class="tab-nav">
                        <button class="tab-btn active" onclick="switchTab('tenant-pending')">待我核对</button>
                        <button class="tab-btn" onclick="switchTab('tenant-settlement')">待我结算</button>
                        <button class="tab-btn" onclick="switchTab('tenant-all')">全部</button>
                    </div>

                    <!-- 待我核对 Tab -->
                    <div id="tenant-pending" class="tab-content active">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">待我核对账单</h3>
                                <button class="btn btn-primary btn-sm">批量核对</button>
                            </div>
                            <div class="card-body">
                                <div style="overflow-x: auto;">
                                    <table style="width: 100%; border-collapse: collapse;">
                                        <thead>
                                            <tr style="background: #f8f9fa;">
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单ID</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单周期</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单金额</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单状态</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单创建时间</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">租户ID</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">租户名称</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">BILL_TNT_202412_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024年12月</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$3,450 USD</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">待核对</span>
                                                </td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 12:00</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC跨境电商</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <button class="btn btn-primary btn-sm">核对</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">BILL_TNT_202412_002</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024年12月</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€2,750 EUR</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">待核对</span>
                                                </td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 13:30</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_002</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ国际贸易</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <button class="btn btn-primary btn-sm">核对</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 待我结算 Tab -->
                    <div id="tenant-settlement" class="tab-content">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">待我结算账单</h3>
                                <button class="btn btn-primary btn-sm">批量结算</button>
                            </div>
                            <div class="card-body">
                                <div style="overflow-x: auto;">
                                    <table style="width: 100%; border-collapse: collapse;">
                                        <thead>
                                            <tr style="background: #f8f9fa;">
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单ID</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单周期</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单金额</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单状态</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单创建时间</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">租户ID</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">租户名称</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">BILL_TNT_202411_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024年11月</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$4,680 USD</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d1ecf1; color: #0c5460;">待结算</span>
                                                </td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-11-30 20:00</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC跨境电商</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <button class="btn btn-primary btn-sm">结算</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 全部 Tab -->
                    <div id="tenant-all" class="tab-content">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">全部租户账单</h3>
                                <button class="btn btn-primary btn-sm">导出全部</button>
                            </div>
                            <div class="card-body">
                                <div style="overflow-x: auto;">
                                    <table style="width: 100%; border-collapse: collapse;">
                                        <thead>
                                            <tr style="background: #f8f9fa;">
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单ID</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单周期</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单金额</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单状态</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">账单创建时间</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">租户ID</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">租户名称</th>
                                                <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">BILL_TNT_202412_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024年12月</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$3,450 USD</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">待核对</span>
                                                </td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 12:00</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC跨境电商</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <button class="btn btn-primary btn-sm">查看</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">BILL_TNT_202411_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024年11月</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$4,680 USD</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">已结算</span>
                                                </td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-11-30 20:00</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_001</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC跨境电商</td>
                                                <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                    <button class="btn btn-primary btn-sm">查看</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 5. 结算管理模块 -->
                <!-- 5.1 服务商结算记录 -->
                <div id="provider-settlements" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">结算单ID</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入结算单ID">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">服务商名称</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部服务商</option>
                                    <option value="PSP_001">Alpha Payment</option>
                                    <option value="PSP_002">Beta Finance</option>
                                    <option value="PSP_003">Gamma Pay</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">结算状态</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部状态</option>
                                    <option value="pending">待结算</option>
                                    <option value="processing">结算中</option>
                                    <option value="completed">已完成</option>
                                    <option value="failed">失败</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 150px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">创建时间</label>
                                <input type="date" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" value="2024-12-20">
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出记录</button>
                            </div>
                        </div>
                    </div>

                    <!-- 服务商结算记录列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">服务商结算记录</h3>
                            <button class="btn btn-primary btn-sm">新建结算</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">结算单ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商编号</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">服务商名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">结算单金额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">结算手续费</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">实际结算金额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">结算单状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">创建时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">STL_PSP_202412_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Alpha Payment</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$15,680 USD</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$78.40</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$15,601.60</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">已完成</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-01 09:00</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">STL_PSP_202412_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Beta Finance</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€12,450 EUR</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€62.25</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€12,387.75</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">结算中</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-15 14:30</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">STL_PSP_202412_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">PSP_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">Gamma Pay</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$8,920 USD</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$44.60</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$8,875.40</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d1ecf1; color: #0c5460;">待结算</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 16:45</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">处理结算</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 5.2 租户结算记录 -->
                <div id="tenant-settlements" class="page-content">
                    <!-- 搜索筛选 -->
                    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">结算单ID</label>
                                <input type="text" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" placeholder="输入结算单ID">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户名称</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部租户</option>
                                    <option value="TENANT_001">ABC跨境电商</option>
                                    <option value="TENANT_002">XYZ国际贸易</option>
                                    <option value="TENANT_003">DEF科技服务</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">结算状态</label>
                                <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    <option value="">全部状态</option>
                                    <option value="pending">待结算</option>
                                    <option value="processing">结算中</option>
                                    <option value="completed">已完成</option>
                                    <option value="failed">失败</option>
                                </select>
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 150px;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">创建时间</label>
                                <input type="date" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" value="2024-12-20">
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div style="display: flex; flex-direction: column;">
                                <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">&nbsp;</label>
                                <button class="btn btn-primary">导出记录</button>
                            </div>
                        </div>
                    </div>

                    <!-- 租户结算记录列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">租户结算记录</h3>
                            <button class="btn btn-primary btn-sm">新建结算</button>
                        </div>
                        <div class="card-body">
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">结算单ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">租户ID</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">租户名称</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">结算单金额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">结算手续费</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">实际结算金额</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">结算单状态</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">创建时间</th>
                                            <th style="padding: 16px 20px; text-align: left; font-weight: 600; color: #333; border-bottom: 2px solid #dee2e6; font-size: 14px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">STL_TNT_202412_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_001</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">ABC跨境电商</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$4,680 USD</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$23.40</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$4,656.60</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d4edda; color: #155724;">已完成</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-01 10:00</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">STL_TNT_202412_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_002</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">XYZ国际贸易</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€3,250 EUR</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€16.25</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">€3,233.75</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #fff3cd; color: #856404;">结算中</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-15 15:30</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">STL_TNT_202412_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">TENANT_003</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">DEF科技服务</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$2,890 USD</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$14.45</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">$2,875.55</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <span style="padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; background: #d1ecf1; color: #0c5460;">待结算</span>
                                            </td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">2024-12-20 17:45</td>
                                            <td style="padding: 16px 20px; border-bottom: 1px solid #eee; font-size: 14px;">
                                                <button class="btn btn-primary btn-sm">处理结算</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 租户代理商基本信息页面 -->
                <div id="tenant-agent-info" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">租户代理商基本信息</h3>
                            <button class="btn btn-success btn-sm">导出</button>
                        </div>
                        <div class="card-body">
                            <!-- 搜索筛选 -->
                            <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                                <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap; margin-bottom: 15px;">
                                    <div style="display: flex; flex-direction: column; min-width: 150px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户ID：</label>
                                        <input type="text" placeholder="请输入租户ID" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户名称：</label>
                                        <input type="text" placeholder="请输入租户名称" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">代理商名称：</label>
                                        <input type="text" placeholder="请输入代理商名称" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 150px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">代理商号码：</label>
                                        <input type="text" placeholder="请输入代理商号码" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                </div>
                                <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap; margin-bottom: 15px;">
                                    <div style="display: flex; flex-direction: column; min-width: 120px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">状态：</label>
                                        <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <option value="">全部状态</option>
                                            <option value="pending">签约未生效</option>
                                            <option value="reviewing">签约审核中</option>
                                            <option value="active">签约生效中</option>
                                            <option value="expired">签约过期</option>
                                        </select>
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 120px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">激活状态：</label>
                                        <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <option value="">全部状态</option>
                                            <option value="activated">已激活</option>
                                            <option value="not-activated">未激活</option>
                                        </select>
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 150px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">创建时间：</label>
                                        <div style="display: flex; gap: 10px; align-items: center;">
                                            <input type="date" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <span style="color: #666;">至</span>
                                            <input type="date" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                        </div>
                                    </div>
                                </div>
                                <div style="display: flex; gap: 10px;">
                                    <button class="btn btn-primary" style="background: #007bff; border: none; padding: 8px 20px; border-radius: 4px; color: white; font-size: 14px;">查询</button>
                                    <button class="btn btn-secondary" style="background: #6c757d; border: none; padding: 8px 20px; border-radius: 4px; color: white; font-size: 14px;">重置</button>
                                </div>
                            </div>

                            <!-- 表格 -->
                            <div class="table-container">
                                <table class="data-table" style="table-layout: fixed; width: 100%;">
                                    <colgroup>
                                        <col style="width: 120px;"> <!-- 租户信息 -->
                                        <col style="width: 140px;"> <!-- 代理商信息 -->
                                        <col style="width: 160px;"> <!-- 管理员信息 -->
                                        <col style="width: 180px;"> <!-- 签约信息 -->
                                        <col style="width: 90px;">  <!-- 激活状态 -->
                                        <col style="width: 140px;"> <!-- 结算信息 -->
                                        <col style="width: 100px;"> <!-- 所属销售 -->
                                        <col style="width: 140px;"> <!-- 时间 -->
                                        <col style="width: 80px;">  <!-- 操作 -->
                                    </colgroup>
                                    <thead>
                                        <tr>
                                            <th>租户信息</th>
                                            <th>代理商信息</th>
                                            <th>管理员信息</th>
                                            <th>签约信息</th>
                                            <th style="text-align: center;">激活状态
                                                <span style="cursor: help; color: #007bff; margin-left: 3px;" title="代理商操作后台的激活状态">❓</span>
                                            </th>
                                            <th>结算信息</th>
                                            <th>所属销售</th>
                                            <th>时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">
                                                <div style="font-weight: 600; margin-bottom: 3px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">深圳科技</div>
                                                <div style="color: #666; font-size: 11px; margin-bottom: 3px;">T202501001</div>
                                                <div style="color: #666; font-size: 11px;">SZ20250001</div>
                                            </td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">
                                                <div style="font-weight: 600; margin-bottom: 3px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">显示简称</div>
                                                <div style="color: #666; font-size: 11px; margin-bottom: 3px;">AG202501001</div>
                                                <span style="padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 3px; font-size: 10px;">A级</span>
                                            </td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">
                                                <div style="font-weight: 500; margin-bottom: 3px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">张经理</div>
                                                <div style="color: #666; font-size: 11px; margin-bottom: 2px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"><EMAIL></div>
                                                <div style="color: #666; font-size: 11px;">+86 13888886666</div>
                                            </td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">
                                                <div style="font-size: 12px; margin-bottom: 4px; white-space: nowrap;">2025-01-01 ~ 2025-12-31</div>
                                                <span style="padding: 3px 8px; border-radius: 10px; font-size: 11px; font-weight: 500; background: #d4edda; color: #155724; white-space: nowrap;">签约生效中</span>
                                            </td>
                                            <td style="text-align: center;">
                                                <span style="padding: 3px 8px; border-radius: 3px; font-size: 11px; font-weight: 500; background: #3498db; color: white; white-space: nowrap;">已激活</span>
                                            </td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">
                                                <div style="font-weight: 500; margin-bottom: 3px;">每月20日</div>
                                                <div style="color: #666; font-size: 11px; margin-bottom: 2px;">USD</div>
                                                <div style="color: #666; font-size: 11px;">****1234</div>
                                            </td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">
                                                <div style="font-weight: 500; margin-bottom: 4px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">李销售</div>
                                                <span style="padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 3px; font-size: 10px;">开发票</span>
                                            </td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">
                                                <div style="font-size: 11px; margin-bottom: 3px; white-space: nowrap;">创建: 2025-01-01 10:30</div>
                                                <div style="color: #666; font-size: 11px; white-space: nowrap;">更新: 2025-01-10 15:20</div>
                                            </td>
                                            <td style="white-space: nowrap;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px;" onclick="showPage('tenant-agent-detail')">详情</a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">
                                                <div style="font-weight: 600; margin-bottom: 3px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">广州贸易</div>
                                                <div style="color: #666; font-size: 11px; margin-bottom: 3px;">T202501002</div>
                                                <div style="color: #666; font-size: 11px;">GZ20250002</div>
                                            </td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">
                                                <div style="font-weight: 600; margin-bottom: 3px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">广州贸易公司</div>
                                                <div style="color: #666; font-size: 11px; margin-bottom: 3px;">AG202501002</div>
                                                <span style="padding: 2px 6px; background: #f3e5f5; color: #7b1fa2; border-radius: 3px; font-size: 10px;">B级</span>
                                            </td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">
                                                <div style="font-weight: 500; margin-bottom: 3px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">王总</div>
                                                <div style="color: #666; font-size: 11px; margin-bottom: 2px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"><EMAIL></div>
                                                <div style="color: #666; font-size: 11px;">+86 13888888888</div>
                                            </td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">
                                                <div style="font-size: 12px; margin-bottom: 4px; white-space: nowrap;">2024-12-01 ~ 2025-11-30</div>
                                                <span style="padding: 3px 8px; border-radius: 10px; font-size: 11px; font-weight: 500; background: #d4edda; color: #155724; white-space: nowrap;">签约生效中</span>
                                            </td>
                                            <td style="text-align: center;">
                                                <span style="padding: 3px 8px; border-radius: 3px; font-size: 11px; font-weight: 500; background: #f39c12; color: white; white-space: nowrap;">未激活</span>
                                            </td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">
                                                <div style="font-weight: 500; margin-bottom: 3px;">每月20日</div>
                                                <div style="color: #666; font-size: 11px; margin-bottom: 2px;">CNY</div>
                                                <div style="color: #666; font-size: 11px;">****5678</div>
                                            </td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">
                                                <div style="font-weight: 500; margin-bottom: 4px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">张销售</div>
                                                <span style="padding: 2px 6px; background: #f3e5f5; color: #7b1fa2; border-radius: 3px; font-size: 10px;">无发票</span>
                                            </td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">
                                                <div style="font-size: 11px; margin-bottom: 3px; white-space: nowrap;">创建: 2024-12-15 14:20</div>
                                                <div style="color: #666; font-size: 11px; white-space: nowrap;">更新: 2025-01-08 09:15</div>
                                            </td>
                                            <td style="white-space: nowrap;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px;" onclick="showPage('tenant-agent-detail')">详情</a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px;">
                                <div style="color: #666; font-size: 14px;">
                                    共 2 条记录，第 1/1 页
                                </div>
                                <div style="display: flex; gap: 5px;">
                                    <button style="padding: 8px 12px; border: 1px solid #ddd; background: white; color: #999; border-radius: 4px; cursor: not-allowed;" disabled>上一页</button>
                                    <button style="padding: 8px 12px; border: 1px solid #007bff; background: #007bff; color: white; border-radius: 4px;">1</button>
                                    <button style="padding: 8px 12px; border: 1px solid #ddd; background: white; color: #999; border-radius: 4px; cursor: not-allowed;" disabled>下一页</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标准代理底价页面 -->
                <div id="tenant-agent-standard-rates" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">标准产品底价</h3>
                        </div>
                        <div class="card-body">

                            <!-- 搜索筛选 -->
                            <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                                <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap; margin-bottom: 15px;">
                                    <div style="display: flex; flex-direction: column; min-width: 150px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户ID：</label>
                                        <input type="text" placeholder="请输入租户ID" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户名称：</label>
                                        <input type="text" placeholder="请输入租户名称" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 150px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">代理商等级：</label>
                                        <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <option value="">全部等级</option>
                                            <option value="A">A级</option>
                                            <option value="B">B级</option>
                                            <option value="C">C级</option>
                                        </select>
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">产品：</label>
                                        <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <option value="">全部产品</option>
                                            <option value="credit_card">电商收款</option>
                                            <option value="bank_transfer">外贸收款</option>
                                            <option value="digital_wallet">物流收款</option>
                                        </select>
                                    </div>
                                    <button style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">查询</button>
                                    <button style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">重置</button>
                                </div>
                            </div>

                            <!-- 表格 -->
                            <div class="table-container">
                                <table class="data-table" style="table-layout: fixed; width: 100%;">
                                    <colgroup>
                                        <col style="width: 80px;">  <!-- 租户ID -->
                                        <col style="width: 120px;"> <!-- 租户名称 -->
                                        <col style="width: 100px;"> <!-- 代理商等级 -->
                                        <col style="width: 120px;"> <!-- 产品名称 -->
                                        <col style="width: 300px;"> <!-- 收费 -->
                                        <col style="width: 120px;"> <!-- 更新时间 -->
                                        <col style="width: 120px;"> <!-- 操作 -->
                                    </colgroup>
                                    <thead>
                                        <tr>
                                            <th>租户ID</th>
                                            <th>租户名称</th>
                                            <th>代理商等级</th>
                                            <th>产品名称</th>
                                            <th>收费</th>
                                            <th>更新时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T001</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Alpha科技</td>
                                            <td><span style="padding: 4px 8px; background: #28a745; color: white; border-radius: 4px; font-size: 12px; white-space: nowrap;">A级</span></td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">电商收款</td>
                                            <td style="max-width: 300px;">
                                                <div style="font-size: 13px; line-height: 1.6;">
                                                    <div style="margin-bottom: 2px;">全球收款→HKD→DBS: 0.20%</div>
                                                    <div style="margin-bottom: 2px;">本地收款→HKD→其他: 0.45%</div>
                                                    <div style="color: #6c757d; font-size: 12px;">默认最高2笔，微信...</div>
                                                </div>
                                            </td>
                                            <td style="white-space: nowrap;">
                                                <div>2025-03-15</div>
                                                <div style="color: #6c757d; font-size: 12px;">10:30</div>
                                            </td>
                                            <td style="white-space: nowrap;">
                                                <a href="#" style="color: #007bff; text-decoration: none; margin-right: 8px; font-size: 13px;">详情</a>

                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T001</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Alpha科技</td>
                                            <td><span style="padding: 4px 8px; background: #ffc107; color: #212529; border-radius: 4px; font-size: 12px; white-space: nowrap;">B级</span></td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">外贸收款</td>
                                            <td style="max-width: 300px;">
                                                <div style="font-size: 13px; line-height: 1.6;">
                                                    <div style="margin-bottom: 2px;">全球收款→HKD→DBS: 0.25%</div>
                                                    <div style="margin-bottom: 2px;">本地收款→HKD→其他: 0.5%</div>
                                                    <div style="color: #6c757d; font-size: 12px;">默认最高2笔，微信...</div>
                                                </div>
                                            </td>
                                            <td style="white-space: nowrap;">
                                                <div>2025-03-14</div>
                                                <div style="color: #6c757d; font-size: 12px;">09:20</div>
                                            </td>
                                            <td style="white-space: nowrap;">
                                                <a href="#" style="color: #007bff; text-decoration: none; margin-right: 8px; font-size: 13px;">详情</a>

                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T002</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Beta支付</td>
                                            <td><span style="padding: 4px 8px; background: #dc3545; color: white; border-radius: 4px; font-size: 12px; white-space: nowrap;">C级</span></td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">物流收款</td>
                                            <td style="max-width: 300px;">
                                                <div style="font-size: 13px; line-height: 1.6;">
                                                    <div style="margin-bottom: 2px;">全球收款→HKD→DBS: 0.30%</div>
                                                    <div style="margin-bottom: 2px;">本地收款→HKD→其他: 0.55%</div>
                                                    <div style="color: #6c757d; font-size: 12px;">默认最高2笔，微信...</div>
                                                </div>
                                            </td>
                                            <td style="white-space: nowrap;">
                                                <div>2025-03-14</div>
                                                <div style="color: #6c757d; font-size: 12px;">09:20</div>
                                            </td>
                                            <td style="white-space: nowrap;">
                                                <a href="#" style="color: #007bff; text-decoration: none; margin-right: 8px; font-size: 13px;">详情</a>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 代理商特殊底价页面 -->
                <div id="tenant-agent-special-rates" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">代理商特殊产品底价</h3>
                            <div style="display: flex; gap: 10px;">

                            </div>
                        </div>
                    
                            <!-- 搜索筛选 -->
                            <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                                <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap; margin-bottom: 15px;">
                                    <div style="display: flex; flex-direction: column; min-width: 150px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户ID：</label>
                                        <input type="text" placeholder="请输入租户ID" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户名称：</label>
                                        <input type="text" placeholder="请输入租户名称" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 150px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">代理商等级：</label>
                                        <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <option value="">全部等级</option>
                                            <option value="A">A级</option>
                                            <option value="B">B级</option>
                                            <option value="C">C级</option>
                                        </select>
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">代理商名称：</label>
                                        <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <option value="">全部代理商</option>
                                            <option value="AGT001">上海润泽科技有限公司-AGT001</option>
                                            <option value="AGT002">北京智汇通科技有限公司-AGT002</option>
                                            <option value="AGT003">广州环球贸易有限公司-AGT003</option>
                                        </select>
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">产品：</label>
                                        <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <option value="">全部产品</option>
                                            <option value="credit_card">电商收款</option>
                                            <option value="bank_transfer">外贸收款</option>
                                            <option value="digital_wallet">物流收款</option>
                                        </select>
                                    </div>
                                    <button style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">查询</button>
                                    <button style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">重置</button>
                                </div>
                            </div>

                            <!-- 表格 -->
                            <div class="table-container">
                                <table class="data-table" style="table-layout: fixed; width: 100%;">
                                    <colgroup>
                                        <col style="width: 80px;">  <!-- 租户ID -->
                                        <col style="width: 120px;"> <!-- 租户名称 -->
                                        <col style="width: 150px;"> <!-- 代理商名称 -->
                                        <col style="width: 100px;"> <!-- 代理商ID -->
                                        <col style="width: 120px;"> <!-- 产品名称 -->
                                        <col style="width: 300px;"> <!-- 收费 -->
                                        <col style="width: 120px;"> <!-- 更新时间 -->
                                        <col style="width: 80px;">  <!-- 操作 -->
                                    </colgroup>
                                    <thead>
                                        <tr>
                                            <th>租户ID</th>
                                            <th>租户名称</th>
                                            <th>代理商名称</th>
                                            <th>代理商ID</th>
                                            <th>产品名称</th>
                                            <th>收费</th>
                                            <th>更新时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T001</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Alpha科技</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;" title="上海润泽科技有限公司">上海润泽科技有限公司</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">AGT001</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">电商收款</td>
                                            <td style="max-width: 300px;">
                                                <div style="font-size: 13px; line-height: 1.6;">
                                                    <div style="margin-bottom: 2px;">全球收款→HKD→DBS: 0.18%</div>
                                                    <div style="margin-bottom: 2px;">本地收款→HKD→其他: 0.42%</div>
                                                    <div style="color: #6c757d; font-size: 12px;">特殊优惠费率</div>
                                                </div>
                                            </td>
                                            <td style="white-space: nowrap;">
                                                <div>2025-03-15</div>
                                                <div style="color: #6c757d; font-size: 12px;">10:30</div>
                                            </td>
                                            <td style="white-space: nowrap;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 13px;">详情</a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T001</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Alpha科技</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;" title="北京智汇通科技有限公司">北京智汇通科技有限公司</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">AGT002</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">外贸收款</td>
                                            <td style="max-width: 300px;">
                                                <div style="font-size: 13px; line-height: 1.6;">
                                                    <div style="margin-bottom: 2px;">全球收款→HKD→DBS: 0.22%</div>
                                                    <div style="margin-bottom: 2px;">本地收款→HKD→其他: 0.47%</div>
                                                    <div style="color: #6c757d; font-size: 12px;">特殊协议费率</div>
                                                </div>
                                            </td>
                                            <td style="white-space: nowrap;">
                                                <div>2025-03-14</div>
                                                <div style="color: #6c757d; font-size: 12px;">09:20</div>
                                            </td>
                                            <td style="white-space: nowrap;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 13px;">详情</a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T002</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Beta支付</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;" title="广州环球贸易有限公司">广州环球贸易有限公司</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">AGT003</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">物流收款</td>
                                            <td style="max-width: 300px;">
                                                <div style="font-size: 13px; line-height: 1.6;">
                                                    <div style="margin-bottom: 2px;">全球收款→HKD→DBS: 0.28%</div>
                                                    <div style="margin-bottom: 2px;">本地收款→HKD→其他: 0.52%</div>
                                                    <div style="color: #6c757d; font-size: 12px;">批量优惠费率</div>
                                                </div>
                                            </td>
                                            <td style="white-space: nowrap;">
                                                <div>2025-03-12</div>
                                                <div style="color: #6c757d; font-size: 12px;">15:45</div>
                                            </td>
                                            <td style="white-space: nowrap;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 13px;">详情</a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 代理商账单记录页面 -->
                <div id="tenant-agent-bill-records" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">代理商账单记录</h3>
                            <div style="display: flex; gap: 10px;">

                            </div>
                        </div>
                        <div class="card-body">

                            <!-- 搜索筛选 -->
                            <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #dee2e6;">
                                <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap; margin-bottom: 15px;">
                                    <div style="display: flex; flex-direction: column; min-width: 150px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户ID：</label>
                                        <input type="text" placeholder="请输入租户ID" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户名称：</label>
                                        <input type="text" placeholder="请输入租户名称" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">代理商名称：</label>
                                        <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <option value="">全部代理商</option>
                                            <option value="agent1">优质代理商A</option>
                                            <option value="agent2">专业代理商B</option>
                                            <option value="agent3">精英代理商C</option>
                                        </select>
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">账单周期：</label>
                                        <input type="month" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" value="2024-01">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 150px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">账单状态：</label>
                                        <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <option value="">全部状态</option>
                                            <option value="pending">待确认</option>
                                            <option value="confirmed">已确认</option>
                                            <option value="settled">已结算</option>
                                        </select>
                                    </div>
                                    <button style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">查询</button>
                                    <button style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">重置</button>
                                </div>
                            </div>

                            <!-- 表格 -->
                            <div class="table-container">
                                <table class="data-table" style="table-layout: fixed; width: 100%;">
                                    <colgroup>
                                        <col style="width: 80px;">  <!-- 租户ID -->
                                        <col style="width: 120px;"> <!-- 租户名称 -->
                                        <col style="width: 140px;"> <!-- 账单编号 -->
                                        <col style="width: 120px;"> <!-- 代理商名称 -->
                                        <col style="width: 100px;"> <!-- 分佣周期 -->
                                        <col style="width: 120px;"> <!-- 账单金额 -->
                                        <col style="width: 120px;"> <!-- 已结算金额 -->
                                        <col style="width: 80px;">  <!-- 账单状态 -->
                                        <col style="width: 120px;"> <!-- 创建时间 -->
                                        <col style="width: 100px;"> <!-- 操作 -->
                                    </colgroup>
                                    <thead>
                                        <tr>
                                            <th>租户ID</th>
                                            <th>租户名称</th>
                                            <th>账单编号</th>
                                            <th>代理商名称</th>
                                            <th>分佣周期</th>
                                            <th>账单金额</th>
                                            <th>已结算金额</th>
                                            <th>账单状态</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T001</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Alpha科技</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">BILL202401001</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">优质代理商A</td>
                                            <td style="white-space: nowrap;">2024-01</td>
                                            <td style="color: #28a745; font-weight: 600; white-space: nowrap;">¥ 25,680.50</td>
                                            <td style="color: #28a745; font-weight: 600; white-space: nowrap;">¥ 25,680.50</td>
                                            <td><span style="padding: 2px 8px; background: #d4edda; color: #155724; border-radius: 8px; font-size: 11px; white-space: nowrap;">已确认</span></td>
                                            <td style="white-space: nowrap; font-size: 13px;">02-01 10:30</td>
                                            <td style="white-space: nowrap;">
                                                <a href="#" style="color: #007bff; text-decoration: none; margin-right: 8px; font-size: 13px;">详情</a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T001</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Alpha科技</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">BILL202401003</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">优质代理商A</td>
                                            <td style="white-space: nowrap;">2024-01</td>
                                            <td style="color: #28a745; font-weight: 600; white-space: nowrap;">¥ 32,150.80</td>
                                            <td style="white-space: nowrap;">¥ 0.00</td>
                                            <td><span style="padding: 2px 8px; background: #fff3cd; color: #856404; border-radius: 8px; font-size: 11px; white-space: nowrap;">待确认</span></td>
                                            <td style="white-space: nowrap; font-size: 13px;">01-31 16:45</td>
                                            <td style="white-space: nowrap;">
                                                <a href="#" style="color: #007bff; text-decoration: none; margin-right: 8px; font-size: 13px;">详情</a>

                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T002</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Beta支付</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">BILL202401002</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">专业代理商B</td>
                                            <td style="white-space: nowrap;">2024-01</td>
                                            <td style="color: #28a745; font-weight: 600; white-space: nowrap;">¥ 18,750.30</td>
                                            <td style="color: #28a745; font-weight: 600; white-space: nowrap;">¥ 18,750.30</td>
                                            <td><span style="padding: 2px 8px; background: #d4edda; color: #155724; border-radius: 8px; font-size: 11px; white-space: nowrap;">已确认</span></td>
                                            <td style="white-space: nowrap; font-size: 13px;">01-30 14:20</td>
                                            <td style="white-space: nowrap;">
                                                <a href="#" style="color: #007bff; text-decoration: none; margin-right: 8px; font-size: 13px;">详情</a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <div class="pagination-container">
                                <div class="pagination-info">
                                    共 <span style="font-weight: 600; color: #007bff;">3</span> 条记录，第 <span style="font-weight: 600; color: #007bff;">1</span> 页，共 <span style="font-weight: 600; color: #007bff;">1</span> 页
                                </div>
                                <div class="pagination-controls">
                                    <button class="btn btn-sm btn-outline-secondary" disabled>上一页</button>
                                    <button class="btn btn-sm btn-primary">1</button>
                                    <button class="btn btn-sm btn-outline-secondary" disabled>下一页</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 代理商账单明细页面 -->
                <div id="tenant-agent-bill-details" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">代理商账单明细</h3>
                        </div>
                        <div class="card-body">

                            <!-- 搜索筛选 -->
                            <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #dee2e6;">
                                <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap; margin-bottom: 15px;">
                                    <div style="display: flex; flex-direction: column; min-width: 150px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户ID：</label>
                                        <input type="text" placeholder="请输入租户ID" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户名称：</label>
                                        <input type="text" placeholder="请输入租户名称" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">代理商名称：</label>
                                        <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <option value="">全部代理商</option>
                                            <option value="agent1">优质代理商A</option>
                                            <option value="agent2">专业代理商B</option>
                                        </select>
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">商户名称：</label>
                                        <input type="text" placeholder="请输入商户名称" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 150px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">分佣类型：</label>
                                        <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <option value="">全部类型</option>
                                            <option value="transaction">交易分佣</option>
                                            <option value="exchange">汇率分佣</option>
                                        </select>
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">分佣时间：</label>
                                        <div style="display: flex; gap: 10px; align-items: center;">
                                            <input type="date" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" value="2024-01-01">
                                            <span style="color: #6c757d;">至</span>
                                            <input type="date" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" value="2024-01-31">
                                        </div>
                                    </div>
                                    <button style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">查询</button>
                                    <button style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">重置</button>
                                </div>
                            </div>

                            <!-- 表格 -->
                            <div class="table-container">
                                <table class="data-table" style="table-layout: fixed; width: 100%;">
                                    <colgroup>
                                        <col style="width: 70px;">  <!-- 租户ID -->
                                        <col style="width: 100px;"> <!-- 租户名称 -->
                                        <col style="width: 130px;"> <!-- 账单编号 -->
                                        <col style="width: 110px;"> <!-- 代理商名称 -->
                                        <col style="width: 120px;"> <!-- 商户名称 -->
                                        <col style="width: 80px;">  <!-- 产品 -->
                                        <col style="width: 80px;">  <!-- 费率 -->
                                        <col style="width: 100px;"> <!-- 交易金额 -->
                                        <col style="width: 80px;">  <!-- 分佣费率 -->
                                        <col style="width: 80px;">  <!-- 分佣金额 -->
                                        <col style="width: 100px;"> <!-- 交易时间 -->
                                        <col style="width: 60px;">  <!-- 状态 -->
                                    </colgroup>
                                    <thead>
                                        <tr>
                                            <th>租户ID</th>
                                            <th>租户名称</th>
                                            <th>账单编号</th>
                                            <th>代理商名称</th>
                                            <th>商户名称</th>
                                            <th>产品</th>
                                            <th>费率</th>
                                            <th>交易金额</th>
                                            <th>分佣费率</th>
                                            <th>分佣金额</th>
                                            <th>交易时间</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T001</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Alpha科技</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">BILL202401001</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">优质代理商A</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;" title="优质代理商A">优质代理商A</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">跨境收款</td>
                                            <td style="white-space: nowrap;">2024-01</td>
                                            <td style="color: #28a745; font-weight: 600; white-space: nowrap;">¥ 10,000.00</td>
                                            <td style="white-space: nowrap;">0.35%</td>
                                            <td style="color: #28a745; font-weight: 600; white-space: nowrap;">¥ 35.00</td>
                                            <td style="white-space: nowrap; font-size: 13px;">01-15 14:30</td>
                                            <td><span style="padding: 2px 6px; background: #d4edda; color: #155724; border-radius: 6px; font-size: 11px;">正常</span></td>
                                        </tr>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T001</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Alpha科技</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">BILL202401003</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">优质代理商A</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;" title="优质代理商A">优质代理商A</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">汇率差价</td>
                                            <td style="white-space: nowrap;">2024-01</td>
                                            <td style="color: #28a745; font-weight: 600; white-space: nowrap;">¥ 50,000.00</td>
                                            <td style="white-space: nowrap;">0.15%</td>
                                            <td style="color: #28a745; font-weight: 600; white-space: nowrap;">¥ 75.00</td>
                                            <td style="white-space: nowrap; font-size: 13px;">01-16 09:20</td>
                                            <td><span style="padding: 2px 6px; background: #d4edda; color: #155724; border-radius: 6px; font-size: 11px;">正常</span></td>
                                        </tr>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T002</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Beta支付</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">BILL202401002</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">专业代理商B</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;" title="专业代理商B">专业代理商B</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">本地收款</td>
                                            <td style="white-space: nowrap;">2024-01</td>
                                            <td style="color: #28a745; font-weight: 600; white-space: nowrap;">¥ 25,000.00</td>
                                            <td style="white-space: nowrap;">0.25%</td>
                                            <td style="color: #28a745; font-weight: 600; white-space: nowrap;">¥ 62.50</td>
                                            <td style="white-space: nowrap; font-size: 13px;">01-20 16:45</td>
                                            <td><span style="padding: 2px 6px; background: #d4edda; color: #155724; border-radius: 6px; font-size: 11px;">正常</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <div class="pagination-container">
                                <div class="pagination-info">
                                    共 <span style="font-weight: 600; color: #007bff;">3</span> 条记录，第 <span style="font-weight: 600; color: #007bff;">1</span> 页，共 <span style="font-weight: 600; color: #007bff;">1</span> 页
                                </div>
                                <div class="pagination-controls">
                                    <button class="btn btn-sm btn-outline-secondary" disabled>上一页</button>
                                    <button class="btn btn-sm btn-primary">1</button>
                                    <button class="btn btn-sm btn-outline-secondary" disabled>下一页</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 代理商结算记录页面 -->
                <div id="tenant-agent-settlement-records" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">代理商结算记录</h3>
                            <div style="display: flex; gap: 10px;">

                            </div>
                        </div>
                        <div class="card-body">

                            <!-- 搜索筛选 -->
                            <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #dee2e6;">
                                <div style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap; margin-bottom: 15px;">
                                    <div style="display: flex; flex-direction: column; min-width: 150px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户ID：</label>
                                        <input type="text" placeholder="请输入租户ID" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">租户名称：</label>
                                        <input type="text" placeholder="请输入租户名称" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">代理商名称：</label>
                                        <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <option value="">全部代理商</option>
                                            <option value="agent1">优质代理商A</option>
                                            <option value="agent2">专业代理商B</option>
                                            <option value="agent3">精英代理商C</option>
                                        </select>
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 150px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">结算方式：</label>
                                        <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <option value="">全部方式</option>
                                            <option value="bank">银行转账</option>
                                            <option value="alipay">支付宝</option>
                                            <option value="wechat">微信支付</option>
                                        </select>
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 200px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">结算周期：</label>
                                        <input type="month" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;" value="2024-01">
                                    </div>
                                    <div style="display: flex; flex-direction: column; min-width: 150px;">
                                        <label style="font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px;">结算状态：</label>
                                        <select style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                                            <option value="">全部状态</option>
                                            <option value="pending">待审批</option>
                                            <option value="approved">已审批</option>
                                            <option value="completed">已完成</option>
                                            <option value="failed">失败</option>
                                        </select>
                                    </div>
                                    <button style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">查询</button>
                                    <button style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">重置</button>
                                </div>
                            </div>

                            <!-- 表格 -->
                            <div class="table-container">
                                <table class="data-table" style="table-layout: fixed; width: 100%;">
                                    <colgroup>
                                        <col style="width: 70px;">  <!-- 租户ID -->
                                        <col style="width: 100px;"> <!-- 租户名称 -->
                                        <col style="width: 100px;"> <!-- 结算编号 -->
                                        <col style="width: 120px;"> <!-- 代理商名称 -->
                                        <col style="width: 90px;">  <!-- 结算方式 -->
                                        <col style="width: 100px;"> <!-- 结算周期 -->
                                        <col style="width: 150px;"> <!-- 结算时间 -->
                                        <col style="width: 120px;"> <!-- 结算金额 -->
                                        <col style="width: 80px;">  <!-- 状态 -->
                                        <col style="width: 100px;"> <!-- 操作 -->
                                    </colgroup>
                                    <thead>
                                        <tr>
                                            <th>租户ID</th>
                                            <th>租户名称</th>
                                            <th>结算编号</th>
                                            <th>代理商名称</th>
                                            <th>结算方式</th>
                                            <th>结算周期</th>
                                            <th>结算时间</th>
                                            <th>结算金额</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T001</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Alpha科技</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">STL001</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">优质代理商A</td>
                                            <td style="white-space: nowrap;">银行转账</td>
                                            <td style="white-space: nowrap;">2024年01月</td>
                                            <td style="white-space: nowrap; font-size: 13px;">2024-02-05 16:30:25</td>
                                            <td style="color: #28a745; font-weight: 600; white-space: nowrap;">¥ 25,680.50</td>
                                            <td><span style="padding: 2px 8px; background: #d4edda; color: #155724; border-radius: 8px; font-size: 11px; white-space: nowrap;">已完成</span></td>
                                            <td style="white-space: nowrap;">
                                                <a href="#" style="color: #007bff; text-decoration: none; margin-right: 8px; font-size: 13px;">详情</a>
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 13px;">凭证</a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T001</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Alpha科技</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">STL002</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">专业代理商B</td>
                                            <td style="white-space: nowrap;">银行转账</td>
                                            <td style="white-space: nowrap;">2024年01月</td>
                                            <td style="white-space: nowrap; font-size: 13px;">2024-02-05 14:30:12</td>
                                            <td style="color: #28a745; font-weight: 600; white-space: nowrap;">¥ 32,150.80</td>
                                            <td><span style="padding: 2px 8px; background: #fff3cd; color: #856404; border-radius: 8px; font-size: 11px; white-space: nowrap;">待审批</span></td>
                                            <td style="white-space: nowrap;">
                                                <a href="#" style="color: #007bff; text-decoration: none; margin-right: 8px; font-size: 13px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 13px;">审批</a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">T002</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">Beta支付</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">STL003</td>
                                            <td style="overflow: hidden; text-overflow: ellipsis;">精英代理商C</td>
                                            <td style="white-space: nowrap;">支付宝</td>
                                            <td style="white-space: nowrap;">2024年01月</td>
                                            <td style="white-space: nowrap; font-size: 13px;">2024-02-08 09:36:42</td>
                                            <td style="color: #28a745; font-weight: 600; white-space: nowrap;">¥ 18,750.30</td>
                                            <td><span style="padding: 2px 8px; background: #d1ecf1; color: #0c5460; border-radius: 8px; font-size: 11px; white-space: nowrap;">已审批</span></td>
                                            <td style="white-space: nowrap;">
                                                <a href="#" style="color: #007bff; text-decoration: none; margin-right: 8px; font-size: 13px;">详情</a>
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 13px;">凭证</a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <div class="pagination-container">
                                <div class="pagination-info">
                                    共 <span style="font-weight: 600; color: #007bff;">3</span> 条记录，第 <span style="font-weight: 600; color: #007bff;">1</span> 页，共 <span style="font-weight: 600; color: #007bff;">1</span> 页
                                </div>
                                <div class="pagination-controls">
                                    <button class="btn btn-sm btn-outline-secondary" disabled>上一页</button>
                                    <button class="btn btn-sm btn-primary">1</button>
                                    <button class="btn btn-sm btn-outline-secondary" disabled>下一页</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 租户代理商详情页面 -->
                <div id="tenant-agent-detail" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">代理商详情</h3>
                            <div style="display: flex; gap: 10px;">
                                <button class="btn btn-secondary btn-sm" onclick="showPage('tenant-agent-info')">返回</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 代理商详情表单 -->
                            <div style="background: white; padding: 30px; border-radius: 8px;">
                                <!-- 租户信息 -->
                                <div style="margin-bottom: 40px;">
                                    <h4 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600; color: #333; padding-bottom: 10px; border-bottom: 2px solid #28a745;">租户信息</h4>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                租户名称
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                深圳科技有限公司
                                            </div>
                                        </div>
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                租户ID
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                T202501001
                                            </div>
                                        </div>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr; gap: 30px; margin-bottom: 30px;">
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                租户账号
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                SZ20250001
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 代理商基本信息 -->
                                <div style="margin-bottom: 40px;">
                                    <h4 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600; color: #333; padding-bottom: 10px; border-bottom: 2px solid #007bff;">代理商基本信息</h4>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                代理商全称
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                上海易也网络科技有限公司
                                            </div>
                                        </div>
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                代理商简称
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                显示简称
                                            </div>
                                        </div>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                代理商等级
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                A级 - 高等级
                                            </div>
                                        </div>
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                代理商号码
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                AG202501001
                                            </div>
                                        </div>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                合同开始时间
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                2025-01-01
                                            </div>
                                        </div>
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                合同结束时间
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                2025-12-31
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 管理员信息 -->
                                <div style="margin-bottom: 40px;">
                                    <h4 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600; color: #333; padding-bottom: 10px; border-bottom: 2px solid #dc3545;">管理员信息</h4>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                管理员姓名
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                张经理
                                            </div>
                                        </div>
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                管理员手机号
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                +86 13888886666
                                            </div>
                                        </div>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr; gap: 30px; margin-bottom: 30px;">
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                管理员邮箱
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                <EMAIL>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 结算信息 -->
                                <div style="margin-bottom: 40px;">
                                    <h4 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600; color: #333; padding-bottom: 10px; border-bottom: 2px solid #ffc107;">结算信息</h4>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                结算日期
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                每月20日
                                            </div>
                                        </div>
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                结算币种
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                USD - 美元
                                            </div>
                                        </div>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                银行名称
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                招商银行
                                            </div>
                                        </div>
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                账户名称
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                上海易也网络科技有限公司
                                            </div>
                                        </div>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                账户号码
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                6225881234567890
                                            </div>
                                        </div>
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                SWIFT Code
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                CMBCCNBS
                                            </div>
                                        </div>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr; gap: 30px; margin-bottom: 30px;">
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                其他路由Code
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                -
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 其他信息 -->
                                <div style="margin-bottom: 40px;">
                                    <h4 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600; color: #333; padding-bottom: 10px; border-bottom: 2px solid #6f42c1;">其他信息</h4>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                发票能力
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                可以开发票
                                            </div>
                                        </div>
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                所属销售
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                李销售
                                            </div>
                                        </div>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                激活状态
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; background: #3498db; color: white;">已激活</span>
                                            </div>
                                        </div>
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                签约状态
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                <span style="padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600; background: #d4edda; color: #155724;">签约生效中</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                创建时间
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                2025-01-01 10:30:00
                                            </div>
                                        </div>
                                        <div>
                                            <label style="display: block; font-size: 14px; font-weight: 500; color: #333; margin-bottom: 8px;">
                                                更新时间
                                            </label>
                                            <div style="padding: 12px 16px; border: 1px solid #e9ecef; border-radius: 6px; background-color: #f8f9fa; color: #495057; font-size: 14px;">
                                                2025-01-10 15:20:00
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 占位页面 -->
                <div id="customer-rate-config" class="page-content">
                    <div class="card">
                        <div class="card-body">
                            <h3>客户费率配置</h3>
                            <p>此页面功能开发中...</p>
                        </div>
                    </div>
                </div>

                <div id="rate-config-orders" class="page-content">
                    <div class="card">
                        <div class="card-body">
                            <h3>费率配置工单</h3>
                            <p>此页面功能开发中...</p>
                        </div>
                    </div>
                </div>

                <div id="customer-exchange-config" class="page-content">
                    <div class="card">
                        <div class="card-body">
                            <h3>客户汇率配置</h3>
                            <p>此页面功能开发中...</p>
                        </div>
                    </div>
                </div>

                <div id="exchange-config-orders" class="page-content">
                    <div class="card">
                        <div class="card-body">
                            <h3>汇率配置工单</h3>
                            <p>此页面功能开发中...</p>
                        </div>
                    </div>
                </div>

                <div id="customer-rate-query" class="page-content">
                    <div class="card">
                        <div class="card-body">
                            <h3>客户费率查询</h3>
                            <p>此页面功能开发中...</p>
                        </div>
                    </div>
                </div>

                <div id="customer-exchange-query" class="page-content">
                    <div class="card">
                        <div class="card-body">
                            <h3>客户汇率查询</h3>
                            <p>此页面功能开发中...</p>
                        </div>
                    </div>
                </div>

                <div id="provider-bill-details" class="page-content">
                    <div class="card">
                        <div class="card-body">
                            <h3>服务商账单明细</h3>
                            <p>此页面功能开发中...</p>
                        </div>
                    </div>
                </div>

                <div id="tenant-bill-details" class="page-content">
                    <div class="card">
                        <div class="card-body">
                            <h3>租户账单明细</h3>
                            <p>此页面功能开发中...</p>
                        </div>
                    </div>
                </div>

                <div id="reports" class="page-content">
                    <div class="card">
                        <div class="card-body">
                            <h3>报表分析</h3>
                            <p>此页面功能开发中...</p>
                        </div>
                    </div>
                </div>

                <div id="settings" class="page-content">
                    <div class="card">
                        <div class="card-body">
                            <h3>系统设置</h3>
                            <p>此页面功能开发中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 侧边栏折叠功能
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('collapsed');
        }

        // 子菜单展开/折叠功能
        function toggleSubmenu(element) {
            const submenu = element.nextElementSibling;
            const arrow = element.querySelector('.nav-arrow');

            if (submenu && submenu.classList.contains('nav-submenu')) {
                submenu.classList.toggle('open');
                if (arrow) {
                    arrow.style.transform = submenu.classList.contains('open') ? 'rotate(180deg)' : 'rotate(0deg)';
                }
            }
        }

        // 页面切换功能
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => {
                page.classList.remove('active');
            });

            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }

            // 更新导航状态
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.classList.remove('active');
            });

            // 找到对应的导航链接并激活
            const activeLink = document.querySelector(`[onclick="showPage('${pageId}')"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }

            // 更新页面标题
            const pageTitles = {
                'dashboard': '业务概览',
                'customer-info': '租户基本信息',
                'customer-products': '租户签约产品',
                'customer-rate-config': '租户费率配置',
                'rate-config-orders': '费率配置工单',
                'customer-exchange-config': '租户汇率配置',
                'exchange-config-orders': '汇率配置工单',
                'customer-rate-query': '租户收费查询',
                'customer-exchange-query': '租户汇率查询',
                'merchant-info': '商户查询',
                'merchant-products-dev': '商户签约产品查询',
                'merchant-rate-dev': '租户商户收费查询',
                'merchant-exchange-dev': '商户汇率查询',
                'merchant-fee-dev': '商户收费查询',
                'merchant-exchange-query-dev': '商户汇率查询',
                'provider-info': '服务商查询',
                'receive-transactions': '收款交易查询',
                'payment-transactions': '付款交易查询',
                'settlement-transactions': '结汇交易查询',
                'exchange-transactions': '换汇交易查询',
                'provider-bills': '服务商账单',
                'tenant-bills': '租户账单',
                'provider-bill-details': '服务商账单明细',
                'tenant-bill-details': '租户账单明细',
                'provider-settlements': '服务商结算记录',
                'tenant-settlements': '租户结算记录',
                'tenant-agent-info': '租户代理商基本信息',
                'tenant-agent-detail': '代理商详情',
                'tenant-agent-standard-rates': '标准代理底价',
                'tenant-agent-special-rates': '代理商特殊底价',
                'tenant-agent-bill-records': '代理商账单记录',
                'tenant-agent-bill-details': '代理商账单明细',
                'tenant-agent-settlement-records': '代理商结算记录',
                'reports': '报表分析',
                'settings': '系统设置'
            };

            const pageTitle = document.getElementById('pageTitle');
            if (pageTitle && pageTitles[pageId]) {
                pageTitle.textContent = pageTitles[pageId];
            }
        }

        // Tab切换功能
        function switchTab(tabId) {
            // 隐藏所有tab内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 显示目标tab
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // 更新tab按钮状态
            const tabBtns = document.querySelectorAll('.tab-btn');
            tabBtns.forEach(btn => {
                btn.classList.remove('active');
            });

            // 激活对应的tab按钮
            const activeBtn = document.querySelector(`[onclick="switchTab('${tabId}')"]`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }
        }

        // 移动端菜单切换
        function toggleMobileMenu() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('mobile-open');
        }

        // 响应式处理
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('mobile-open');
            }
        });

        // Tab切换功能 - 代理商账单记录页面
        function switchBillTab(tabId, buttonElement) {
            // 隐藏所有tab内容
            const allTabContents = document.querySelectorAll('#tenant-agent-bill-records .tab-content');
            allTabContents.forEach(content => {
                content.style.display = 'none';
            });

            // 显示选中的tab内容
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.style.display = 'block';
            }

            // 更新tab按钮状态
            const allTabButtons = document.querySelectorAll('#tenant-agent-bill-records .tab-btn');
            allTabButtons.forEach(btn => {
                btn.classList.remove('active');
                btn.style.borderBottom = '2px solid transparent';
                btn.style.color = '#6c757d';
            });

            // 设置当前tab按钮为活跃状态
            if (buttonElement) {
                buttonElement.classList.add('active');
                buttonElement.style.borderBottom = '2px solid #007bff';
                buttonElement.style.color = '#007bff';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示业务概览页面
            showPage('dashboard');
        });
    </script>
</body>
</html>
