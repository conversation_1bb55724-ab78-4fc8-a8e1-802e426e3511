# EX与2C2P付款业务合作方案

## 1. 项目概述

### 1.1 合作背景
- **2C2P**：新加坡、泰国知名支付机构，为商户提供收单和付款服务
- **EX**：提供跨境支付解决方案，具备强大的付款能力
- **合作目标**：通过API对接，为2C2P商户提供VA（虚拟账户）和付款能力

### 1.2 业务模式
- IPL提供VA给2C2P, 2C2P展示给商户
- 商户转账资金到VA, 触发付款流程
- 2C2P通过API指令EX执行付款
- EX代表商户完成对收款人的付款

## 2. 业务关系架构 (Business Relationship)

### 2.1 参与方角色
| 参与方 | 角色定义 | 主要职责 | 业务关系 |
|--------|----------|----------|----------|
| **商户(Merchant)** | 付款发起方 | 发起付款需求，转账到VA | 2C2P的直接客户 |
| **2C2P** | 支付服务商 | 商户服务, API对接, 指令传递 | EX的合作伙伴 |
| **EX** | 技术服务商 | API提供，付款执行 | 2C2P的技术供应商 |
| **IPL** | 资金通道 | VA提供，资金监控 | EX的资金合作方 |
| **收款人(Payee)** | 资金接收方 | 接收最终付款 | 商户的交易对手 |

### 2.2 业务关系图

```mermaid
graph TB
    M[商户 Merchant] -->|服务合同| 2C2P[2C2P 支付服务商]
    2C2P -->|技术合作协议| EX[EX 技术服务商]
    EX -->|资金通道协议| IPL[IPL 资金通道]
    M -->|付款指令| 2C2P
    2C2P -->|API调用| EX
    EX -->|资金操作| IPL
    IPL -->|最终付款| P[收款人 Payee]

    style M fill:#e1f5fe
    style 2C2P fill:#fff3e0
    style EX fill:#e8f5e8
    style IPL fill:#f3e5f5
    style P fill:#fce4ec
```

### 2.3 合作模式
- **B2B2C模式**: 2C2P作为中间服务商，为商户提供EX的技术能力
- **白标服务**: EX为2C2P提供白标付款解决方案
- **API集成**: 通过标准化API实现系统对接
- **风险共担**: 各方在业务流程中承担相应风险责任

## 3. 资金流向 (Funds Flow)

### 3.1 资金流向图

```mermaid
flowchart LR
    M[商户账户] -->|1. 转账| VA[虚拟账户 VA]
    VA -->|2. 资金池化| IPL[IPL主账户]
    IPL -->|3. 付款执行| P[收款人账户]

    subgraph "资金监控"
        VA -.->|实时监控| Monitor[资金监控系统]
        Monitor -.->|余额查询| IPL
    end

    subgraph "手续费"
        IPL -->|4a. 技术服务费| EX[EX收入]
        IPL -->|4b. 通道费用| 2C2P[2C2P收入]
    end

    style M fill:#e3f2fd
    style VA fill:#fff3e0
    style IPL fill:#e8f5e8
    style P fill:#fce4ec
    style EX fill:#f3e5f5
    style 2C2P fill:#fff8e1
```

### 3.2 详细资金流程

#### 阶段1: 资金入账
1. **商户转账**: 商户将付款资金转入指定VA账户
2. **资金确认**: IPL系统确认资金到账
3. **余额更新**: 更新商户可用余额

#### 阶段2: 资金池化
1. **资金归集**: VA资金自动归集到IPL主账户
2. **流动性管理**: IPL统一管理资金流动性
3. **风险控制**: 实时监控资金异常

#### 阶段3: 付款执行
1. **付款指令**: 2C2P发送付款指令给EX
2. **资金划转**: IPL从主账户划转资金
3. **最终付款**: 资金到达收款人账户

#### 阶段4: 费用结算
1. **技术服务费**: EX收取API调用和技术服务费
2. **通道费用**: 2C2P收取商户服务费和通道费
3. **银行费用**: IPL承担银行手续费和汇率成本

### 3.3 资金流时序图

```mermaid
sequenceDiagram
    participant M as 商户
    participant VA as 虚拟账户
    participant IPL as IPL主账户
    participant EX as EX系统
    participant 2C2P as 2C2P
    participant P as 收款人

    Note over M,P: 资金流向时序

    %% 资金入账阶段
    M->>VA: 1. 转账 $10,000
    Note right of M: 银行转账/在线支付

    VA->>IPL: 2. 资金归集 $10,000
    Note right of VA: 自动归集到主账户

    IPL->>EX: 3. 余额通知
    Note right of IPL: 实时余额更新

    %% 付款执行阶段
    2C2P->>EX: 4. 付款指令 $9,950
    Note right of 2C2P: 扣除服务费$50

    EX->>IPL: 5. 资金划转请求
    Note right of EX: 验证余额充足

    IPL->>P: 6. 执行付款 $9,930
    Note right of IPL: 扣除技术费$20

    %% 费用分配
    IPL->>2C2P: 7. 服务费 $30
    Note right of IPL: 通道费分成

    IPL->>EX: 8. 技术费 $20
    Note right of IPL: API服务费
```

### 3.4 资金账户结构

#### 3.4.1 虚拟账户 (VA)
- **账户类型**: 临时托管账户
- **资金性质**: 客户资金，专款专用
- **监管要求**: 符合各国资金托管法规
- **操作权限**: 仅支持入账和归集

#### 3.4.2 IPL主账户
- **账户类型**: 运营资金池
- **资金管理**: 统一流动性管理
- **风险控制**: 实时监控和预警
- **合规要求**: 满足反洗钱和KYC要求

#### 3.4.3 费用分配账户
```
总交易金额: $10,000
├── 商户服务费: $50 (0.5%) → 2C2P收入
├── 技术服务费: $20 (0.2%) → EX收入
├── 银行手续费: $30 (0.3%) → IPL成本
└── 净付款金额: $9,900 → 收款人
```

### 3.5 风险控制机制

#### 3.5.1 资金安全
- **资金隔离**: VA资金与运营资金严格分离
- **实时监控**: 24/7资金流向监控
- **异常预警**: 大额交易和异常模式预警
- **备付金管理**: 维持充足的流动性备付金

#### 3.5.2 合规风险
- **AML检查**: 所有交易进行反洗钱筛查
- **制裁名单**: 实时检查制裁和黑名单
- **交易限额**: 设置单笔和日累计限额
- **审计追踪**: 完整的资金流审计轨迹

### 3.6 对账与结算

#### 3.6.1 实时对账
- **T+0对账**: 实时资金流水对账
- **余额核对**: 每小时余额一致性检查
- **异常处理**: 自动识别和处理对账差异

#### 3.6.2 定期结算
- **日终结算**: 每日费用和收入结算
- **月度对账**: 月度综合对账报告
- **年度审计**: 第三方审计和合规检查

## 4. 业务流程

### 4.1 整体流程图

```mermaid
flowchart TD
    A[商户发起付款需求] --> B[2C2P为商户分配VA]
    B --> C[商户转账到VA]
    C --> D[IPL监控到资金入账]
    D --> E[2C2P发送付款指令给EX]
    E --> F[EX验证指令和资金]
    F --> G{资金是否充足?}
    G -->|是| H[EX执行付款]
    G -->|否| I[返回余额不足]
    H --> J[付款成功]
    J --> K[通知2C2P]
    K --> L[2C2P通知商户]
    I --> M[2C2P通知商户失败]
    
    style A fill:#e1f5fe
    style J fill:#e8f5e8
    style I fill:#ffebee
    style M fill:#ffebee
```

### 4.2 详细时序图

```mermaid
sequenceDiagram
    participant M as 商户
    participant 2C2P as 2C2P
    participant EX as EX
    participant IPL as IPL
    participant P as 收款人

    Note over M,P: 付款业务流程

    %% 1. 付款准备阶段
    M->>2C2P: 1. 发起付款请求
    Note right of M: 包含收款人信息、金额等
    
    2C2P->>EX: 2. 请求分配VA
    Note right of 2C2P: 传递商户ID、付款信息
    
    EX->>IPL: 3. 申请VA账户
    IPL-->>EX: 4. 返回VA信息
    EX-->>2C2P: 5. 返回VA详情
    2C2P-->>M: 6. 展示VA给商户
    
    %% 2. 资金转入阶段
    M->>IPL: 7. 转账到VA
    Note right of M: 银行转账或其他方式
    
    IPL->>EX: 8. 资金入账通知
    Note right of IPL: Webhook通知
    
    %% 3. 付款执行阶段
    2C2P->>EX: 9. 发送付款指令
    Note right of 2C2P: API调用，包含付款详情
    
    EX->>EX: 10. 验证资金和指令
    
    alt 资金充足且指令有效
        EX->>P: 11. 执行付款
        P-->>EX: 12. 付款确认
        EX-->>2C2P: 13. 付款成功通知
        2C2P-->>M: 14. 通知商户成功
    else 资金不足或指令无效
        EX-->>2C2P: 13. 付款失败通知
        2C2P-->>M: 14. 通知商户失败
    end
```

## 5. API接口设计

### 5.1 核心接口列表

| 接口名称 | 方法 | 描述 | 调用方 |
|----------|------|------|--------|
| `/api/va/create` | POST | 创建虚拟账户 | 2C2P→EX |
| `/api/payment/execute` | POST | 执行付款指令 | 2C2P→EX |
| `/api/payment/status` | GET | 查询付款状态 | 2C2P→EX |
| `/webhook/fund-received` | POST | 资金入账通知 | IPL→EX |
| `/webhook/payment-result` | POST | 付款结果通知 | EX→2C2P |

### 5.2 关键接口示例

#### 5.2.1 创建虚拟账户
```json
POST /api/va/create
{
  "merchant_id": "2C2P_MERCHANT_001",
  "payment_reference": "PAY_20240101_001",
  "amount": 10000.00,
  "currency": "USD",
  "payee_info": {
    "name": "John Doe",
    "account": "**********",
    "bank_code": "SWIFT_CODE"
  }
}
```

#### 5.2.2 执行付款指令
```json
POST /api/payment/execute
{
  "va_reference": "VA_20240101_001",
  "payment_reference": "PAY_20240101_001",
  "amount": 10000.00,
  "currency": "USD"
}
```

## 6. 风控和合规

### 6.1 资金安全
- VA资金实时监控
- 付款前资金验证
- 异常交易预警

### 6.2 合规要求
- KYC/AML检查
- 交易限额控制
- 监管报告

### 6.3 对账机制
- 日终对账
- 实时余额查询
- 异常处理流程

## 7. 技术实现要点

### 7.1 系统集成
- RESTful API设计
- Webhook事件通知
- 幂等性保证
- 超时重试机制

### 7.2 数据安全
- HTTPS加密传输
- API签名验证
- 敏感数据脱敏
- 访问日志记录

### 7.3 性能优化
- 异步处理
- 缓存机制
- 负载均衡
- 监控告警

## 8. 部署和运维

### 8.1 环境要求
- 生产环境高可用部署
- 灾备机制
- 监控体系

### 8.2 运维支持
- 7×24小时监控
- 故障快速响应
- 定期系统维护

## 9. 商业模式

### 9.1 收费模式
- 按交易笔数收费
- 按交易金额比例收费
- 月度服务费

### 9.2 结算周期
- T+1结算
- 月度对账
- 费用透明化

## 10. 实施计划

### 10.1 项目阶段

```mermaid
gantt
    title EX与2C2P合作项目实施计划
    dateFormat  YYYY-MM-DD
    section 需求分析
    需求调研           :done, req1, 2024-01-01, 2024-01-07
    技术方案设计       :done, req2, 2024-01-08, 2024-01-14

    section 开发阶段
    API接口开发        :dev1, 2024-01-15, 2024-02-15
    VA系统集成         :dev2, 2024-01-20, 2024-02-10
    支付流程开发       :dev3, 2024-01-25, 2024-02-20

    section 测试阶段
    单元测试           :test1, 2024-02-16, 2024-02-25
    集成测试           :test2, 2024-02-21, 2024-03-05
    UAT测试            :test3, 2024-03-01, 2024-03-15

    section 上线部署
    生产环境部署       :deploy1, 2024-03-16, 2024-03-20
    试运行             :deploy2, 2024-03-21, 2024-03-31
    正式上线           :deploy3, 2024-04-01, 2024-04-01
```

### 10.2 里程碑节点
- **M1**: 技术方案确认 (2024-01-14)
- **M2**: API开发完成 (2024-02-15)
- **M3**: 集成测试通过 (2024-03-05)
- **M4**: 正式上线 (2024-04-01)

## 11. 风险评估与应对

### 11.1 技术风险
| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| API性能问题 | 中 | 影响用户体验 | 压力测试，性能优化 |
| 系统集成复杂 | 高 | 延期上线 | 分阶段集成，充分测试 |
| 数据安全 | 高 | 合规风险 | 加密传输，安全审计 |

### 11.2 业务风险
| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| 监管政策变化 | 中 | 业务调整 | 密切关注政策，灵活调整 |
| 汇率波动 | 中 | 成本增加 | 汇率对冲，风险分担 |
| 竞争加剧 | 低 | 市场份额 | 差异化服务，提升体验 |

## 12. 成功指标

### 12.1 技术指标
- **系统可用性**: ≥99.9%
- **API响应时间**: ≤2秒
- **付款成功率**: ≥99.5%
- **资金到账时效**: T+0实时

### 12.2 业务指标
- **月交易笔数**: 目标10,000笔
- **月交易金额**: 目标$10M
- **客户满意度**: ≥95%
- **客户留存率**: ≥90%

## 13. 附录

### 13.1 术语表
- **VA**: Virtual Account, 虚拟账户
- **2C2P**: 新加坡、泰国支付服务商
- **EX**: 跨境支付技术服务商
- **IPL**: 资金通道提供方
- **API**: Application Programming Interface
- **KYC**: Know Your Customer
- **AML**: Anti-Money Laundering

### 13.2 联系方式
- **EX技术团队**: <EMAIL>
- **2C2P对接团队**: <EMAIL>
- **项目经理**: <EMAIL>

### 13.3 相关文档
- [API技术规范文档]
- [安全合规指南]
- [运维手册]
- [故障处理流程]

---

**文档版本**: v1.0
**创建日期**: 2024-01-01
**更新日期**: 2024-01-01
**负责人**: EX技术团队
**审核人**: EX产品团队
