<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汇率返点管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        body {
            background-color: #f0f2f5;
            color: #333;
            padding: 20px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
        }
        .page-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e8e8e8;
        }
        .page-title {
            font-size: 24px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: 600;
            margin-bottom: 8px;
        }
        .page-subtitle {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
        }
        .breadcrumb {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            margin-bottom: 8px;
        }
        .breadcrumb a {
            color: #1890ff;
            text-decoration: none;
        }
        
        /* Tab样式 */
        .tab-container {
            margin-bottom: 24px;
        }
        .tab-header {
            display: flex;
            border-bottom: 1px solid #e8e8e8;
            margin-bottom: 16px;
        }
        .tab-item {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: rgba(0, 0, 0, 0.65);
            transition: all 0.3s;
        }
        .tab-item:hover {
            color: #1890ff;
        }
        .tab-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            font-weight: 500;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        
        /* 操作栏样式 */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .search-box {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        .search-box input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            width: 200px;
        }
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            border: none;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        .btn-default {
            background-color: #fff;
            border: 1px solid #d9d9d9;
            color: rgba(0, 0, 0, 0.65);
        }
        .btn-default:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }
        
        /* 表格样式 */
        .table-container {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            overflow: hidden;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        .table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            border-bottom: 1px solid #e8e8e8;
        }
        .table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            color: rgba(0, 0, 0, 0.85);
        }
        .table tbody tr:hover {
            background: #f5f5f5;
        }
        .table tbody tr:last-child td {
            border-bottom: none;
        }
        
        /* 状态标签 */
        .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-standard {
            background: #e6f7ff;
            color: #1890ff;
        }
        .status-special {
            background: #fff2e8;
            color: #fa8c16;
        }
        
        /* 操作按钮 */
        .action-buttons {
            display: flex;
            gap: 8px;
        }
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        .btn-link {
            background: none;
            border: none;
            color: #1890ff;
            cursor: pointer;
            padding: 4px 8px;
            font-size: 12px;
        }
        .btn-link:hover {
            color: #40a9ff;
        }
        .btn-danger {
            color: #ff4d4f;
        }
        .btn-danger:hover {
            color: #ff7875;
        }
        
        /* 汇率简介样式 */
        .exchange-intro {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 14px;
            color: #fa8c16;
        }
        
        /* 货币对样式 */
        .currency-pair {
            font-weight: 500;
            color: #1890ff;
        }
        
        /* 加点/返点样式 */
        .rate-value {
            font-weight: 500;
        }
        .rate-positive {
            color: #52c41a;
        }
        .rate-negative {
            color: #ff4d4f;
        }
        
        /* 弹层样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: #fff;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }
        
        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .close {
            font-size: 24px;
            font-weight: bold;
            color: #999;
            cursor: pointer;
            line-height: 1;
        }
        
        .close:hover {
            color: #333;
        }
        
        .modal-body {
            padding: 24px;
        }
        
        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #e8e8e8;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }
        
        /* 表单样式 */
        .form-section {
            margin-bottom: 24px;
        }
        
        .form-section h4 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }
        
        .form-group {
            flex: 1;
        }
        
        .form-group.half {
            flex: 0 0 calc(50% - 8px);
        }
        
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }
        
        .required {
            color: #ff4d4f;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        /* 单选按钮组 - 卡片式设计 */
        .radio-group {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .radio-label {
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-weight: normal !important;
            margin-bottom: 0 !important;
            padding: 12px 20px;
            border: 2px solid #e8e8e8;
            border-radius: 8px;
            background: #fafafa;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 120px;
            position: relative;
        }
        
        .radio-label:hover {
            border-color: #1890ff;
            background: #f0f8ff;
        }
        
        .radio-label input[type="radio"] {
            display: none;
        }
        
        .radio-label input[type="radio"]:checked + .radio-custom {
            display: none;
        }
        
        .radio-label input[type="radio"]:checked {
            display: none;
        }
        
        .radio-label.checked {
            border-color: #1890ff;
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            color: white;
            font-weight: 500;
        }
        
        .radio-label.checked::before {
            content: '✓';
            position: absolute;
            right: 8px;
            top: 8px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .radio-custom {
            display: none;
        }
        
        /* 带单位的输入框 */
        .input-with-unit {
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .input-with-unit input {
            padding-right: 30px;
        }
        
        .input-with-unit .unit {
            position: absolute;
            right: 12px;
            color: #666;
            font-size: 14px;
            pointer-events: none;
        }
        
        /* 客户名称显示 */
        .customer-name {
            padding: 8px 12px;
            background-color: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            min-height: 34px;
            display: flex;
            align-items: center;
            color: #666;
            font-size: 14px;
        }
        
        /* 报价信息展示 */
        .quote-info {
            background-color: #f9f9f9;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 16px;
            margin: 16px 0;
        }
        
        .quote-row {
            display: flex;
            gap: 32px;
        }
        
        .quote-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quote-item label {
            font-weight: 500;
            color: #666;
            margin-bottom: 0 !important;
        }
        
        .quote-item span {
            font-weight: 600;
            color: #1890ff;
            font-size: 16px;
        }
        
        /* 预计收益展示 */
        .profit-info {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 12px 16px;
            margin: 16px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .profit-info label {
            font-weight: 500;
            color: #52c41a;
            margin-bottom: 0 !important;
        }
        
        .profit-info span {
            font-weight: 600;
            color: #52c41a;
            font-size: 16px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                margin: 20px;
            }
            
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .form-group.half {
                flex: 1;
            }
            
            .quote-row {
                flex-direction: column;
                gap: 12px;
            }
            
            .radio-group {
                flex-direction: column;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <div class="breadcrumb">
                <a href="计费管理.html">代理商管理</a> / 汇率返点管理
            </div>
            <div class="page-title">汇率返点管理</div>
            <div class="page-subtitle">管理代理商的汇率加点和返点配置</div>
        </div>

        <!-- Tab容器 -->
        <div class="tab-container">
            <div class="tab-header">
                <div class="tab-item active" onclick="switchTab('base-rate')">底价汇率</div>
                <div class="tab-item" onclick="switchTab('fixed-rebate')">固定返点</div>
            </div>

            <!-- 底价汇率Tab -->
            <div id="base-rate-tab" class="tab-content active">
                <div class="exchange-intro">
                    <strong>底价汇率简介：</strong>代理商可以在系统底价汇率基础上加点，向商户提供汇率服务并获得差价收益。加点越高，代理商收益越大，但商户成本也越高。
                </div>
                
                <div class="action-bar">
                    <div class="search-box">
                        <input type="text" placeholder="搜索货币对">
                        <button class="btn btn-default">搜索</button>
                    </div>
                    <button class="btn btn-primary" onclick="addExchangeRate('base-rate')">新增底价汇率</button>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>返点简介</th>
                                <th>类型</th>
                                <th>货币对</th>
                                <th>加点</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="base-rate-table-body">
                            <tr>
                                <td><span class="status-tag status-standard">A类代理返点</span></td>
                                <td><span class="status-tag status-standard">标准</span></td>
                                <td><span class="currency-pair">USD/CNY</span></td>
                                <td><span class="rate-value rate-negative">-0.15%</span></td>
                                <td>2024-01-15 10:30:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="status-tag status-special">B类代理返点</span></td>
                                <td><span class="status-tag status-special">特殊</span></td>
                                <td><span class="currency-pair">EUR/CNY</span></td>
                                <td><span class="rate-value rate-negative">-0.15%</span></td>
                                <td>2024-01-14 15:20:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="status-tag status-special">001特殊代理返点</span></td>
                                <td><span class="status-tag status-standard">标准</span></td>
                                <td><span class="currency-pair">GBP/CNY</span></td>
                                <td><span class="rate-value rate-negative">-0.12%</span></td>
                                <td>2024-01-13 09:15:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="status-tag status-special">004特殊代理返点</span></td>
                                <td><span class="status-tag status-special">特殊</span></td>
                                <td><span class="currency-pair">JPY/CNY</span></td>
                                <td><span class="rate-value rate-negative">-0.12%</span></td>
                                <td>2024-01-12 14:45:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 固定返点Tab -->
            <div id="fixed-rebate-tab" class="tab-content">
                <div class="exchange-intro">
                    <strong>固定返点简介：</strong>代理商按照固定返点比例从汇率差价中获得分成，无汇率定价权。适用于标准化运营，系统统一管理汇率价格。
                </div>
                
                <div class="action-bar">
                    <div class="search-box">
                        <input type="text" placeholder="搜索货币对">
-                        <button class="btn btn-default">搜索</button>
                    </div>
                    <button class="btn btn-primary" onclick="addExchangeRate('fixed-rebate')">新增固定返点</button>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>返点简介</th>
                                <th>类型</th>
                                <th>货币对</th>
                                <th>返点比例</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="fixed-rebate-table-body">
                            <tr>
                                <td><span class="status-tag status-special">A类代理返点</span></td>
                                <td><span class="status-tag status-standard">标准</span></td>
                                <td><span class="currency-pair">USD/CNY</span></td>
                                <td><span class="rate-value rate-positive">600bps</span></td>
                                <td>2024-01-15 10:30:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="status-tag status-special">B类代理返点</span></td>
                                <td><span class="status-tag status-special">特殊</span></td>
                                <td><span class="currency-pair">EUR/CNY</span></td>
                                <td><span class="rate-value rate-positive">750bps</span></td>
                                <td>2024-01-14 15:20:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="status-tag status-special">001代理返点</span></td>
                                <td><span class="status-tag status-standard">标准</span></td>
                                <td><span class="currency-pair">GBP/CNY</span></td>
                                <td><span class="rate-value rate-positive">500bps</span></td>
                                <td>2024-01-13 09:15:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="status-tag status-special">002代理返点</span></td>
                                <td><span class="status-tag status-special">特殊</span></td>
                                <td><span class="currency-pair">JPY/CNY</span></td>
                                <td><span class="rate-value rate-positive">800bps</span></td>
                                <td>2024-01-12 14:45:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-link">编辑</button>
                                        <button class="btn-link btn-danger">删除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增返点配置弹层 -->
    <div id="rebateModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新增固定返点配置</h3>
                <span class="close" onclick="hideRebateModal()">&times;</span>
            </div>
            
            <form id="rebateForm" class="modal-body">
                <!-- 基本信息 -->
                <div class="form-section">
                    <h4>基本信息</h4>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="description">返点简介 <span class="required">*</span></label>
                            <input type="text" id="description" name="description" placeholder="请输入返点简介" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>返点类型 <span class="required">*</span></label>
                            <div class="radio-group">
                                <label class="radio-label checked" onclick="selectRadio(this, 'rebateType', 'standard')">
                                    <input type="radio" name="rebateType" value="standard" checked>
                                    标准返点
                                </label>
                                <label class="radio-label" onclick="selectRadio(this, 'rebateType', 'special')">
                                    <input type="radio" name="rebateType" value="special">
                                    特殊返点
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 返点配置 -->
                <div class="form-section">
                    <h4>返点配置</h4>
                    
                    <div class="form-row">
                        <div class="form-group half">
                            <label for="sellCurrency">卖出币种 <span class="required">*</span></label>
                            <select id="sellCurrency" name="sellCurrency" onchange="onSellCurrencyChange()" required>
                                <option value="">请选择币种</option>
                                <option value="USD">USD - 美元</option>
                                <option value="EUR">EUR - 欧元</option>
                                <option value="GBP">GBP - 英镑</option>
                                <option value="JPY">JPY - 日元</option>
                                <option value="HKD">HKD - 港币</option>
                                <option value="AUD">AUD - 澳元</option>
                                <option value="CAD">CAD - 加元</option>
                                <option value="SGD">SGD - 新币</option>
                            </select>
                        </div>
                        
                        <div class="form-group half">
                            <label for="buyCurrency">买入币种 <span class="required">*</span></label>
                            <select id="buyCurrency" name="buyCurrency" onchange="updateQuoteAndProfit()" required>
                                <option value="">请选择币种</option>
                                <option value="CNY">CNY - 人民币</option>
                                <option value="USD">USD - 美元</option>
                                <option value="EUR">EUR - 欧元</option>
                                <option value="GBP">GBP - 英镑</option>
                                <option value="JPY">JPY - 日元</option>
                                <option value="HKD">HKD - 港币</option>
                                <option value="AUD">AUD - 澳元</option>
                                <option value="CAD">CAD - 加元</option>
                                <option value="SGD">SGD - 新币</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group half">
                            <label for="sellAmount">卖出金额</label>
                            <div class="amount-display">
                                <span id="sellAmountDisplay">1,000</span>
                                <span class="currency-unit" id="sellCurrencyUnit"></span>
                            </div>
                            <input type="hidden" id="sellAmount" name="sellAmount" value="1000">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>报价类型 <span class="required">*</span></label>
                            <div class="radio-group">
                                <label class="radio-label">
                                    <input type="radio" name="quoteType" value="standard" checked onchange="toggleQuoteType()">
                                    <span class="radio-custom"></span>
                                    标准对客报价
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="quoteType" value="special" onchange="toggleQuoteType()">
                                    <span class="radio-custom"></span>
                                    特殊客户报价
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div id="customerAccountGroup" class="form-row" style="display: none;">
                        <div class="form-group half">
                            <label for="customerAccount">账户号码</label>
                            <input type="text" id="customerAccount" name="customerAccount" placeholder="请输入账户号码" onblur="queryCustomer()">
                        </div>
                        <div class="form-group half">
                            <label>商户名称</label>
                            <div class="customer-name" id="customerName"></div>
                        </div>
                    </div>
                    
                    <!-- 报价信息展示 -->
                    <div class="quote-info">
                        <div class="quote-row">
                            <div class="quote-item">
                                <label>对客报价:</label>
                                <span id="clientQuote">0.0000</span>
                            </div>
                            <div class="quote-item">
                                <label>成本报价:</label>
                                <span id="costQuote">0.0000</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group half">
                            <label for="rebateValue">返点值 <span class="required">*</span></label>
                            <div class="input-with-unit">
                                <input type="number" id="rebateValue" name="rebateValue" step="0.000001" max="100" placeholder="0.000000" onchange="updateQuoteAndProfit()" required>
                                <span class="unit">%</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 预计收益展示 -->
                    <div class="profit-info">
                        <label>预计收益:</label>
                        <span id="expectedProfit">0.00</span> CNY
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="remarks">备注</label>
                            <textarea id="remarks" name="remarks" rows="3" placeholder="请输入备注信息"></textarea>
                        </div>
                    </div>
                </div>
            </form>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-default" onclick="hideRebateModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRebateConfig()">保存</button>
            </div>
        </div>
    </div>

    <script>
        // Tab切换功能
        function switchTab(tabName) {
            // 隐藏所有tab内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有tab项的active状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示选中的tab内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活选中的tab项
            event.target.classList.add('active');
        }
        
        // 新增汇率配置
        function addExchangeRate(type) {
            if (type === 'fixed-rebate') {
                showAddRebateModal();
            } else {
                // 其他类型跳转到配置页面
                window.location.href = `exchange-rate-config.html?type=${type}`;
            }
        }
        
        // 显示新增返点弹层
        function showAddRebateModal() {
            document.getElementById('rebateModal').style.display = 'flex';
            resetRebateForm();
        }
        
        // 隐藏弹层
        function hideRebateModal() {
            document.getElementById('rebateModal').style.display = 'none';
        }
        
        // 重置表单
        function resetRebateForm() {
            document.getElementById('rebateForm').reset();
            document.getElementById('customerAccountGroup').style.display = 'none';
            document.getElementById('customerName').textContent = '';
            document.getElementById('clientQuote').textContent = '0.0000';
            document.getElementById('costQuote').textContent = '0.0000';
            document.getElementById('expectedProfit').textContent = '0.00';
        }
        
        // 切换报价类型
        function toggleQuoteType() {
            const quoteType = document.querySelector('input[name="quoteType"]:checked').value;
            const customerAccountGroup = document.getElementById('customerAccountGroup');
            
            if (quoteType === 'special') {
                customerAccountGroup.style.display = 'block';
            } else {
                customerAccountGroup.style.display = 'none';
                document.getElementById('customerAccount').value = '';
                document.getElementById('customerName').textContent = '';
            }
            
            updateQuoteAndProfit();
        }
        
        // 查询客户信息
        function queryCustomer() {
            const account = document.getElementById('customerAccount').value;
            const customerNames = {
                'ACC001': '北京科技有限公司',
                'ACC002': '上海贸易公司',
                'ACC003': '深圳电商企业',
                'ACC004': '广州进出口公司'
            };
            
            const customerName = customerNames[account] || '';
            document.getElementById('customerName').textContent = customerName;
            
            if (customerName) {
                updateQuoteAndProfit();
            }
        }
        
        // 更新报价和收益
        function updateQuoteAndProfit() {
            const sellCurrency = document.getElementById('sellCurrency').value;
            const buyCurrency = document.getElementById('buyCurrency').value;
            const sellAmount = parseFloat(document.getElementById('sellAmount').value) || 1000;
            const rebateValue = parseFloat(document.getElementById('rebateValue').value) || 0;
            const quoteType = document.querySelector('input[name="quoteType"]:checked').value;
            
            if (!sellCurrency || !buyCurrency || sellCurrency === buyCurrency) {
                return;
            }
            
            // 模拟汇率数据
            const exchangeRates = {
                'USD-CNY': { client: 7.1230, cost: 7.1330 },
                'EUR-CNY': { client: 7.8450, cost: 7.8550 },
                'GBP-CNY': { client: 9.1200, cost: 9.1300 },
                'JPY-CNY': { client: 0.0485, cost: 0.0495 },
                'HKD-CNY': { client: 0.9120, cost: 0.9130 },
                'AUD-CNY': { client: 4.7800, cost: 4.7900 },
                'CAD-CNY': { client: 5.2300, cost: 5.2400 },
                'SGD-CNY': { client: 5.3100, cost: 5.3200 }
            };
            
            const rateKey = `${sellCurrency}-${buyCurrency}`;
            const reverseKey = `${buyCurrency}-${sellCurrency}`;
            
            let clientQuote = 0, costQuote = 0;
            
            if (exchangeRates[rateKey]) {
                clientQuote = exchangeRates[rateKey].client;
                costQuote = exchangeRates[rateKey].cost;
            } else if (exchangeRates[reverseKey]) {
                clientQuote = 1 / exchangeRates[reverseKey].client;
                costQuote = 1 / exchangeRates[reverseKey].cost;
            }
            
            // 特殊客户报价调整
            if (quoteType === 'special') {
                clientQuote *= 0.999; // 给特殊客户更优惠的价格
                costQuote *= 0.999;
            }
            
            document.getElementById('clientQuote').textContent = clientQuote.toFixed(4);
            document.getElementById('costQuote').textContent = costQuote.toFixed(4);
            
            // 计算预计收益
            const spread = costQuote - clientQuote;
            const rebateAmount = spread * (rebateValue / 100);
            const expectedProfit = (spread - rebateAmount) * sellAmount;
            
            document.getElementById('expectedProfit').textContent = expectedProfit.toFixed(2);
        }
        
        // 保存返点配置
        function saveRebateConfig() {
            const formData = new FormData(document.getElementById('rebateForm'));
            const rebateData = {
                description: formData.get('description'),
                rebateType: formData.get('rebateType'),
                sellCurrency: formData.get('sellCurrency'),
                buyCurrency: formData.get('buyCurrency'),
                sellAmount: formData.get('sellAmount'),
                quoteType: formData.get('quoteType'),
                customerAccount: formData.get('customerAccount'),
                rebateValue: formData.get('rebateValue'),
                remarks: formData.get('remarks')
            };
            
            // 表单验证
            if (!rebateData.description || !rebateData.sellCurrency || !rebateData.buyCurrency || !rebateData.rebateValue) {
                alert('请填写所有必填字段');
                return;
            }
            
            if (rebateData.sellCurrency === rebateData.buyCurrency) {
                alert('卖出币种和买入币种不能相同');
                return;
            }
            
            if (rebateData.quoteType === 'special' && !rebateData.customerAccount) {
                alert('特殊客户报价需要输入账户号码');
                return;
            }
            
            // 模拟保存
            console.log('保存返点配置:', rebateData);
            alert('返点配置保存成功！');
            hideRebateModal();
            
            // 这里可以刷新表格数据
            // loadRebateList();
        }
        
        // 编辑汇率配置
        function editExchangeRate(id, type) {
            window.location.href = `exchange-rate-config.html?id=${id}&type=${type}`;
        }
        
        // 删除汇率配置
        function deleteExchangeRate(id) {
            if (confirm('确定要删除这个汇率配置吗？')) {
                // 这里添加删除逻辑
                console.log('删除汇率配置:', id);
            }
        }
    </script>
</body>
</html>
