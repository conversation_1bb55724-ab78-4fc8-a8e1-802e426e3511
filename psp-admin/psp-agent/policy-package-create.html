<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增政策包</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: #f0f2f5;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
        }

        /* 页面头部 */
        .page-header {
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e8e8e8;
        }

        .breadcrumb {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            margin-bottom: 8px;
        }

        .breadcrumb a {
            color: #1890ff;
            text-decoration: none;
        }

        .page-title {
            font-size: 24px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: 600;
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
        }

        /* 表单区域 */
        .form-section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            margin-bottom: 12px;
            padding-bottom: 6px;
            border-bottom: 2px solid #1890ff;
        }

        .form-row {
            display: flex;
            gap: 24px;
            margin-bottom: 12px;
            align-items: flex-start;
        }

        .form-item {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-item.inline {
            flex-direction: row;
            align-items: center;
            gap: 12px;
        }

        .form-item.inline label {
            min-width: 100px;
            margin-bottom: 0;
        }

        .form-item label {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: 500;
        }

        .form-item label.required::after {
            content: " *";
            color: #ff4d4f;
        }

        .form-item input, .form-item select, .form-item textarea {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-item input:focus, .form-item select:focus, .form-item textarea:focus {
            outline: none;
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .form-item textarea {
            resize: vertical;
            min-height: 80px;
        }

        /* 单选框和复选框样式 */
        .radio-group, .checkbox-group {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }

        .radio-item, .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .radio-item:hover, .checkbox-item:hover {
            border-color: #40a9ff;
            background: #f0f8ff;
        }

        .radio-item input:checked + span, .checkbox-item input:checked + span {
            color: #1890ff;
            font-weight: 500;
        }

        .radio-item:has(input:checked), .checkbox-item:has(input:checked) {
            border-color: #1890ff;
            background: #e6f7ff;
        }

        /* 提示信息 */
        .info-tip {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            padding: 12px;
            margin-top: 8px;
            font-size: 13px;
            color: #1890ff;
        }

        /* 双栏选择器 */
        .dual-selector {
            display: flex;
            gap: 16px;
            margin-top: 16px;
        }

        .selector-column {
            flex: 1;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            overflow: hidden;
        }

        .selector-header {
            background: #fafafa;
            padding: 12px 16px;
            border-bottom: 1px solid #e8e8e8;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
        }

        .selector-search {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .selector-search input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }

        .selector-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .selector-item {
            padding: 8px 12px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.3s;
        }

        .selector-item:hover {
            background: #f5f5f5;
        }

        .selector-item.selected {
            background: #e6f7ff;
            color: #1890ff;
        }

        .selector-item:last-child {
            border-bottom: none;
        }

        .item-title {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .item-desc {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            border: none;
            font-size: 14px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: rgba(0, 0, 0, 0.65);
            border: 1px solid #d9d9d9;
        }

        .btn-secondary:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }

        .btn-large {
            padding: 12px 24px;
            font-size: 16px;
        }

        /* 操作按钮区域 */
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-top: 20px;
            padding-top: 16px;
            border-top: 1px solid #e8e8e8;
        }

        /* 错误提示 */
        .error-message {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            border-radius: 4px;
            padding: 8px 12px;
            margin-top: 8px;
            font-size: 13px;
            color: #ff4d4f;
        }

        /* 隐藏元素 */
        .hidden {
            display: none;
        }

        /* 代理商信息显示 */
        .agent-info, .merchant-info {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 12px;
            margin-top: 8px;
            font-size: 13px;
        }

        .info-item {
            margin-bottom: 4px;
        }

        .info-label {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
        }

        .info-value {
            color: #52c41a;
        }

        /* 政策内容区域 */
        .policy-content {
            margin-top: 16px;
        }

        .policy-module {
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            margin-bottom: 12px;
            overflow: hidden;
        }

        .module-header {
            background: #fafafa;
            padding: 8px 12px;
            border-bottom: 1px solid #e8e8e8;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
        }

        .module-content {
            padding: 12px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .dual-selector {
                flex-direction: column;
            }
            
            .form-row {
                flex-direction: column;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="breadcrumb">
                <a href="#">代理商管理</a> / <a href="#">代理政策管理</a> / 新增政策包
            </div>
            <div class="page-title">新增政策包</div>
            <div class="page-subtitle">创建新的代理商政策包，包含交易分佣和汇率返点配置</div>
        </div>

        <form id="policyForm">
            <!-- 1. 基本信息 -->
            <div class="form-section">
                <div class="section-title">1. 基本信息</div>

                <div class="form-row">
                    <div class="form-item" style="flex: 1;">
                        <label class="required">政策包编号</label>
                        <input type="text" id="policyName" placeholder="请输入政策包名称" required style="width: 300px;">
                    </div>
                    <div class="form-item" style="flex: 2;">
                        <label class="required">政策包简介</label>
                        <input type="text" id="policyIntro" placeholder="请输入政策包简介" required style="width: 100%;">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-item" style="flex: 1;">
                        <label>备注</label>
                        <textarea id="policyRemark" placeholder="请输入备注信息（可选）" style="height: 60px;"></textarea>
                    </div>
                </div>
            </div>

            

            <!-- 2. 政策内容 -->
            <div class="form-section">
                <div class="section-title">2. 政策内容</div>

                <!-- 选择政策类型 -->
                <div class="form-row">
                    <div class="form-item inline">
                        <label class="required">政策类型</label>
                        <div class="checkbox-group">
                            <label class="checkbox-item">
                                <input type="checkbox" name="policyType" value="commission" onchange="togglePolicyModule('commission')">
                                <span>交易分佣</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="policyType" value="exchange" onchange="togglePolicyModule('exchange')">
                                <span>汇率返点</span>
                            </label>
                            <label class="checkbox-item" style="opacity: 0.5; cursor: not-allowed;">
                                <input type="checkbox" name="policyType" value="newCustomer" disabled>
                                <span>新客分佣（即将上线）</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 交易分佣模块 -->
                <div id="commissionModule" class="policy-module hidden">
                    <div class="module-header">交易分佣配置</div>
                    <div class="module-content">
                        <div class="dual-selector">
                            <!-- 可选列表 -->
                            <div class="selector-column">
                                <div class="selector-header">可选交易分佣</div>
                                <div class="selector-search">
                                    <input type="text" placeholder="搜索产品名称或分佣简介" onkeyup="filterCommissionOptions(this.value)">
                                </div>
                                <div class="selector-list" id="commissionOptions">
                                    <div class="selector-item" data-id="comm1" data-product="B2C-电商收款" onclick="selectCommissionItem(this)">
                                        <div class="item-title">A类代理商底价 - B2C-电商收款</div>
                                        <div class="item-desc">标准底价分佣，适用于A级代理商</div>
                                    </div>
                                    <div class="selector-item" data-id="comm2" data-product="B2B-外贸收款" onclick="selectCommissionItem(this)">
                                        <div class="item-title">B类代理商底价 - B2B-外贸收款</div>
                                        <div class="item-desc">特殊底价分佣，适用于B级代理商</div>
                                    </div>
                                    <div class="selector-item" data-id="comm3" data-product="全球付款" onclick="selectCommissionItem(this)">
                                        <div class="item-title">C类代理商底价 - 全球付款</div>
                                        <div class="item-desc">标准底价分佣，适用于C级代理商</div>
                                    </div>
                                    <div class="selector-item" data-id="comm4" data-product="B2C-电商收款" onclick="selectCommissionItem(this)">
                                        <div class="item-title">A类代理商固定比例 - B2C-电商收款</div>
                                        <div class="item-desc">固定比例分佣60%，适用于A级代理商</div>
                                    </div>
                                    <div class="selector-item" data-id="comm5" data-product="本地付款" onclick="selectCommissionItem(this)">
                                        <div class="item-title">标准固定比例 - 本地付款</div>
                                        <div class="item-desc">固定比例分佣50%，标准配置</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 已选列表 -->
                            <div class="selector-column">
                                <div class="selector-header">已选交易分佣</div>
                                <div class="selector-list" id="selectedCommissions">
                                    <div style="padding: 40px; text-align: center; color: #999;">
                                        暂无选择
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="commissionError" class="error-message hidden">
                            一个产品只能选择一个分佣配置！
                        </div>
                        <div class="info-message" style="margin-top: 10px; padding: 8px 12px; background-color: #e7f3ff; border-left: 4px solid #007bff; color: #004085; font-size: 14px;">
                            <i class="fas fa-info-circle" style="margin-right: 5px;"></i>
                            请确保所有产品都选到，没选择的产品代理商不会获取分佣
                        </div>
                    </div>
                </div>

                <!-- 汇率返点模块 -->
                <div id="exchangeModule" class="policy-module hidden">
                    <div class="module-header">
                        汇率返点配置
                        <button type="button" class="btn btn-primary btn-sm" onclick="showExchangeConfigModal()" style="float: right;">新增配置</button>
                    </div>
                    <div class="module-content">
                        <div class="dual-selector">
                            <!-- 可选列表 -->
                            <div class="selector-column">
                                <div class="selector-header">可选汇率返点</div>
                                <div class="selector-search">
                                    <input type="text" placeholder="搜索货币对或返点简介" onkeyup="filterExchangeOptions(this.value)">
                                </div>
                                <div class="selector-list" id="exchangeOptions">
                                    <div class="selector-item" data-id="ex1" data-pair="USD/CNY" onclick="selectExchangeItem(this)">
                                        <div class="item-title">A类代理返点 - USD/CNY</div>
                                        <div class="item-desc">标准汇率返点 -0.15%</div>
                                    </div>
                                    <div class="selector-item" data-id="ex2" data-pair="EUR/CNY" onclick="selectExchangeItem(this)">
                                        <div class="item-title">B类代理返点 - EUR/CNY</div>
                                        <div class="item-desc">特殊汇率返点 -0.15%</div>
                                    </div>
                                    <div class="selector-item" data-id="ex3" data-pair="GBP/CNY" onclick="selectExchangeItem(this)">
                                        <div class="item-title">001特殊代理返点 - GBP/CNY</div>
                                        <div class="item-desc">标准汇率返点 -0.12%</div>
                                    </div>
                                    <div class="selector-item" data-id="ex4" data-pair="JPY/CNY" onclick="selectExchangeItem(this)">
                                        <div class="item-title">004特殊代理返点 - JPY/CNY</div>
                                        <div class="item-desc">特殊汇率返点 -0.12%</div>
                                    </div>
                                    <div class="selector-item" data-id="ex5" data-pair="HKD/CNY" onclick="selectExchangeItem(this)">
                                        <div class="item-title">标准固定返点 - HKD/CNY</div>
                                        <div class="item-desc">固定返点比例 50%</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 已选列表 -->
                            <div class="selector-column">
                                <div class="selector-header">已选汇率返点</div>
                                <div class="selector-list" id="selectedExchanges">
                                    <div style="padding: 40px; text-align: center; color: #999;">
                                        暂无选择
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="exchangeError" class="error-message hidden">
                            一个货币对只能选择一个返点配置！
                        </div>
                        <div class="info-message" style="margin-top: 10px; padding: 8px 12px; background-color: #e7f3ff; border-left: 4px solid #007bff; color: #004085; font-size: 14px;">
                            <i class="fas fa-info-circle" style="margin-right: 5px;"></i>
                            请确保所有货币对都选到，没选择的货币对代理商不会获取返点
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button type="button" class="btn btn-secondary btn-large" onclick="goBack()">取消</button>
                <button type="submit" class="btn btn-primary btn-large">保存提交</button>
            </div>
        </form>
    </div>

    <script>
        // 全局变量
        let selectedCommissions = new Map(); // 存储已选择的交易分佣，key为产品名称
        let selectedExchanges = new Map(); // 存储已选择的汇率返点，key为货币对

        // 切换适用对象
        function toggleApplyTarget() {
            const applyTarget = document.querySelector('input[name="applyTarget"]:checked')?.value;
            const agentSelection = document.getElementById('agentSelection');
            const merchantSelection = document.getElementById('merchantSelection');

            if (applyTarget === 'agent') {
                agentSelection.classList.remove('hidden');
                merchantSelection.classList.add('hidden');
            } else if (applyTarget === 'merchant') {
                agentSelection.classList.add('hidden');
                merchantSelection.classList.remove('hidden');
            } else {
                agentSelection.classList.add('hidden');
                merchantSelection.classList.add('hidden');
            }
        }

        // 切换代理商类型
        function toggleAgentType() {
            const agentType = document.querySelector('input[name="agentType"]:checked')?.value;
            const levelSelection = document.getElementById('levelSelection');
            const specificAgentSelection = document.getElementById('specificAgentSelection');

            if (agentType === 'level') {
                levelSelection.classList.remove('hidden');
                specificAgentSelection.classList.add('hidden');
            } else if (agentType === 'specific') {
                levelSelection.classList.add('hidden');
                specificAgentSelection.classList.remove('hidden');
            } else {
                levelSelection.classList.add('hidden');
                specificAgentSelection.classList.add('hidden');
            }
        }

        // 加载代理商信息
        function loadAgentInfo() {
            const agentId = document.getElementById('agentId').value.trim();
            const agentInfo = document.getElementById('agentInfo');

            if (!agentId) {
                agentInfo.classList.add('hidden');
                return;
            }

            // 模拟API调用
            setTimeout(() => {
                // 模拟数据
                const mockData = {
                    'A001': { name: '润泽科技', status: '已生效' },
                    'B002': { name: '智汇通', status: '待审核' },
                    'C003': { name: '创新支付', status: '已生效' }
                };

                const data = mockData[agentId];
                if (data) {
                    document.getElementById('agentName').textContent = data.name;
                    document.getElementById('agentStatus').textContent = data.status;
                    agentInfo.classList.remove('hidden');
                } else {
                    document.getElementById('agentName').textContent = '未找到';
                    document.getElementById('agentStatus').textContent = '-';
                    agentInfo.classList.remove('hidden');
                }
            }, 500);
        }

        // 加载商户信息
        function loadMerchantInfo() {
            const merchantAccount = document.getElementById('merchantAccount').value.trim();
            const merchantInfo = document.getElementById('merchantInfo');

            if (!merchantAccount) {
                merchantInfo.classList.add('hidden');
                return;
            }

            // 模拟API调用
            setTimeout(() => {
                // 模拟数据
                const mockData = {
                    'M001001': { name: '上海电商有限公司', authStatus: '已认证' },
                    'M002001': { name: '北京贸易公司', authStatus: '待认证' },
                    'M003001': { name: '深圳科技企业', authStatus: '已认证' }
                };

                const data = mockData[merchantAccount];
                if (data) {
                    document.getElementById('merchantName').textContent = data.name;
                    document.getElementById('merchantAuthStatus').textContent = data.authStatus;
                    merchantInfo.classList.remove('hidden');
                } else {
                    document.getElementById('merchantName').textContent = '未找到';
                    document.getElementById('merchantAuthStatus').textContent = '-';
                    merchantInfo.classList.remove('hidden');
                }
            }, 500);
        }

        // 切换政策模块显示
        function togglePolicyModule(moduleType) {
            const checkbox = document.querySelector(`input[name="policyType"][value="${moduleType}"]`);
            const module = document.getElementById(`${moduleType}Module`);

            if (checkbox.checked) {
                module.classList.remove('hidden');
            } else {
                module.classList.add('hidden');
                // 清空已选择的项目
                if (moduleType === 'commission') {
                    selectedCommissions.clear();
                    updateSelectedCommissions();
                } else if (moduleType === 'exchange') {
                    selectedExchanges.clear();
                    updateSelectedExchanges();
                }
            }
        }

        // 选择交易分佣项目
        function selectCommissionItem(element) {
            const id = element.dataset.id;
            const product = element.dataset.product;
            const title = element.querySelector('.item-title').textContent;
            const desc = element.querySelector('.item-desc').textContent;

            // 检查是否已经选择了同一产品的其他配置
            if (selectedCommissions.has(product) && selectedCommissions.get(product).id !== id) {
                showCommissionError();
                return;
            }

            hideCommissionError();

            // 添加到已选择列表
            selectedCommissions.set(product, { id, title, desc });
            element.classList.add('selected');

            updateSelectedCommissions();
        }

        // 更新已选择的交易分佣显示
        function updateSelectedCommissions() {
            const container = document.getElementById('selectedCommissions');

            if (selectedCommissions.size === 0) {
                container.innerHTML = '<div style="padding: 40px; text-align: center; color: #999;">暂无选择</div>';
                return;
            }

            let html = '';
            selectedCommissions.forEach((item, product) => {
                html += `
                    <div class="selector-item" style="background: #e6f7ff;">
                        <div class="item-title">${item.title}</div>
                        <div class="item-desc">${item.desc}</div>
                        <button type="button" onclick="removeCommissionItem('${product}')" style="float: right; background: none; border: none; color: #ff4d4f; cursor: pointer;">移除</button>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 移除交易分佣项目
        function removeCommissionItem(product) {
            selectedCommissions.delete(product);

            // 移除选中状态
            const items = document.querySelectorAll('#commissionOptions .selector-item');
            items.forEach(item => {
                if (item.dataset.product === product) {
                    item.classList.remove('selected');
                }
            });

            updateSelectedCommissions();
            hideCommissionError();
        }

        // 选择汇率返点项目
        function selectExchangeItem(element) {
            const id = element.dataset.id;
            const pair = element.dataset.pair;
            const title = element.querySelector('.item-title').textContent;
            const desc = element.querySelector('.item-desc').textContent;

            // 检查是否已经选择了同一货币对的其他配置
            if (selectedExchanges.has(pair) && selectedExchanges.get(pair).id !== id) {
                showExchangeError();
                return;
            }

            hideExchangeError();

            // 添加到已选择列表
            selectedExchanges.set(pair, { id, title, desc });
            element.classList.add('selected');

            updateSelectedExchanges();
        }

        // 更新已选择的汇率返点显示
        function updateSelectedExchanges() {
            const container = document.getElementById('selectedExchanges');

            if (selectedExchanges.size === 0) {
                container.innerHTML = '<div style="padding: 40px; text-align: center; color: #999;">暂无选择</div>';
                return;
            }

            let html = '';
            selectedExchanges.forEach((item, pair) => {
                html += `
                    <div class="selector-item" style="background: #e6f7ff;">
                        <div class="item-title">${item.title}</div>
                        <div class="item-desc">${item.desc}</div>
                        <button type="button" onclick="removeExchangeItem('${pair}')" style="float: right; background: none; border: none; color: #ff4d4f; cursor: pointer;">移除</button>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 移除汇率返点项目
        function removeExchangeItem(pair) {
            selectedExchanges.delete(pair);

            // 移除选中状态
            const items = document.querySelectorAll('#exchangeOptions .selector-item');
            items.forEach(item => {
                if (item.dataset.pair === pair) {
                    item.classList.remove('selected');
                }
            });

            updateSelectedExchanges();
            hideExchangeError();
        }

        // 筛选交易分佣选项
        function filterCommissionOptions(keyword) {
            const items = document.querySelectorAll('#commissionOptions .selector-item');
            items.forEach(item => {
                const title = item.querySelector('.item-title').textContent.toLowerCase();
                const desc = item.querySelector('.item-desc').textContent.toLowerCase();
                const searchText = keyword.toLowerCase();

                if (title.includes(searchText) || desc.includes(searchText)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // 筛选汇率返点选项
        function filterExchangeOptions(keyword) {
            const items = document.querySelectorAll('#exchangeOptions .selector-item');
            items.forEach(item => {
                const title = item.querySelector('.item-title').textContent.toLowerCase();
                const desc = item.querySelector('.item-desc').textContent.toLowerCase();
                const searchText = keyword.toLowerCase();

                if (title.includes(searchText) || desc.includes(searchText)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // 显示/隐藏错误信息
        function showCommissionError() {
            document.getElementById('commissionError').classList.remove('hidden');
        }

        function hideCommissionError() {
            document.getElementById('commissionError').classList.add('hidden');
        }

        function showExchangeError() {
            document.getElementById('exchangeError').classList.remove('hidden');
        }

        function hideExchangeError() {
            document.getElementById('exchangeError').classList.add('hidden');
        }

        // 表单提交
        document.getElementById('policyForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // 验证基本信息
            const policyName = document.getElementById('policyName').value.trim();
            const policyIntro = document.getElementById('policyIntro').value.trim();

            if (!policyName) {
                alert('请输入政策包名称');
                return;
            }

            if (!policyIntro) {
                alert('请输入政策包简介');
                return;
            }

            // 验证适用范围
            const applyTarget = document.querySelector('input[name="applyTarget"]:checked');
            if (!applyTarget) {
                alert('请选择适用对象');
                return;
            }

            if (applyTarget.value === 'agent') {
                const agentType = document.querySelector('input[name="agentType"]:checked');
                if (!agentType) {
                    alert('请选择代理商类型');
                    return;
                }

                if (agentType.value === 'level') {
                    const agentLevels = document.querySelectorAll('input[name="agentLevel"]:checked');
                    if (agentLevels.length === 0) {
                        alert('请选择至少一个代理商等级');
                        return;
                    }
                } else if (agentType.value === 'specific') {
                    const agentId = document.getElementById('agentId').value.trim();
                    if (!agentId) {
                        alert('请输入代理商ID');
                        return;
                    }
                }
            } else if (applyTarget.value === 'merchant') {
                const merchantAccount = document.getElementById('merchantAccount').value.trim();
                if (!merchantAccount) {
                    alert('请输入商户账户号码');
                    return;
                }
            }

            // 验证政策内容
            const policyTypes = document.querySelectorAll('input[name="policyType"]:checked');
            if (policyTypes.length === 0) {
                alert('请选择至少一种政策类型');
                return;
            }

            // 验证是否选择了具体的政策配置
            let hasCommission = false;
            let hasExchange = false;

            policyTypes.forEach(type => {
                if (type.value === 'commission') {
                    hasCommission = true;
                    if (selectedCommissions.size === 0) {
                        alert('请选择至少一个交易分佣配置');
                        return;
                    }
                } else if (type.value === 'exchange') {
                    hasExchange = true;
                    if (selectedExchanges.size === 0) {
                        alert('请选择至少一个汇率返点配置');
                        return;
                    }
                }
            });

            // 收集表单数据
            const formData = {
                basicInfo: {
                    name: policyName,
                    intro: policyIntro,
                    remark: document.getElementById('policyRemark').value.trim()
                },
                applyScope: {
                    target: applyTarget.value,
                    // 根据选择的类型收集相应数据
                },
                policyContent: {
                    commissions: Array.from(selectedCommissions.values()),
                    exchanges: Array.from(selectedExchanges.values())
                }
            };

            console.log('提交的数据:', formData);
            alert('政策包创建成功！');

            // 这里可以调用API提交数据
            // submitPolicyPackage(formData);
        });

        // 返回
        function goBack() {
            if (confirm('确定要离开吗？未保存的数据将丢失。')) {
                window.history.back();
            }
        }
    </script>
</body>
</html>
