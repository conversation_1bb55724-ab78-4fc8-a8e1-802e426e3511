<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PSP代理商管理系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; display: flex; min-height: 100vh; }

        /* 左侧菜单 */
        .sidebar { width: 280px; background: #2c3e50; color: white; position: fixed; height: 100vh; overflow-y: auto; }
        .sidebar-header { padding: 20px; border-bottom: 1px solid #34495e; background: #1a252f; }
        .sidebar-title { font-size: 18px; font-weight: 600; color: #ecf0f1; margin-bottom: 5px; }
        .sidebar-subtitle { font-size: 12px; color: #95a5a6; }
        .nav-section { padding: 20px 0; }
        .nav-main-item { margin-bottom: 5px; }
        .nav-main-link { display: flex; align-items: center; justify-content: space-between; padding: 12px 20px; color: #ecf0f1; text-decoration: none; font-weight: 500; transition: all 0.3s; border-left: 3px solid transparent; }
        .nav-main-link:hover, .nav-main-link.active { background: #34495e; border-left-color: #3498db; color: #3498db; }
        .nav-arrow { font-size: 12px; transition: transform 0.3s; }
        .nav-submenu { max-height: 0; overflow: hidden; transition: max-height 0.3s ease; background: #1a252f; }
        .nav-submenu.active { max-height: 500px; }
        .nav-sub-link { display: block; padding: 10px 20px 10px 40px; color: #bdc3c7; text-decoration: none; font-size: 14px; transition: all 0.3s; border-left: 3px solid transparent; }
        .nav-sub-link:hover, .nav-sub-link.active { background: #34495e; color: #3498db; border-left-color: #3498db; }

        /* 主内容区 */
        .main-content { margin-left: 280px; flex: 1; padding: 20px; min-height: 100vh; }
        .page-content { display: none; }
        .page-content.active { display: block; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; margin-bottom: 20px; }
        .card-header { background: #f8f9fa; padding: 20px; border-bottom: 1px solid #dee2e6; display: flex; justify-content: space-between; align-items: center; }
        .card-title { font-size: 20px; font-weight: 600; color: #333; margin: 0; }
        .card-body { padding: 20px; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; text-decoration: none; display: inline-block; text-align: center; transition: all 0.3s; margin-right: 10px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-success { background: #28a745; color: white; }
        .btn-sm { padding: 6px 12px; font-size: 12px; }
        
        /* 政策包卡片样式 */
        .package-card {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            background: white;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .package-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
        }
        
        .package-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .package-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .package-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .badge-standard {
            background: #d4edda;
            color: #155724;
        }
        
        .badge-special {
            background: #fff3cd;
            color: #856404;
        }
        
        .package-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .fee-structure {
            display: flex;
            gap: 20px;
        }
        
        .fee-column {
            flex: 1;
        }
        
        .fee-column-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            text-align: center;
            padding-bottom: 8px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .fee-items {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .fee-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 12px;
            background: #f8f9fa;
            border-radius: 6px;
            font-size: 13px;
        }
        
        .fee-item-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .fee-item-name {
            color: #333;
            font-weight: 500;
        }
        
        .fee-item-type {
            color: #666;
            font-size: 11px;
        }
        
        .commission-rate {
            color: #007bff;
            font-weight: 600;
            font-size: 14px;
        }
        
        .rebate-rate {
            color: #28a745;
            font-weight: 600;
            font-size: 14px;
        }
        
        .actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }
        
        .btn-outline {
            background: white;
            color: #007bff;
            border: 1px solid #007bff;
        }
        
        .btn-outline:hover {
            background: #007bff;
            color: white;
        }

        /* Tab样式 */
        .tab-container { margin-bottom: 20px; }
        .tab-nav { display: flex; border-bottom: 2px solid #dee2e6; margin-bottom: 20px; }
        .tab-item { padding: 12px 24px; background: #f8f9fa; border: 1px solid #dee2e6; border-bottom: none; cursor: pointer; font-weight: 500; color: #6c757d; transition: all 0.3s; margin-right: 2px; }
        .tab-item.active { background: white; color: #007bff; border-color: #007bff; border-bottom: 2px solid white; margin-bottom: -2px; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }

        /* 搜索区域 */
        .search-section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .search-grid { display: flex; gap: 15px; align-items: end; flex-wrap: wrap; margin-bottom: 15px; }
        .search-item { display: flex; flex-direction: column; min-width: 150px; }
        .search-item label { font-size: 14px; font-weight: 500; color: #333; margin-bottom: 5px; }
        .search-item input, .search-item select { padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; }

        /* 表格样式 */
        .table-container { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .data-table { width: 100%; border-collapse: collapse; table-layout: fixed; }
        .data-table th { background: #f8f9fa; padding: 16px 12px; text-align: left; font-weight: 600; color: #333; border-bottom: 1px solid #dee2e6; font-size: 14px; }
        .data-table td { padding: 16px 12px; border-bottom: 1px solid #eee; font-size: 14px; vertical-align: top; }
        .data-table tr:hover { background: #f8f9fa; }

        /* 操作链接 */
        .action-link { color: #007bff; text-decoration: none; margin-right: 8px; font-size: 13px; transition: color 0.3s; }
        .action-link:hover { color: #0056b3; }
        .action-link.danger { color: #dc3545; }
        .action-link.danger:hover { color: #c82333; }

        /* 分页样式 */
        .pagination-container { display: flex; justify-content: space-between; align-items: center; margin-top: 20px; margin-bottom: 40px; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .pagination-info { color: #666; font-size: 14px; }
        .pagination-controls { display: flex; gap: 5px; }
        .pagination-controls button { padding: 8px 12px; border: 1px solid #ddd; background: white; color: #666; border-radius: 4px; cursor: pointer; transition: all 0.3s; }
        .pagination-controls button:hover { background: #f8f9fa; }
        .pagination-controls button.active { background: #007bff; color: white; border-color: #007bff; }

        /* 多行内容样式 */
        .multi-line-content { font-size: 13px; line-height: 1.6; max-width: 100%; }
        .multi-line-content .line-item { margin-bottom: 2px; }
        .multi-line-content .more-indicator { color: #6c757d; font-size: 12px; }

        /* 状态标签 */
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600; white-space: nowrap; }
        .status-active { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        
        /* 分佣管理和汇率返点专属样式 */
        .commission-section .container,
        .exchange-section .container {
            max-width: 1400px;
            margin: 0;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
        }
        .commission-section .page-header,
        .exchange-section .page-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e8e8e8;
        }
        .commission-section .page-title,
        .exchange-section .page-title {
            font-size: 24px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: 600;
            margin-bottom: 8px;
        }
        .commission-section .page-subtitle,
        .exchange-section .page-subtitle {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
        }
        .commission-section .breadcrumb,
        .exchange-section .breadcrumb {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            margin-bottom: 8px;
        }
        .commission-section .breadcrumb a,
        .exchange-section .breadcrumb a {
            color: #1890ff;
            text-decoration: none;
        }
        
        /* Tab样式重写 */
        .commission-section .tab-container,
        .exchange-section .tab-container {
            margin-bottom: 24px;
        }
        .commission-section .tab-header,
        .exchange-section .tab-header {
            display: flex;
            border-bottom: 1px solid #e8e8e8;
            margin-bottom: 16px;
        }
        .commission-section .tab-item,
        .exchange-section .tab-item {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: rgba(0, 0, 0, 0.65);
            transition: all 0.3s;
        }
        .commission-section .tab-item:hover,
        .exchange-section .tab-item:hover {
            color: #1890ff;
        }
        .commission-section .tab-item.active,
        .exchange-section .tab-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            font-weight: 500;
        }
        .commission-section .tab-content,
        .exchange-section .tab-content {
            display: none;
        }
        .commission-section .tab-content.active,
        .exchange-section .tab-content.active {
            display: block;
        }
        
        /* 操作栏样式 */
        .commission-section .action-bar,
        .exchange-section .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .commission-section .search-box,
        .exchange-section .search-box {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        .commission-section .search-box input,
        .exchange-section .search-box input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            width: 200px;
        }
        .commission-section .btn,
        .exchange-section .btn {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            border: none;
            font-size: 14px;
            transition: all 0.3s;
        }
        .commission-section .btn-primary,
        .exchange-section .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        .commission-section .btn-primary:hover,
        .exchange-section .btn-primary:hover {
            background-color: #40a9ff;
        }
        .commission-section .btn-default,
        .exchange-section .btn-default {
            background-color: #fff;
            border: 1px solid #d9d9d9;
            color: rgba(0, 0, 0, 0.65);
        }
        .commission-section .btn-default:hover,
        .exchange-section .btn-default:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }
        
        /* 表格样式重写 */
        .commission-section .table-container,
        .exchange-section .table-container {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            overflow: hidden;
        }
        .commission-section .table,
        .exchange-section .table {
            width: 100%;
            border-collapse: collapse;
        }
        .commission-section .table th,
        .exchange-section .table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            border-bottom: 1px solid #e8e8e8;
        }
        .commission-section .table td,
        .exchange-section .table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            color: rgba(0, 0, 0, 0.85);
        }
        .commission-section .table tbody tr:hover,
        .exchange-section .table tbody tr:hover {
            background: #f5f5f5;
        }
        .commission-section .table tbody tr:last-child td,
        .exchange-section .table tbody tr:last-child td {
            border-bottom: none;
        }
        
        /* 状态标签重写 */
        .commission-section .status-tag,
        .exchange-section .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .commission-section .status-standard,
        .exchange-section .status-standard {
            background: #e6f7ff;
            color: #1890ff;
        }
        .commission-section .status-special,
        .exchange-section .status-special {
            background: #fff2e8;
            color: #fa8c16;
        }
        
        /* 操作按钮 */
        .commission-section .action-buttons,
        .exchange-section .action-buttons {
            display: flex;
            gap: 8px;
        }
        .commission-section .btn-link,
        .exchange-section .btn-link {
            background: none;
            border: none;
            color: #1890ff;
            cursor: pointer;
            padding: 4px 8px;
            font-size: 12px;
        }
        .commission-section .btn-link:hover,
        .exchange-section .btn-link:hover {
            color: #40a9ff;
        }
        .commission-section .btn-danger,
        .exchange-section .btn-danger {
            color: #ff4d4f;
        }
        .commission-section .btn-danger:hover,
        .exchange-section .btn-danger:hover {
            color: #ff7875;
        }
        
        /* 分佣简介样式 */
        .commission-section .commission-intro,
        .exchange-section .commission-intro {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 14px;
            color: #52c41a;
        }
        
        /* 菜单收起功能样式 */
        .sidebar.collapsed {
            width: 60px;
        }
        
        .sidebar.collapsed .sidebar-title,
        .sidebar.collapsed .sidebar-subtitle {
            display: none;
        }
        
        .sidebar.collapsed .nav-main-link {
            text-align: center;
            padding: 12px 8px;
            font-size: 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .main-content {
            transition: margin-left 0.3s ease;
        }
        
        .main-content.expanded {
            margin-left: 60px;
        }
        
        .toggle-btn {
            position: absolute;
            top: 15px;
            right: -15px;
            width: 30px;
            height: 30px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            z-index: 1001;
            transition: all 0.3s ease;
        }
        
        .toggle-btn:hover {
            background: #0056b3;
        }
        
        .sidebar {
            transition: width 0.3s ease;
            z-index: 1000;
        }
        
        /* 分页按钮样式 */
        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .pagination-btn:hover {
            background: #f5f5f5;
            border-color: #007bff;
        }
        
        .pagination-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .pagination-btn:disabled {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
            border-color: #dee2e6;
        }
        
        .pagination-btn:disabled:hover {
            background: #f8f9fa;
            border-color: #dee2e6;
        }
    </style>
</head>
<body>
    <!-- 左侧菜单 -->
    <div class="sidebar" id="sidebar">
        <button class="toggle-btn" onclick="toggleSidebar()" id="toggleBtn">«</button>
        <div class="sidebar-header">
            <div class="sidebar-title">代理商管理</div>
            <div class="sidebar-subtitle">Agent Management System</div>
        </div>
        
        <nav class="nav-section">
            <div class="nav-main-item">
                <a class="nav-main-link active" href="#" onclick="showPage('agent-list')" id="menu-agent-list">代理商签约信息</a>
            </div>
            <div class="nav-main-item">
                <a class="nav-main-link" href="#" onclick="showPage('commission-config')" id="menu-commission-config">交易分佣配置</a>
            </div>
            <div class="nav-main-item">
                <a class="nav-main-link" href="#" onclick="showPage('exchange-rate-config')" id="menu-exchange-rate-config">汇率返点配置</a>
            </div>
            <div class="nav-main-item">
                <a class="nav-main-link" href="#" onclick="showPage('policy-package')" id="menu-policy-package">代理政策管理</a>
            </div>
            <div class="nav-main-item">
                <a class="nav-main-link" href="#" onclick="showPage('policy-assignment')" id="menu-policy-assignment">政策分配管理</a>
            </div>
        </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content" id="mainContent">
        <!-- 代理商签约信息页面 -->
        <div id="agent-list" class="page-content active">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题 -->
                <div style="margin-bottom: 20px;">
                    <h3 style="font-size: 20px; font-weight: 600; color: #333; margin: 0;">代理商签约信息</h3>
                </div>
                
                <!-- Tab导航 -->
                <div style="margin-bottom: 30px;">
                    <div style="border-bottom: 1px solid #e0e0e0;">
                        <div style="display: flex; gap: 0;">
                            <button class="agent-tab-btn active" onclick="switchAgentTab('signed')" style="padding: 12px 24px; border: none; background: none; font-size: 14px; font-weight: 500; cursor: pointer; border-bottom: 2px solid #007bff; color: #007bff;">已签约</button>
                            <button class="agent-tab-btn" onclick="switchAgentTab('draft')" style="padding: 12px 24px; border: none; background: none; font-size: 14px; font-weight: 500; cursor: pointer; border-bottom: 2px solid transparent; color: #666;">草稿</button>
                        </div>
                    </div>
                </div>

                <!-- 已签约Tab内容 -->
                <div id="signed-content" class="agent-tab-content">
                    <!-- 搜索筛选区域 -->
                    <div style="margin-bottom: 30px;">
                        <!-- 第一行搜索条件 -->
                    <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px; flex-wrap: wrap;">
                        <div style="display: flex; flex-direction: column; min-width: 200px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">代理商名称：</label>
                            <input type="text" placeholder="请输入代理商名称" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 200px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">代理商ID：</label>
                            <input type="text" placeholder="请输入代理商ID" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 200px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">合同编号：</label>
                            <input type="text" placeholder="请输入代理商ID" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">状态：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部状态</option>
                                <option>已激活</option>
                                <option>未激活</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">激活状态：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部状态</option>
                                <option>已激活</option>
                                <option>未激活</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">销售：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部销售</option>
                                <option>张三</option>
                                <option>李四</option>
                            </select>
                        </div>
                    </div>

                    <!-- 第二行搜索条件 -->
                    <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">创建时间：</label>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <input type="date" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <span style="color: #666;">至</span>
                                <input type="date" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                            </div>
                        </div>
                    </div>

                    <!-- 查询按钮 -->
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">查询</button>
                        <button style="background: #6c757d; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">重置</button>
                        <a href="psp-agent-create.html" style="background: #28a745; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer; text-decoration: none; display: inline-block;">新增代理商</a>
                    </div>
                </div>

                <!-- 表格 -->
                <div style="border: 1px solid #e0e0e0; border-radius: 8px; overflow-x: auto; overflow-y: hidden;">
                    <table style="min-width: 1540px; border-collapse: collapse; table-layout: fixed;">
                        <colgroup>
                            <col style="width: 220px;"> <!-- 代理商信息 -->
                            <col style="width: 200px;"> <!-- 管理员信息 -->
                            <col style="width: 180px;"> <!-- 签约信息 -->
                            <col style="width: 180px;"> <!-- 结算信息 -->
                            <col style="width: 180px;"> <!-- 政策信息 -->
                            <col style="width: 120px;"> <!-- 所属销售 -->
                            <col style="width: 180px;"> <!-- 时间 -->
                            <col style="width: 200px;"> <!-- 操作 -->
                        </colgroup>
                        <thead>
                            <tr style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">代理商信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">管理员信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">签约信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">结算信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">激活信息</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">所属销售</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">时间</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">EUREWAX001</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">欧瑞华科技有限公司</div>
                                    <div style="margin-top: 4px; display: flex; align-items: center; gap: 6px;">
                                        <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">企业</span>
                                        <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">A级</span>
                                    </div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">名称：张三</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">邮箱：<EMAIL></div>
                                    <div style="color: #6c757d; font-size: 12px;">手机：+86 138****8888</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">签约截止：2025-01-15</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">合同编号：*********</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">生效中</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">结算币种：USD</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">结汇日：每月20日</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">普通发票</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">POL001</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">A级代理商政策</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">优惠费率</span>
                                </td>
                                <td style="padding: 8px 6px; border-bottom: 1px solid #f1f3f4; font-size: 11px; vertical-align: middle; white-space: nowrap;">
                                    <div style="display: flex; align-items: center; gap: 3px; margin-bottom: 3px;">
                                        <span style="color: #666; font-size: 10px; min-width: 32px;">代理:</span>
                                        <span style="color: #007bff; font-size: 10px; font-family: monospace;">agent.eurewax.com/EUREWAX001</span>
                                        <button onclick="copyToClipboard('https://agent.eurewax.com/EUREWAX001')" style="background: none; border: none; color: #007bff; cursor: pointer; padding: 1px; font-size: 10px;" title="复制链接">📋</button>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 3px; margin-bottom: 3px;">
                                        <span style="color: #666; font-size: 10px; min-width: 32px;">商户:</span>
                                        <span style="color: #28a745; font-size: 10px; font-family: monospace;">register.eurewax.com/EUREWAX001</span>
                                        <button onclick="copyToClipboard('https://register.eurewax.com/EUREWAX001')" style="background: none; border: none; color: #28a745; cursor: pointer; padding: 1px; font-size: 10px;" title="复制链接">📋</button>
                                    </div>
                                    <div>
                                        <span style="display: inline-block; padding: 1px 4px; background: #d4edda; color: #155724; border-radius: 8px; font-size: 9px; font-weight: 600;">已激活</span>
                                    </div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">李销售</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">创建时间：2024-01-01</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">更新时间：2024-01-01</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>

                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">EUREWAX002</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">深圳跨境电商有限公司</div>
                                    <div style="margin-top: 4px; display: flex; align-items: center; gap: 6px;">
                                        <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">企业</span>
                                        <span style="display: inline-block; padding: 2px 6px; background: #fff3e0; color: #f57c00; border-radius: 10px; font-size: 10px; font-weight: 600;">B级</span>
                                    </div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">名称：王五</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">邮箱：<EMAIL></div>
                                    <div style="color: #6c757d; font-size: 12px;">手机：+86 139****9999</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">签约截止：2025-02-20</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">合同编号：*********</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">已过期</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">POL002</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">B级代理商政策</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #fff3e0; color: #f57c00; border-radius: 10px; font-size: 10px; font-weight: 600;">标准费率</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">结算币种：EUR</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">结汇日：每月15日</div>
                                    <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">无发票</span>
                                </td>
                                <td style="padding: 8px 6px; border-bottom: 1px solid #f1f3f4; font-size: 11px; vertical-align: middle; white-space: nowrap;">
                                    <div style="display: flex; align-items: center; gap: 3px; margin-bottom: 3px;">
                                        <span style="color: #666; font-size: 10px; min-width: 32px;">代理:</span>
                                        <span style="color: #007bff; font-size: 10px; font-family: monospace;">agent.eurewax.com/EUREWAX002</span>
                                        <button onclick="copyToClipboard('https://agent.eurewax.com/EUREWAX002')" style="background: none; border: none; color: #007bff; cursor: pointer; padding: 1px; font-size: 10px;" title="复制链接">📋</button>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 3px; margin-bottom: 3px;">
                                        <span style="color: #666; font-size: 10px; min-width: 32px;">商户:</span>
                                        <span style="color: #28a745; font-size: 10px; font-family: monospace;">register.eurewax.com/EUREWAX002</span>
                                        <button onclick="copyToClipboard('https://register.eurewax.com/EUREWAX002')" style="background: none; border: none; color: #28a745; cursor: pointer; padding: 1px; font-size: 10px;" title="复制链接">📋</button>
                                    </div>
                                    <div>
                                        <span style="display: inline-block; padding: 1px 4px; background: #f8d7da; color: #721c24; border-radius: 8px; font-size: 9px; font-weight: 600;">未激活</span>
                                    </div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">赵销售</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">创建时间：2024-01-01</div>
                                    <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">更新时间：2024-01-01</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页控件 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 0 20px;">
                    <div style="color: #666; font-size: 14px;">
                        显示第 <span id="currentStart">1</span>-<span id="currentEnd">3</span> 条，共 <span id="totalRecords">15</span> 条记录
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <button class="pagination-btn" onclick="goToPage(1)" id="firstBtn">
                            <span>首页</span>
                        </button>
                        <button class="pagination-btn" onclick="previousPage()" id="prevBtn">
                            <span>上一页</span>
                        </button>
                        <div style="display: flex; gap: 5px;" id="pageNumbers">
                            <button class="pagination-btn active" onclick="goToPage(1)">1</button>
                            <button class="pagination-btn" onclick="goToPage(2)">2</button>
                            <button class="pagination-btn" onclick="goToPage(3)">3</button>
                            <button class="pagination-btn" onclick="goToPage(4)">4</button>
                            <button class="pagination-btn" onclick="goToPage(5)">5</button>
                        </div>
                        <button class="pagination-btn" onclick="nextPage()" id="nextBtn">
                            <span>下一页</span>
                        </button>
                        <button class="pagination-btn" onclick="goToPage(5)" id="lastBtn">
                            <span>末页</span>
                        </button>
                        <div style="display: flex; align-items: center; gap: 5px; margin-left: 15px;">
                            <span style="color: #666; font-size: 14px;">每页</span>
                            <select style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;" onchange="changePageSize(this.value)">
                                <option value="10">10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span style="color: #666; font-size: 14px;">条</span>
                        </div>
                    </div>
                </div>
                </div>
                
                <!-- 草稿Tab内容 -->
                <div id="draft-content" class="agent-tab-content" style="display: none;">
                    <!-- 搜索筛选区域 -->
                    <div style="margin-bottom: 30px;">
                        <!-- 第一行搜索条件 -->
                        <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px; flex-wrap: wrap;">
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; color: #333; margin-bottom: 8px;">代理商名称：</label>
                                <input type="text" placeholder="请输入代理商名称" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                            </div>
                            <div style="display: flex; flex-direction: column; min-width: 200px;">
                                <label style="font-size: 14px; color: #333; margin-bottom: 8px;">合同编号：</label>
                                <input type="text" placeholder="请输入合同编号" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                            </div>
                            <div style="display: flex; gap: 10px; align-items: end;">
                                <button style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; font-size: 14px; cursor: pointer;">查询</button>
                                <button style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; font-size: 14px; cursor: pointer;">重置</button>
                                <a href="psp-agent-create.html" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; font-size: 14px; cursor: pointer; text-decoration: none; display: inline-block;">新增代理商</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 草稿表格 -->
                    <div style="border: 1px solid #e0e0e0; border-radius: 8px; overflow-x: auto; overflow-y: hidden;">
                        <table style="min-width: 1280px; border-collapse: collapse; table-layout: fixed;">
                            <colgroup>
                                <col style="width: 220px;"> <!-- 代理商信息 -->
                                <col style="width: 200px;"> <!-- 管理员信息 -->
                                <col style="width: 180px;"> <!-- 政策信息 -->
                                <col style="width: 180px;"> <!-- 结算信息 -->
                                <col style="width: 150px;"> <!-- 所属销售 -->
                                <col style="width: 180px;"> <!-- 时间 -->
                                <col style="width: 200px;"> <!-- 操作 -->
                            </colgroup>
                            <thead>
                                <tr style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                    <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">代理商信息</th>
                                    <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">管理员信息</th>
                                    <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">结算信息</th>
                                    <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策信息</th>
                                    <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">所属销售</th>
                                    <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">时间</th>
                                    <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="border-bottom: 1px solid #f1f3f4;">
                                    <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                        <div style="color: #333; font-weight: 500;">EUREWAX001</div>
                                        <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">欧瑞华科技有限公司</div>
                                        <div style="margin-top: 4px; display: flex; align-items: center; gap: 6px;">
                                            <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">企业</span>
                                            <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">A级</span>
                                        </div>
                                    </td>
                                    <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                        <div style="color: #333; font-weight: 500;">名称：张三</div>
                                        <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">邮箱：<EMAIL></div>
                                        <div style="color: #6c757d; font-size: 12px;">手机：+86 138****8888</div>
                                    </td>
                                    <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                        <div style="color: #333; font-size: 12px;">结算币种：USD</div>
                                        <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">结汇日：每月20日</div>
                                        <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">普通发票</span>
                                    </td>
                                    <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                        <div style="color: #333; font-weight: 500;">POL001</div>
                                        <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">A级代理商政策</div>
                                        <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">优惠费率</span>
                                    </td>
                                    <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                        <div style="color: #333; font-weight: 500;">李销售</div>
                                    </td>
                                    <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                        <div style="color: #333; font-size: 12px;">创建时间：2024-01-01</div>
                                        <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">更新时间：2024-01-01</div>
                                    </td>
                                    <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                        <div style="display: flex; gap: 6px;">
                                            <a href="psp-agent-create.html" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">继续编辑</a>
                                            <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                        </div>
                                    </td>
                                </tr>
                                <tr style="border-bottom: 1px solid #f1f3f4;">
                                    <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                        <div style="color: #333; font-weight: 500;">TEST001</div>
                                        <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">测试代理商</div>
                                        <div style="margin-top: 4px; display: flex; align-items: center; gap: 6px;">
                                            <span style="display: inline-block; padding: 2px 6px; background: #fff3cd; color: #856404; border-radius: 10px; font-size: 10px; font-weight: 600;">个人</span>
                                            <span style="display: inline-block; padding: 2px 6px; background: #fff3e0; color: #f57c00; border-radius: 10px; font-size: 10px; font-weight: 600;">B级</span>
                                        </div>
                                    </td>
                                    <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                        <div style="color: #333; font-weight: 500;">名称：王五</div>
                                        <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">邮箱：<EMAIL></div>
                                        <div style="color: #6c757d; font-size: 12px;">手机：+86 139****9999</div>
                                    </td>
                                    <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                        <div style="color: #333; font-size: 12px;">结算币种：EUR</div>
                                        <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">结汇日：每月15日</div>
                                        <span style="display: inline-block; padding: 2px 6px; background: #e3f2fd; color: #1976d2; border-radius: 10px; font-size: 10px; font-weight: 600;">无发票</span>
                                    </td>
                                    <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                        <div style="color: #333; font-weight: 500;">赵销售</div>
                                    </td>
                                    <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                        <div style="color: #333; font-size: 12px;">创建时间：2024-01-01</div>
                                        <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">更新时间：2024-01-01</div>
                                    </td>
                                    <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                        <div style="display: flex; gap: 6px;">
                                            <a href="psp-agent-create.html" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">继续编辑</a>
                                            <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页控件 -->
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 0 20px;">
                        <div style="color: #666; font-size: 14px;">
                            显示第 <span>1</span>-<span>2</span> 条，共 <span>2</span> 条记录
                        </div>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <button class="pagination-btn" disabled>
                                <span>首页</span>
                            </button>
                            <button class="pagination-btn" disabled>
                                <span>上一页</span>
                            </button>
                            <div style="display: flex; gap: 5px;">
                                <button class="pagination-btn active">1</button>
                            </div>
                            <button class="pagination-btn" disabled>
                                <span>下一页</span>
                            </button>
                            <button class="pagination-btn" disabled>
                                <span>末页</span>
                            </button>
                            <div style="display: flex; align-items: center; gap: 5px; margin-left: 15px;">
                                <span style="color: #666; font-size: 14px;">每页</span>
                                <select style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span style="color: #666; font-size: 14px;">条</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 底价分佣配置页面 -->
        <div id="commission-config" class="page-content commission-section" style="display:none;">
            <div class="container">
                <div class="page-header">
                    <div class="breadcrumb">
                        <a href="#">代理商管理</a> / 分佣管理
                    </div>
                    <div class="page-title">分佣管理</div>
                    <div class="page-subtitle">管理代理商的交易分佣配置和规则</div>
                </div>
                
                <div class="tab-container">
                    <div class="tab-header">
                        <div class="tab-item active" onclick="switchTab('commission', 'base-price')">底价分佣</div>
                    </div>
                    
                    <!-- 底价分佣Tab -->
                    <div id="commission-base-price" class="tab-content active">
                        <div class="commission-intro">
                            <strong>底价分佣简介：</strong>代理商可以在系统设定的底价基础上加点，向商户收取更高费用，差价部分作为代理商的分佣收益。适用于代理商有定价自主权的业务场景。
                        </div>
                        
                        <div class="action-bar">
                            <div class="search-box">
                                <input type="text" placeholder="搜索产品名称">
                                <button class="btn btn-default">搜索</button>
                            </div>
                            <button class="btn btn-primary" onclick="window.location.href='basicfee-configue.html'">新增底价分佣</button>
                        </div>
                        
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">分佣简介</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">类型</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">产品名称</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">底价</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">时间</th>
                                        <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">A类代理商底价</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于A级代理商的标准底价政策</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">B2C-电商收款</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">电商平台收款业务</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">2.5% + $0.3</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">百分比 + 固定费用</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-15 12:22</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-15 12:22</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">B类代理商底价</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于B级代理商的特殊底价政策</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #fff3cd; color: #856404; border-radius: 12px; font-size: 11px; font-weight: 600;">特殊</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">B2B-外贸收款</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">外贸企业收款业务</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">2.0% + $0.2</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">比例费 + 固定费</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-14 15:20</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-14 15:20</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">C类代理商底价</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于C级代理商的标准底价政策</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">全球付款</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">全球跨境付款业务</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">3.0% </div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">比例费</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-13 12:22</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-13 12:22</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页控件 -->
                        <div class="pagination-container" style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 12px 0; border-top: 1px solid #f1f3f4;">
                            <div style="color: #6c757d; font-size: 13px;">
                                显示第 1-3 条，共 3 条记录
                            </div>
                            <div class="pagination" style="display: flex; align-items: center; gap: 8px;">
                                <select style="padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 12px; color: #495057;">
                                    <option value="10">10条/页</option>
                                    <option value="20">20条/页</option>
                                    <option value="50">50条/页</option>
                                </select>
                                <button style="padding: 6px 12px; border: 1px solid #ddd; background: #f8f9fa; color: #6c757d; border-radius: 4px; font-size: 12px; cursor: not-allowed;" disabled>上一页</button>
                                <span style="display: flex; gap: 4px;">
                                    <button style="padding: 6px 10px; border: 1px solid #007bff; background: #007bff; color: white; border-radius: 4px; font-size: 12px;">1</button>
                                </span>
                                <button style="padding: 6px 12px; border: 1px solid #ddd; background: #f8f9fa; color: #6c757d; border-radius: 4px; font-size: 12px; cursor: not-allowed;" disabled>下一页</button>
                            </div>
                        </div>
                    </div>
                        
                </div>
            </div>
        </div>
        <!-- 汇率返点配置页面 -->
        <div id="exchange-rate-config" class="page-content commission-section" style="display:none;">
            <div class="container">
                <div class="page-header">
                    <div class="breadcrumb">
                        <a href="#">代理商管理</a> / 汇率返点管理
                    </div>
                    <div class="page-title">汇率返点管理</div>
                    <div class="page-subtitle">管理代理商的汇率加点和返点配置</div>
                </div>
                
                <div class="tab-container">
                    <div class="tab-header">
                        <div class="tab-item" onclick="switchTab('exchange', 'fixed-rebate')">固定返点</div>
                    </div>
                    
                    
                    
                    <!-- 固定返点Tab -->
                    <div id="exchange-fixed-rebate" class="tab-content">
                        <div class="commission-intro">
                            <strong>固定返点简介：</strong>代理商按照预设的固定返点比例从商户交易中获得返点，无加点权。适用于标准化运营和批量管理的业务场景。
                        </div>
                        
                        <div class="action-bar">
                            <div class="search-box">
                                <input type="text" placeholder="搜索货币对">
                                <button class="btn btn-default">搜索</button>
                            </div>
                            <button class="btn btn-primary" onclick="showAddRebateModal()">新增固定返点</button>
                        </div>
                        
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr style="background: #f8f9fa; border-bottom: 2px solid #e9ecef;">
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: left;">返点简介</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: center;">类型</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: left;">货币对</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: left;">返点比例</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: left;">更新时间</th>
                                        <th style="padding: 12px 10px; font-size: 13px; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; text-align: center;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">A类代理返点</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于A级代理商的固定返点</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">USD/CNY</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">美元对人民币</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">0.10%</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">返点比例</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-15 10:30</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-15 10:30</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #f1f3f4;">
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">B类代理返点</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">适用于B级代理商的特殊返点</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                            <span style="display: inline-block; padding: 3px 8px; background: #fff3cd; color: #856404; border-radius: 12px; font-size: 11px; font-weight: 600;">特殊</span>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">EUR/CNY</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">欧元对人民币</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-weight: 500;">0.08%</div>
                                            <div style="color: #6c757d; font-size: 12px; margin-top: 2px;">返点比例</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="color: #333; font-size: 12px;">创建时间：2024-01-14 15:20</div>
                                            <div style="color: #6c757d; font-size: 11px;">更新时间：2024-01-14 15:20</div>
                                        </td>
                                        <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                            <div style="display: flex; gap: 6px;">
                                                <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                                <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                                <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- 分页控件 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 15px 0; border-top: 1px solid #e9ecef;">
                            <div style="color: #6c757d; font-size: 13px;">
                                显示第 1-2 条，共 2 条记录
                            </div>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span style="color: #6c757d; font-size: 13px;">每页显示</span>
                                    <select style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px; background: white;">
                                        <option value="10" selected>10</option>
                                        <option value="20">20</option>
                                        <option value="50">50</option>
                                    </select>
                                    <span style="color: #6c757d; font-size: 13px;">条</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <button style="padding: 6px 12px; border: 1px solid #ddd; background: #f8f9fa; color: #6c757d; border-radius: 4px; font-size: 13px; cursor: not-allowed;" disabled>
                                        上一页
                                    </button>
                                    <button style="padding: 6px 12px; border: 1px solid #007bff; background: #007bff; color: white; border-radius: 4px; font-size: 13px; cursor: pointer;">
                                        1
                                    </button>
                                    <button style="padding: 6px 12px; border: 1px solid #ddd; background: #f8f9fa; color: #6c757d; border-radius: 4px; font-size: 13px; cursor: not-allowed;" disabled>
                                        下一页
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 底价代理政策页面 -->
        <div id="base-price-policy" class="page-content">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题和操作按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <h3 style="font-size: 20px; font-weight: 600; color: #333; margin: 0;">底价代理政策</h3>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">新增政策</button>
                        <button style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">导出</button>
                    </div>
                </div>

                <!-- 搜索筛选区域 -->
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px; flex-wrap: wrap;">
                        <div style="display: flex; flex-direction: column; min-width: 200px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">政策名称：</label>
                            <input type="text" placeholder="请输入政策名称" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">政策类型：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部类型</option>
                                <option>标准政策</option>
                                <option>特殊政策</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">产品名称：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部产品</option>
                                <option>电商收款</option>
                                <option>外贸收款</option>
                                <option>物流收款</option>
                                <option>广告联盟收款</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">查询</button>
                        <button style="background: #6c757d; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">重置</button>
                    </div>
                </div>

                <!-- 表格 -->
                <div style="border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden; background: white;">
                    <table style="width: 100%; border-collapse: collapse; table-layout: fixed;">
                        <colgroup>
                            <col style="width: 200px;"> <!-- 政策名称 -->
                            <col style="width: 250px;"> <!-- 政策简介 -->
                            <col style="width: 120px;"> <!-- 政策类型 -->
                            <col style="width: 150px;"> <!-- 产品名称 -->
                            <col style="width: 300px;"> <!-- 产品底价 -->
                            <col style="width: 150px;"> <!-- 更新时间 -->
                            <col style="width: 120px;"> <!-- 操作 -->
                        </colgroup>
                        <thead>
                            <tr style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策名称</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策简介</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策类型</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">产品名称</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">产品底价</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">更新时间</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">A级电商收款标准政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">适用于A级代理商的电商收款业务标准底价政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">电商收款</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">全球收款→HKD→DBS: 0.20% | 本地收款→HKD→其他: 0.45%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">默认最高2条，微信...</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-15</div>
                                    <div style="color: #6c757d; font-size: 11px;">10:30</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">B级外贸收款标准政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">适用于B级代理商的外贸收款业务标准底价政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">外贸收款</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">全球收款→HKD→DBS: 0.25% | 本地收款→HKD→其他: 0.5%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">默认最高2条，微信...</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-14</div>
                                    <div style="color: #6c757d; font-size: 11px;">09:20</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">上海润泽科技特殊政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">专为上海润泽科技定制的电商收款特殊底价政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #fff3cd; color: #856404; border-radius: 12px; font-size: 11px; font-weight: 600;">特殊政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">电商收款</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">全球收款→HKD→DBS: 0.15% | 本地收款→HKD→其他: 0.35%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">特殊优惠价格</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-10</div>
                                    <div style="color: #6c757d; font-size: 11px;">14:25</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


        <!-- 汇率返点代理政策页面 -->
        <div id="exchange-rebate-policy" class="page-content">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题和操作按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <h3 style="font-size: 20px; font-weight: 600; color: #333; margin: 0;">汇率返点代理政策</h3>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">新增政策</button>
                        <button style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">导出</button>
                    </div>
                </div>

                <!-- 搜索筛选区域 -->
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px; flex-wrap: wrap;">
                        <div style="display: flex; flex-direction: column; min-width: 200px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">政策名称：</label>
                            <input type="text" placeholder="请输入政策名称" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 200px;">
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">政策类型：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部类型</option>
                                <option>标准政策</option>
                                <option>特殊政策</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column; min-width: 150px;">
                            <label style="font-size: 14px; color: #333; margin-bottom: 8px;">货币对：</label>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; width: 150px;">
                                <option>全部货币对</option>
                                <option>USD→CNH</option>
                                <option>EUR→USD</option>
                                <option>GBP→USD</option>
                                <option>USD→HKD</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">查询</button>
                        <button style="background: #6c757d; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">重置</button>
                    </div>
                </div>

                <!-- 表格 -->
                <div style="border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden; background: white;">
                    <table style="width: 100%; border-collapse: collapse; table-layout: fixed;">
                        <colgroup>
                            <col style="width: 200px;"> <!-- 政策名称 -->
                            <col style="width: 250px;"> <!-- 政策简介 -->
                            <col style="width: 120px;"> <!-- 政策类型 -->
                            <col style="width: 300px;"> <!-- 汇率返点 -->
                            <col style="width: 150px;"> <!-- 更新时间 -->
                            <col style="width: 120px;"> <!-- 操作 -->
                        </colgroup>
                        <thead>
                            <tr style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策名称</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策简介</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">政策类型</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">汇率返点</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">更新时间</th>
                                <th style="padding: 12px 10px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #dee2e6; font-size: 13px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">A级标准汇率返点政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">适用于A级代理商的标准汇率返点政策，返点比例50%</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">USD→CNH: 50% | EUR→USD: 50% | GBP→USD: 50%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">标准返点比例</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-15</div>
                                    <div style="color: #6c757d; font-size: 11px;">10:30</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>

                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">B级标准汇率返点政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">适用于B级代理商的标准汇率返点政策，返点比例40%</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 12px; font-size: 11px; font-weight: 600;">标准政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">USD→CNH: 40% | EUR→USD: 40% | GBP→USD: 40%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">标准返点比例</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-14</div>
                                    <div style="color: #6c757d; font-size: 11px;">09:20</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #f1f3f4;">
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-weight: 500;">上海润泽科技特殊返点政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">专为上海润泽科技定制的USD→CNH特殊汇率返点政策</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle; text-align: center;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #fff3cd; color: #856404; border-radius: 12px; font-size: 11px; font-weight: 600;">特殊政策</span>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px; line-height: 1.4;">USD→CNH: 65% | EUR→USD: 55%</div>
                                    <div style="color: #6c757d; font-size: 11px; margin-top: 2px;">特殊优惠返点</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="color: #333; font-size: 12px;">2025-03-10</div>
                                    <div style="color: #6c757d; font-size: 11px;">14:25</div>
                                </td>
                                <td style="padding: 12px 10px; border-bottom: 1px solid #f1f3f4; font-size: 13px; vertical-align: middle;">
                                    <div style="display: flex; gap: 6px;">
                                        <a href="#" style="color: #007bff; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #007bff; background: #f8f9fa; margin-right: 4px;">详情</a>
                                        <a href="#" style="color: #28a745; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #28a745; background: #f8f9fa; margin-right: 4px;">编辑</a>
                                        <a href="#" style="color: #dc3545; text-decoration: none; font-size: 12px; padding: 4px 8px; border-radius: 3px; border: 1px solid #dc3545; background: #f8f9fa;">删除</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>



        <!-- 混合代理政策页面 -->
        <div id="hybrid-policy" class="page-content">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题和操作按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <h3 style="font-size: 20px; font-weight: 600; color: #333; margin: 0;">混合代理政策</h3>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">新增政策</button>
                        <button style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 14px; cursor: pointer;">导出</button>
                    </div>
                </div>
                <div style="text-align: center; padding: 40px; color: #666;">
                    <div style="font-size: 16px; margin-bottom: 10px;">混合代理政策</div>
                    <div style="font-size: 14px;">同时使用底价和汇率返点的混合政策模式</div>
                </div>
            </div>
        </div>

        <!-- 代理政策管理页面 -->
        <div id="policy-package" class="page-content" style="display:none;">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题 -->
                <div style="margin-bottom: 30px;">
                    <h3 style="font-size: 24px; font-weight: 600; color: #333; margin: 0 0 10px 0;">代理商政策包管理</h3>
                    <p style="color: #666; margin: 0 0 15px 0; font-size: 14px;">管理代理商政策包，支持标准政策包和特殊政策包</p>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer; transition: all 0.3s;" onclick="createPackage()">新增政策包</button>
                    </div>
                </div>
                
                <!-- 政策包管理内容 -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <!-- A级代理商政策包 -->
                    <div style="border: 2px solid #e9ecef; border-radius: 12px; padding: 20px; transition: all 0.3s; cursor: pointer;" onclick="selectPackage('a-level')" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 4px 12px rgba(0,123,255,0.15)';" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none';">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div style="font-size: 18px; font-weight: 600; color: #333;">A级代理商政策包</div>
                            <span style="padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; background: #d4edda; color: #155724;">标准政策</span>
                        </div>
                        <div style="color: #666; font-size: 14px; margin-bottom: 15px; line-height: 1.5;">适用于A级代理商及其所有商户，费率相对较低，适合大型代理商</div>
                        <div style="display: flex; gap: 15px;">
                            <div style="flex: 1;">
                                <div style="font-size: 14px; font-weight: 600; color: #333; margin-bottom: 8px; text-align: center;">交易分佣</div>
                                <div style="display: flex; flex-direction: column; gap: 6px;">
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">外贸收款</span>
                                            <span style="color: #666; font-size: 11px;">底价分佣</span>
                                        </div>
                                        <span style="color: #007bff; font-weight: 600;">0.8%</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">跨境电商</span>
                                            <span style="color: #666; font-size: 11px;">底价分佣</span>
                                        </div>
                                        <span style="color: #007bff; font-weight: 600;">0.9%</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">数字货币</span>
                                            <span style="color: #666; font-size: 11px;">固定比例</span>
                                        </div>
                                        <span style="color: #007bff; font-weight: 600;">1.5%</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">外汇交易</span>
                                            <span style="color: #666; font-size: 11px;">底价分佣</span>
                                        </div>
                                        <span style="color: #007bff; font-weight: 600;">0.7%</span>
                                    </div>
                                </div>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: 14px; font-weight: 600; color: #333; margin-bottom: 8px; text-align: center;">汇率返点</div>
                                <div style="display: flex; flex-direction: column; gap: 6px;">
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">USD/CNY</span>
                                            <span style="color: #666; font-size: 11px;">底价汇率</span>
                                        </div>
                                        <span style="color: #28a745; font-weight: 600;">+0.05</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">EUR/CNY</span>
                                            <span style="color: #666; font-size: 11px;">底价汇率</span>
                                        </div>
                                        <span style="color: #28a745; font-weight: 600;">+0.08</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">GBP/CNY</span>
                                            <span style="color: #666; font-size: 11px;">固定返点</span>
                                        </div>
                                        <span style="color: #28a745; font-weight: 600;">0.15%</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                                        <div style="display: flex; flex-direction: column;">
                                            <span style="color: #333; font-weight: 500;">JPY/CNY</span>
                                            <span style="color: #666; font-size: 11px;">底价汇率</span>
                                        </div>
                                        <span style="color: #28a745; font-weight: 600;">+0.03</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div style="display: flex; gap: 10px; margin-top: 20px;">
    <button style="background: white; color: #007bff; border: 1px solid #007bff; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">编辑</button>
    <button style="background: white; color: #007bff; border: 1px solid #007bff; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">复制</button>
    <button style="background: #f5faff; color: #007bff; border: 1px solid #007bff; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;" onclick="window.location.href='package-detail.html?packageId=a-level'">详情</button>
</div>
                    </div>
                    
                    <!-- 新增政策包 -->
                    <div style="border: 2px dashed #dee2e6; border-radius: 12px; padding: 40px; text-align: center; color: #6c757d; cursor: pointer; transition: all 0.3s;" onclick="createPackage()" onmouseover="this.style.borderColor='#007bff'; this.style.color='#007bff';" onmouseout="this.style.borderColor='#dee2e6'; this.style.color='#6c757d';">
                        <div style="font-size: 48px; margin-bottom: 15px;">+</div>
                        <div>新增政策包</div>
                        <div style="font-size: 12px; margin-top: 5px;">创建新的标准或特殊政策包</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 政策分配管理页面 -->
        <div id="policy-assignment" class="page-content" style="display:none;">
            <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- 页面标题 -->
                <div style="margin-bottom: 30px;">
                    <h3 style="font-size: 24px; font-weight: 600; color: #333; margin: 0 0 10px 0;">政策分配管理</h3>
                    <p style="color: #666; margin: 0 0 15px 0; font-size: 14px;">管理代理商政策包分配，支持代理商和特定商户分配</p>
                    <div style="display: flex; gap: 10px;">
                        <button style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer; transition: all 0.3s;" onclick="createAssignment()">政策分配</button>
                    </div>
                </div>
                
                <!-- 搜索筛选区域 -->
                <div style="background: #f8f9fa; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                    <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                        <div style="min-width: 200px;">
                            <label style="display: block; font-size: 13px; color: #495057; margin-bottom: 5px;">政策编号</label>
                            <input id="assignment-policy-filter" type="text" placeholder="请输入政策编号" style="width: 100%; padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                        </div>
                        <div style="min-width: 200px;">
                            <label style="display: block; font-size: 13px; color: #495057; margin-bottom: 5px;">分配对象</label>
                            <select id="assignment-object-filter" style="width: 100%; padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                                <option value="">全部</option>
                                <option value="agent">代理商</option>
                                <option value="merchant">特定商户</option>
                            </select>
                        </div>
                        <div style="min-width: 200px;">
                            <label style="display: block; font-size: 13px; color: #495057; margin-bottom: 5px;">状态</label>
                            <select id="assignment-status-filter" style="width: 100%; padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                                <option value="">全部</option>
                                <option value="active">可用</option>
                                <option value="inactive">不可用</option>
                            </select>
                        </div>
                        <div style="min-width: 200px; display: flex; align-items: flex-end;">
                            <button onclick="filterAssignments()" style="background: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">搜索</button>
                            <button onclick="resetAssignmentFilter()" style="background: white; color: #6c757d; border: 1px solid #6c757d; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer; margin-left: 10px;">重置</button>
                        </div>
                    </div>
                </div>
                
                <!-- 列表内容 -->
                <div style="overflow-x: auto;">
                    <table id="assignment-table" style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 12px 15px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; font-size: 14px;">政策编号</th>
                                <th style="padding: 12px 15px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; font-size: 14px;">政策简介</th>
                                <th style="padding: 12px 15px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; font-size: 14px;">分配对象</th>
                                <th style="padding: 12px 15px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; font-size: 14px;">对象信息</th>
                                <th style="padding: 12px 15px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; font-size: 14px;">分配时间</th>
                                <th style="padding: 12px 15px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; font-size: 14px;">状态</th>
                                <th style="padding: 12px 15px; text-align: left; font-weight: 600; color: #495057; border-bottom: 2px solid #e9ecef; font-size: 14px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 代理商分配示例 -->
                            <tr>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">POL-A001</td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">A级代理商政策包</td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">代理商</td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">
                                    <div>代理商ID: AGENT001</div>
                                    <div style="color: #6c757d; font-size: 12px;">欧瑞华科技代理商</div>
                                </td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">2025-07-15 12:22</td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 4px; font-size: 12px;">可用</span>
                                </td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">
                                    <button style="background: #f5faff; color: #007bff; border: 1px solid #007bff; padding: 4px 10px; border-radius: 4px; font-size: 12px; cursor: pointer; margin-right: 5px;">详情</button>
                                    <button onclick="openChangePolicyModal(this)" style="background: white; color: #6c757d; border: 1px solid #6c757d; padding: 4px 10px; border-radius: 4px; font-size: 12px; cursor: pointer;">更换政策</button>
                                </td>
                            </tr>
                            <!-- 特定商户分配示例 -->
                            <tr>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">POL-B002</td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">B级特殊政策包</td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">特定商户</td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">
                                    <div>商户账号: M202503001</div>
                                    <div style="color: #6c757d; font-size: 12px;">上海润渠科技有限公司</div>
                                    <div style="margin-top: 4px;">代理商ID: AGENT001</div>
                                    <div style="color: #6c757d; font-size: 12px;">欧瑞华科技代理商</div>
                                </td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">2025-07-20 13:44</td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #d4edda; color: #155724; border-radius: 4px; font-size: 12px;">可用</span>
                                </td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">
                                    <button style="background: #f5faff; color: #007bff; border: 1px solid #007bff; padding: 4px 10px; border-radius: 4px; font-size: 12px; cursor: pointer; margin-right: 5px;">详情</button>
                                    <button onclick="openChangePolicyModal(this)" style="background: white; color: #6c757d; border: 1px solid #6c757d; padding: 4px 10px; border-radius: 4px; font-size: 12px; cursor: pointer;">更换政策</button>
                                </td>
                            </tr>
                            <!-- 不可用状态示例 -->
                            <tr>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">POL-C003</td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">C级代理商政策包</td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">代理商</td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">
                                    <div>代理商ID: AGENT002</div>
                                    <div style="color: #6c757d; font-size: 12px;">北京智汇通科技代理商</div>
                                </td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">2025-06-10 12:22</td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">
                                    <span style="display: inline-block; padding: 3px 8px; background: #f8d7da; color: #721c24; border-radius: 4px; font-size: 12px;">不可用</span>
                                </td>
                                <td style="padding: 12px 15px; border-bottom: 1px solid #e9ecef; font-size: 14px;">
                                    <button style="background: #f5faff; color: #007bff; border: 1px solid #007bff; padding: 4px 10px; border-radius: 4px; font-size: 12px; cursor: pointer; margin-right: 5px;">详情</button>
                                    <button onclick="openChangePolicyModal(this)" style="background: white; color: #6c757d; border: 1px solid #6c757d; padding: 4px 10px; border-radius: 4px; font-size: 12px; cursor: pointer;">更换政策</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页控件 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px;">
                    <div style="color: #6c757d; font-size: 14px;">共 <span style="font-weight: 600;">10</span> 条记录</div>
                    <div style="display: flex; gap: 5px;">
                        <button style="background: white; color: #6c757d; border: 1px solid #dee2e6; padding: 5px 10px; border-radius: 4px; font-size: 14px; cursor: pointer;">上一页</button>
                        <button style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 14px; cursor: pointer;">1</button>
                        <button style="background: white; color: #6c757d; border: 1px solid #dee2e6; padding: 5px 10px; border-radius: 4px; font-size: 14px; cursor: pointer;">2</button>
                        <button style="background: white; color: #6c757d; border: 1px solid #dee2e6; padding: 5px 10px; border-radius: 4px; font-size: 14px; cursor: pointer;">下一页</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
    
    <!-- 政策分配弹窗 -->
    <div id="assignment-modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);">
        <div style="background-color: #fefefe; margin: 5% auto; padding: 20px; border: 1px solid #888; width: 80%; max-width: 800px; border-radius: 5px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h4 style="margin: 0;">政策分配</h4>
                <span style="cursor: pointer; font-size: 20px;" onclick="closeAssignmentModal()">&times;</span>
            </div>
            
            <!-- 步骤导航 -->
            <div style="display: flex; margin-bottom: 20px;">
                <div id="step1" style="flex: 1; padding: 10px; text-align: center; background-color: #e9f5ff; color: #007bff; font-weight: 600; border-radius: 4px 0 0 4px; cursor: pointer;" onclick="goToStep(1)">
                    1. 选择范围
                </div>
                <div id="step2" style="flex: 1; padding: 10px; text-align: center; background-color: #f8f9fa; color: #6c757d; border-radius: 0; cursor: pointer;" onclick="goToStep(2)">
                    2. 选择政策包
                </div>
                <div id="step3" style="flex: 1; padding: 10px; text-align: center; background-color: #f8f9fa; color: #6c757d; border-radius: 0 4px 4px 0; cursor: pointer;" onclick="goToStep(3)">
                    3. 确认信息
                </div>
            </div>
            
            <!-- 步骤1内容 -->
            <div id="step1-content" style="display: block;">
                    <div style="margin-bottom: 25px;">
                        <h4 style="margin-bottom: 15px; color: #333;">选择适用范围</h4>
                        <div style="display: flex; gap: 15px;">
                            <div class="scope-option" onclick="selectScope('agent')" style="flex: 1; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; cursor: pointer; transition: all 0.3s;">
                                <div style="font-weight: 600; margin-bottom: 10px; color: #333;">代理商</div>
                                <div style="color: #6c757d; font-size: 14px;">所选代理商的所有商户交易均享受该政策</div>
                            </div>
                            <div class="scope-option" onclick="selectScope('merchant')" style="flex: 1; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; cursor: pointer; transition: all 0.3s;">
                                <div style="font-weight: 600; margin-bottom: 10px; color: #333;">代理商特定商户</div>
                                <div style="color: #6c757d; font-size: 14px;">选定的商户的交易，代理商享受当前配置的政策，其他商户交易不享受该政策</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 代理商选择区域（默认隐藏） -->
                    <div id="agent-selection" style="display: none; margin-top: 20px;">
                        <h4 style="margin-bottom: 15px; color: #333;">选择代理商</h4>
                        <div style="margin-bottom: 15px;">
                            <input type="text" placeholder="输入代理商ID或名称搜索" style="width: 100%; padding: 10px; border: 1px solid #ced4da; border-radius: 4px;">
                        </div>
                        <div style="max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 4px;">
                            <div class="agent-item" onclick="selectAgent('AGENT001')" style="padding: 12px; border-bottom: 1px solid #dee2e6; cursor: pointer; transition: all 0.3s;">
                                <div style="font-weight: 600;">欧瑞华科技代理商</div>
                                <div style="color: #6c757d; font-size: 14px;">代理商ID: AGENT001</div>
                            </div>
                            <div class="agent-item" onclick="selectAgent('AGENT002')" style="padding: 12px; border-bottom: 1px solid #dee2e6; cursor: pointer; transition: all 0.3s;">
                                <div style="font-weight: 600;">北京智汇通科技代理商</div>
                                <div style="color: #6c757d; font-size: 14px;">代理商ID: AGENT002</div>
                            </div>
                            <div class="agent-item" onclick="selectAgent('AGENT003')" style="padding: 12px; cursor: pointer; transition: all 0.3s;">
                                <div style="font-weight: 600;">广州数字科技代理商</div>
                                <div style="color: #6c757d; font-size: 14px;">代理商ID: AGENT003</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 商户选择区域（默认隐藏） -->
                    <div id="merchant-selection" style="display: none; margin-top: 20px;">
                        <h4 style="margin-bottom: 15px; color: #333;">选择代理商</h4>
                        <div style="margin-bottom: 15px;">
                            <select id="merchant-agent-select" style="width: 100%; padding: 10px; border: 1px solid #ced4da; border-radius: 4px;" onchange="selectMerchantAgent(this.value)">
                                <option value="">请选择代理商</option>
                                <option value="AGENT001">欧瑞华科技代理商 (AGENT001)</option>
                                <option value="AGENT002">北京智汇通科技代理商 (AGENT002)</option>
                                <option value="AGENT003">广州数字科技代理商 (AGENT003)</option>
                            </select>
                        </div>
                        
                        <h4 style="margin-bottom: 15px; color: #333;">选择商户</h4>
                        <div style="margin-bottom: 15px;">
                            <input type="text" placeholder="输入商户账号或名称搜索" style="width: 100%; padding: 10px; border: 1px solid #ced4da; border-radius: 4px;">
                        </div>
                        <div style="max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 4px;">
                            <div class="merchant-item" onclick="selectMerchant('M202503001')" style="padding: 12px; border-bottom: 1px solid #dee2e6; cursor: pointer; transition: all 0.3s;">
                                <div style="font-weight: 600;">上海润渠科技有限公司</div>
                                <div style="color: #6c757d; font-size: 14px;">商户账号: M202503001</div>
                            </div>
                            <div class="merchant-item" onclick="selectMerchant('M202503002')" style="padding: 12px; border-bottom: 1px solid #dee2e6; cursor: pointer; transition: all 0.3s;">
                                <div style="font-weight: 600;">深圳市数字贸易有限公司</div>
                                <div style="color: #6c757d; font-size: 14px;">商户账号: M202503002</div>
                            </div>
                            <div class="merchant-item" onclick="selectMerchant('M202503003')" style="padding: 12px; cursor: pointer; transition: all 0.3s;">
                                <div style="font-weight: 600;">杭州云商贸易有限公司</div>
                                <div style="color: #6c757d; font-size: 14px;">商户账号: M202503003</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px; display: flex; justify-content: space-between;">
                        <button onclick="closeAssignmentModal()" style="padding: 10px 20px; background: white; color: #6c757d; border: 1px solid #6c757d; border-radius: 4px; cursor: pointer;">取消</button>
                        <button onclick="goToStep(2)" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">下一步</button>
                    </div>
                </div>
                
                <!-- 步骤2内容 -->
                <div id="step2-content" style="display: none;">
                    <div style="margin-bottom: 25px;">
                        <h4 style="margin-bottom: 15px; color: #333;">选择政策包</h4>
                        
                        <!-- 筛选区域 -->
                        <div style="background: #f8f9fa; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                            <div style="display: flex; gap: 15px; margin-bottom: 10px;">
                                <div style="flex: 1;">
                                    <label style="display: block; font-size: 13px; color: #495057; margin-bottom: 5px;">政策编号</label>
                                    <input id="policy-number-filter" type="text" placeholder="请输入政策编号" style="width: 100%; padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                                </div>
                                <div style="flex: 1;">
                                    <label style="display: block; font-size: 13px; color: #495057; margin-bottom: 5px;">政策简介</label>
                                    <input id="policy-desc-filter" type="text" placeholder="请输入政策简介关键词" style="width: 100%; padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                                </div>
                            </div>
                            <div style="display: flex; justify-content: flex-end;">
                                <button onclick="filterPolicyPackages()" style="background: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">筛选</button>
                                <button onclick="resetPolicyFilter()" style="background: white; color: #6c757d; border: 1px solid #6c757d; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer; margin-left: 10px;">重置</button>
                            </div>
                        </div>
                        
                        <!-- 政策包列表 -->
                        <div style="max-height: 300px; overflow-y: auto;">
                            <!-- 政策包选项 -->
                            <div class="policy-package" onclick="selectPackage('POL-A001')" style="border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 15px; cursor: pointer; transition: all 0.3s;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                    <div style="font-weight: 600; color: #333;">政策编号: POL-A001</div>
                                    <div style="color: #28a745;">可用</div>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <div style="font-weight: 600; color: #333;">政策简介: A级代理商政策包</div>
                                </div>
                                <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                                    <div style="background: #e9f5ff; color: #007bff; padding: 4px 8px; border-radius: 4px; font-size: 12px;">交易分佣: 0.5%</div>
                                    <div style="background: #e8f5e9; color: #28a745; padding: 4px 8px; border-radius: 4px; font-size: 12px;">汇率返点: +0.03</div>
                                </div>
                            </div>
                            
                            <div class="policy-package" onclick="selectPackage('POL-B002')" style="border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 15px; cursor: pointer; transition: all 0.3s;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                    <div style="font-weight: 600; color: #333;">政策编号: POL-B002</div>
                                    <div style="color: #28a745;">可用</div>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <div style="font-weight: 600; color: #333;">政策简介: B级特殊政策包</div>
                                </div>
                                <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                                    <div style="background: #e9f5ff; color: #007bff; padding: 4px 8px; border-radius: 4px; font-size: 12px;">交易分佣: 0.3%</div>
                                    <div style="background: #e8f5e9; color: #28a745; padding: 4px 8px; border-radius: 4px; font-size: 12px;">汇率返点: +0.02</div>
                                </div>
                            </div>
                            
                            <div class="policy-package" onclick="selectPackage('POL-C003')" style="border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; cursor: pointer; transition: all 0.3s;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                    <div style="font-weight: 600; color: #333;">政策编号: POL-C003</div>
                                    <div style="color: #28a745;">可用</div>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <div style="font-weight: 600; color: #333;">政策简介: C级代理商政策包</div>
                                </div>
                                <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                                    <div style="background: #e9f5ff; color: #007bff; padding: 4px 8px; border-radius: 4px; font-size: 12px;">交易分佣: 0.2%</div>
                                    <div style="background: #e8f5e9; color: #28a745; padding: 4px 8px; border-radius: 4px; font-size: 12px;">汇率返点: +0.01</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px; display: flex; justify-content: space-between;">
                        <button onclick="goToStep(1)" style="padding: 10px 20px; background: white; color: #6c757d; border: 1px solid #6c757d; border-radius: 4px; cursor: pointer;">上一步</button>
                        <button onclick="goToStep(3)" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">下一步</button>
                    </div>
                </div>
                
                <!-- 步骤3内容 -->
                <div id="step3-content" style="display: none;">
                    <div style="margin-bottom: 25px;">
                        <h4 style="margin-bottom: 15px; color: #333;">确认政策分配信息</h4>
                        
                        <div style="background: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                            <div style="margin-bottom: 15px;">
                                <div style="font-weight: 600; margin-bottom: 5px;">适用范围</div>
                                <div id="confirm-scope" style="color: #495057;"></div>
                            </div>
                            
                            <div style="margin-bottom: 15px;">
                                <div style="font-weight: 600; margin-bottom: 5px;">对象信息</div>
                                <div id="confirm-target" style="color: #495057;"></div>
                            </div>
                            
                            <div>
                                <div style="font-weight: 600; margin-bottom: 5px;">政策包信息</div>
                                <div id="confirm-package" style="color: #495057;"></div>
                            </div>
                        </div>
                        
                        <div class="alert" style="background-color: #fff3cd; color: #856404; padding: 12px; border-radius: 4px; margin-bottom: 20px;">
                            <div style="font-weight: 600; margin-bottom: 5px;">注意事项</div>
                            <div style="font-size: 14px;">政策分配生效后，将影响代理商的分佣和返点结算。请确认信息无误后再提交。</div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px; display: flex; justify-content: space-between;">
                        <button onclick="goToStep(2)" style="padding: 10px 20px; background: white; color: #6c757d; border: 1px solid #6c757d; border-radius: 4px; cursor: pointer;">上一步</button>
                        <button onclick="saveAssignment()" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面切换功能
        function showPage(pageId) {
            console.log('Switching to page:', pageId);
            
            // 隐藏所有页面
            var pages = document.querySelectorAll('.page-content');
            pages.forEach(function(page) {
                page.classList.remove('active');
                page.style.display = 'none';
            });
            
            // 显示目标页面
            var targetPage = document.getElementById(pageId);
            if(targetPage) {
                targetPage.classList.add('active');
                targetPage.style.display = 'block';
                console.log('Page shown:', pageId);
            } else {
                console.error('Page not found:', pageId);
            }
            
            // 菜单高亮处理 - 移除所有活跃状态
            var allMenuLinks = document.querySelectorAll('.nav-main-link');
            allMenuLinks.forEach(function(link) {
                link.classList.remove('active');
            });
            
            // 设置当前菜单为活跃状态
            var menuMapping = {
                'agent-list': 'menu-agent-list',
                'commission-config': 'menu-commission-config',
                'exchange-rate-config': 'menu-exchange-rate-config',
                'policy-package': 'menu-policy-package',
                'policy-assignment': 'menu-policy-assignment'
            };
            
            var activeMenuId = menuMapping[pageId];
            if(activeMenuId) {
                var activeMenu = document.getElementById(activeMenuId);
                if(activeMenu) {
                    activeMenu.classList.add('active');
                    console.log('Menu activated:', activeMenuId);
                }
            }
        }

        // 政策分配管理相关函数
        let selectedScope = '';
        let selectedAgentId = '';
        let selectedAgentName = '';
        let selectedMerchantId = '';
        let selectedMerchantName = '';
        let selectedPackageId = '';
        let selectedPackageName = '';
        
        function createAssignment() {
            // 显示政策分配弹窗
            document.getElementById('assignment-modal').style.display = 'block';
            // 重置所有选择和步骤
            resetAssignmentModal();
        }
        
        function closeAssignmentModal() {
            document.getElementById('assignment-modal').style.display = 'none';
        }
        
        function resetAssignmentModal() {
            // 重置所有选择
            selectedScope = '';
            selectedAgentId = '';
            selectedAgentName = '';
            selectedMerchantId = '';
            selectedMerchantName = '';
            selectedPackageId = '';
            selectedPackageName = '';
            
            // 重置所有步骤状态
            document.getElementById('step1').style.background = '#e9f5ff';
            document.getElementById('step1').style.color = '#007bff';
            document.getElementById('step1').style.fontWeight = '600';
            document.getElementById('step2').style.background = '#f8f9fa';
            document.getElementById('step2').style.color = '#6c757d';
            document.getElementById('step2').style.fontWeight = 'normal';
            document.getElementById('step3').style.background = '#f8f9fa';
            document.getElementById('step3').style.color = '#6c757d';
            document.getElementById('step3').style.fontWeight = 'normal';
            
            // 显示第一步，隐藏其他步骤
            document.getElementById('step1-content').style.display = 'block';
            document.getElementById('step2-content').style.display = 'none';
            document.getElementById('step3-content').style.display = 'none';
            
            // 隐藏选择区域
            document.getElementById('agent-selection').style.display = 'none';
            document.getElementById('merchant-selection').style.display = 'none';
            
            // 重置选中样式
            const scopeOptions = document.querySelectorAll('.scope-option');
            scopeOptions.forEach(option => {
                option.style.border = '1px solid #dee2e6';
                option.style.background = 'white';
            });
            
            const policyPackages = document.querySelectorAll('.policy-package');
            policyPackages.forEach(pkg => {
                pkg.style.border = '1px solid #dee2e6';
                pkg.style.background = 'white';
            });
        }
        
        function selectScope(scope) {
            selectedScope = scope;
            
            // 重置选中样式
            const scopeOptions = document.querySelectorAll('.scope-option');
            scopeOptions.forEach(option => {
                option.style.border = '1px solid #dee2e6';
                option.style.background = 'white';
            });
            
            // 设置选中项样式
            event.currentTarget.style.border = '2px solid #007bff';
            event.currentTarget.style.background = '#f5faff';
            
            // 隐藏所有选择区域
            document.getElementById('agent-selection').style.display = 'none';
            document.getElementById('merchant-selection').style.display = 'none';
            
            // 显示对应选择区域
            if (scope === 'agent') {
                document.getElementById('agent-selection').style.display = 'block';
            } else if (scope === 'merchant') {
                document.getElementById('merchant-selection').style.display = 'block';
            }
        }
        
        function selectAgent(agentId) {
            // 设置选中代理商
            selectedAgentId = agentId;
            selectedAgentName = event.currentTarget.querySelector('div:first-child').innerText;
            
            // 重置选中样式
            const agentItems = document.querySelectorAll('.agent-item');
            agentItems.forEach(item => {
                item.style.background = 'white';
            });
            
            // 设置选中项样式
            event.currentTarget.style.background = '#f5faff';
        }
        
        function selectMerchant(merchantId) {
            // 设置选中商户
            selectedMerchantId = merchantId;
            selectedMerchantName = event.currentTarget.querySelector('div:first-child').innerText;
            
            // 重置选中样式
            const merchantItems = document.querySelectorAll('.merchant-item');
            merchantItems.forEach(item => {
                item.style.background = 'white';
            });
            
            // 设置选中项样式
            event.currentTarget.style.background = '#f5faff';
        }
        
        function selectMerchantAgent(agentId) {
            // 设置选中代理商
            if (!agentId) return;
            
            selectedAgentId = agentId;
            const select = document.getElementById('merchant-agent-select');
            const selectedOption = select.options[select.selectedIndex];
            selectedAgentName = selectedOption.text.replace(` (${agentId})`, '');
            
            // 重置商户选择
            selectedMerchantId = '';
            selectedMerchantName = '';
            
            // 重置商户选中样式
            const merchantItems = document.querySelectorAll('.merchant-item');
            merchantItems.forEach(item => {
                item.style.background = 'white';
            });
            
            // 在实际应用中，这里应该根据选择的代理商加载对应的商户列表
            // 这里仅作演示，使用静态数据
        }
        
        function selectPackage(packageId) {
            // 设置选中政策包
            selectedPackageId = packageId;
            selectedPackageName = event.currentTarget.querySelector('div:nth-child(2) div').innerText.replace('政策简介: ', '');
            
            // 重置选中样式
            const policyPackages = document.querySelectorAll('.policy-package');
            policyPackages.forEach(pkg => {
                pkg.style.border = '1px solid #dee2e6';
                pkg.style.background = 'white';
            });
            
            // 设置选中项样式
            event.currentTarget.style.border = '2px solid #007bff';
            event.currentTarget.style.background = '#f5faff';
        }
        
        function filterPolicyPackages() {
            const numberFilter = document.getElementById('policy-number-filter').value.toLowerCase();
            const descFilter = document.getElementById('policy-desc-filter').value.toLowerCase();
            
            const policyPackages = document.querySelectorAll('.policy-package');
            let visibleCount = 0;
            
            policyPackages.forEach(pkg => {
                const policyNumber = pkg.querySelector('div:first-child div:first-child').innerText.toLowerCase();
                const policyDesc = pkg.querySelector('div:nth-child(2) div').innerText.toLowerCase();
                
                const matchesNumber = numberFilter === '' || policyNumber.includes(numberFilter);
                const matchesDesc = descFilter === '' || policyDesc.includes(descFilter);
                
                if (matchesNumber && matchesDesc) {
                    pkg.style.display = 'block';
                    visibleCount++;
                } else {
                    pkg.style.display = 'none';
                }
            });
            
            // 重置选中的政策包
            if (selectedPackageId) {
                const selectedPackage = document.querySelector(`.policy-package[onclick*="'${selectedPackageId}'"`);
                if (selectedPackage && selectedPackage.style.display === 'none') {
                    selectedPackageId = '';
                    selectedPackageName = '';
                }
            }
            
            // 如果没有符合条件的政策包，显示提示
            const noResultsElem = document.getElementById('no-policy-results');
            if (visibleCount === 0) {
                if (!noResultsElem) {
                    const noResults = document.createElement('div');
                    noResults.id = 'no-policy-results';
                    noResults.style.padding = '20px';
                    noResults.style.textAlign = 'center';
                    noResults.style.color = '#6c757d';
                    noResults.innerText = '没有符合条件的政策包';
                    
                    const packageList = document.querySelector('#step2-content .policy-package').parentNode;
                    packageList.appendChild(noResults);
                }
            } else if (noResultsElem) {
                noResultsElem.remove();
            }
        }
        
        function resetPolicyFilter() {
            document.getElementById('policy-number-filter').value = '';
            document.getElementById('policy-desc-filter').value = '';
            
            const policyPackages = document.querySelectorAll('.policy-package');
            policyPackages.forEach(pkg => {
                pkg.style.display = 'block';
            });
            
            // 移除无结果提示
            const noResultsElem = document.getElementById('no-policy-results');
            if (noResultsElem) {
                noResultsElem.remove();
            }
        }
        
        function goToStep(step) {
            // 验证当前步骤是否完成
            if (step === 2) {
                if (selectedScope === '') {
                    alert('请选择适用范围');
                    return;
                }
                
                if (selectedScope === 'agent' && selectedAgentId === '') {
                    alert('请选择代理商');
                    return;
                }
                
                if (selectedScope === 'merchant' && (selectedMerchantId === '' || selectedAgentId === '')) {
                    alert('请选择代理商和商户');
                    return;
                }
            } else if (step === 3) {
                if (selectedPackageId === '') {
                    alert('请选择政策包');
                    return;
                }
                
                // 更新确认信息
                updateConfirmInfo();
            }
            
            // 隐藏所有步骤内容
            document.getElementById('step1-content').style.display = 'none';
            document.getElementById('step2-content').style.display = 'none';
            document.getElementById('step3-content').style.display = 'none';
            
            // 重置所有步骤样式
            document.getElementById('step1').style.background = '#f8f9fa';
            document.getElementById('step1').style.color = '#6c757d';
            document.getElementById('step1').style.fontWeight = 'normal';
            document.getElementById('step2').style.background = '#f8f9fa';
            document.getElementById('step2').style.color = '#6c757d';
            document.getElementById('step2').style.fontWeight = 'normal';
            document.getElementById('step3').style.background = '#f8f9fa';
            document.getElementById('step3').style.color = '#6c757d';
            document.getElementById('step3').style.fontWeight = 'normal';
            
            // 显示当前步骤内容并高亮当前步骤
            document.getElementById('step' + step + '-content').style.display = 'block';
            document.getElementById('step' + step).style.background = '#e9f5ff';
            document.getElementById('step' + step).style.color = '#007bff';
            document.getElementById('step' + step).style.fontWeight = '600';
        }
        
        function updateConfirmInfo() {
            // 更新确认页面的信息
            const confirmScope = document.getElementById('confirm-scope');
            const confirmTarget = document.getElementById('confirm-target');
            const confirmPackage = document.getElementById('confirm-package');
            
            // 设置适用范围
            if (selectedScope === 'agent') {
                confirmScope.innerText = '代理商 - 所有商户交易均享受该政策';
            } else if (selectedScope === 'merchant') {
                confirmScope.innerText = '代理商特定商户 - 仅选定商户交易享受该政策';
            }
            
            // 设置对象信息
            if (selectedScope === 'agent') {
                confirmTarget.innerHTML = `<div>代理商ID: ${selectedAgentId}</div><div>${selectedAgentName}</div>`;
            } else if (selectedScope === 'merchant') {
                confirmTarget.innerHTML = `<div>商户账号: ${selectedMerchantId}</div><div>${selectedMerchantName}</div><div>所属代理商ID: ${selectedAgentId}</div>`;
            }
            
            // 设置政策包信息
            confirmPackage.innerHTML = `<div>政策编号: ${selectedPackageId}</div><div>政策简介: ${selectedPackageName}</div>`;
        }
        
        function saveAssignment() {
            // 模拟保存操作
            alert('政策分配保存成功！');
            closeAssignmentModal();
            
            // 刷新列表（实际应用中应该调用API获取最新数据）
            // 这里只是模拟刷新效果
            setTimeout(function() {
                alert('列表已更新，新的政策分配已生效。');
            }, 500);
        }
        
        // 政策分配列表筛选相关函数
        function filterAssignments() {
            const policyFilter = document.getElementById('assignment-policy-filter').value.toLowerCase();
            const objectFilter = document.getElementById('assignment-object-filter').value;
            const statusFilter = document.getElementById('assignment-status-filter').value;
            
            const assignmentRows = document.querySelectorAll('#assignment-table tbody tr');
            let visibleCount = 0;
            
            assignmentRows.forEach(row => {
                const policyId = row.querySelector('td:nth-child(1)').innerText.toLowerCase();
                const objectType = row.querySelector('td:nth-child(3)').innerText;
                const status = row.querySelector('td:nth-child(6) span').innerText;
                
                const matchesPolicy = policyFilter === '' || policyId.includes(policyFilter);
                const matchesObject = objectFilter === '' || objectType.includes(objectFilter === 'agent' ? '代理商' : '特定商户');
                const matchesStatus = statusFilter === '' || (statusFilter === 'active' && status === '可用') || (statusFilter === 'inactive' && status === '不可用');
                
                if (matchesPolicy && matchesObject && matchesStatus) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });
            
            // 显示或隐藏无结果提示
            const noResultsElem = document.getElementById('no-assignment-results');
            if (visibleCount === 0) {
                if (!noResultsElem) {
                    const noResults = document.createElement('tr');
                    noResults.id = 'no-assignment-results';
                    const td = document.createElement('td');
                    td.setAttribute('colspan', '7');
                    td.style.textAlign = 'center';
                    td.style.padding = '20px';
                    td.style.color = '#6c757d';
                    td.innerText = '没有符合条件的政策分配记录';
                    
                    noResults.appendChild(td);
                    document.querySelector('#assignment-table tbody').appendChild(noResults);
                }
            } else if (noResultsElem) {
                noResultsElem.remove();
            }
        }
        
        function resetAssignmentFilter() {
            document.getElementById('assignment-policy-filter').value = '';
            document.getElementById('assignment-object-filter').value = '';
            document.getElementById('assignment-status-filter').value = '';
            
            const assignmentRows = document.querySelectorAll('#assignment-table tbody tr');
            assignmentRows.forEach(row => {
                row.style.display = '';
            });
            
            // 移除无结果提示
            const noResultsElem = document.getElementById('no-assignment-results');
            if (noResultsElem) {
                noResultsElem.remove();
            }
        }
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing page...');
            // 默认显示代理商签约信息页面
            showPage('agent-list');
        });

        // Tab切换功能
        function switchTab(module, tabName) {
            // 根据模块名找到对应的容器
            let container;
            if (module === 'commission') {
                container = document.querySelector('#commission-config');
            } else if (module === 'exchange') {
                container = document.querySelector('#exchange-rate-config');
            } else {
                return;
            }
            
            // 隐藏该模块下所有tab内容
            const tabContents = container.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除该模块下所有tab按钮的活跃状态
            const tabItems = container.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示选中的tab内容
            const targetTab = document.getElementById(module + '-' + tabName);
            if (targetTab) {
                targetTab.classList.add('active');
            }
            
            // 设置对应按钮为活跃状态
            event.target.classList.add('active');
        }
        
        // 新增分佣功能
        function addCommission(type) {
            if (type === 'base-price') {
                alert('新增底价分佣功能待开发');
            } else if (type === 'fixed-ratio') {
                alert('新增固定比例分佣功能待开发');
            }
        }
        
        // 新增汇率功能
        function addExchangeRate(type) {
            if (type === 'base-rate') {
                alert('新增底价汇率功能待开发');
            } else if (type === 'fixed-rebate') {
                showAddRebateModal();
            }
        }

        // 复制链接功能
        function copyToClipboard(text) {
            if (navigator.clipboard && window.isSecureContext) {
                // 使用现代 Clipboard API
                navigator.clipboard.writeText(text).then(function() {
                    // 显示复制成功提示
                    showCopySuccess();
                }).catch(function(err) {
                    console.error('复制失败:', err);
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                // 降级方案
                fallbackCopyTextToClipboard(text);
            }
        }

        // 降级复制方案
        function fallbackCopyTextToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            try {
                var successful = document.execCommand('copy');
                if (successful) {
                    showCopySuccess();
                } else {
                    alert('复制失败，请手动复制');
                }
            } catch (err) {
                alert('复制失败，请手动复制');
            }
            document.body.removeChild(textArea);
        }

        // 显示复制成功提示
        function showCopySuccess() {
            // 创建提示元素
            var toast = document.createElement('div');
            toast.textContent = '链接已复制到剪贴板';
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 10px 15px;
                border-radius: 4px;
                font-size: 14px;
                z-index: 10000;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                transition: opacity 0.3s ease;
            `;
            document.body.appendChild(toast);
            
            // 3秒后自动消失
            setTimeout(function() {
                toast.style.opacity = '0';
                setTimeout(function() {
                    if (toast.parentNode) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
        
        // 菜单收起功能
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const toggleBtn = document.getElementById('toggleBtn');
            
            if (sidebar.classList.contains('collapsed')) {
                // 展开菜单
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
                toggleBtn.innerHTML = '«';
            } else {
                // 收起菜单
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
                toggleBtn.innerHTML = '»';
            }
        }
        
        // 分页功能变量
        let currentPage = 1;
        let pageSize = 10;
        let totalRecords = 15; // 模拟总记录数
        let totalPages = Math.ceil(totalRecords / pageSize);
        
        // 跳转到指定页面
        function goToPage(page) {
            if (page < 1 || page > totalPages) return;
            
            currentPage = page;
            updatePagination();
            loadPageData();
        }
        
        // 上一页
        function previousPage() {
            if (currentPage > 1) {
                goToPage(currentPage - 1);
            }
        }
        
        // 下一页
        function nextPage() {
            if (currentPage < totalPages) {
                goToPage(currentPage + 1);
            }
        }
        
        // 更改每页显示数量
        function changePageSize(newSize) {
            pageSize = parseInt(newSize);
            totalPages = Math.ceil(totalRecords / pageSize);
            currentPage = 1;
            updatePagination();
            loadPageData();
        }
        
        // 更新分页显示
        function updatePagination() {
            // 更新记录信息
            const start = (currentPage - 1) * pageSize + 1;
            const end = Math.min(currentPage * pageSize, totalRecords);
            
            document.getElementById('currentStart').textContent = start;
            document.getElementById('currentEnd').textContent = end;
            document.getElementById('totalRecords').textContent = totalRecords;
            
            // 更新按钮状态
            document.getElementById('firstBtn').disabled = currentPage === 1;
            document.getElementById('prevBtn').disabled = currentPage === 1;
            document.getElementById('nextBtn').disabled = currentPage === totalPages;
            document.getElementById('lastBtn').disabled = currentPage === totalPages;
            
            // 更新页码按钮
            updatePageNumbers();
        }
        
        // 更新页码按钮
        function updatePageNumbers() {
            const pageNumbersDiv = document.getElementById('pageNumbers');
            pageNumbersDiv.innerHTML = '';
            
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
            
            // 调整起始页面
            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const btn = document.createElement('button');
                btn.className = 'pagination-btn' + (i === currentPage ? ' active' : '');
                btn.textContent = i;
                btn.onclick = () => goToPage(i);
                pageNumbersDiv.appendChild(btn);
            }
        }
        
        // 加载页面数据（模拟）
        function loadPageData() {
            // 这里可以添加实际的数据加载逻辑
            console.log(`加载第 ${currentPage} 页数据，每页 ${pageSize} 条`);
        }
        
        // 初始化分页
        document.addEventListener('DOMContentLoaded', function() {
            updatePagination();
        });
        
        // 代理政策管理相关函数
        function selectPackage(packageId) {
            // 移除所有选中状态
            document.querySelectorAll('.package-card').forEach(card => {
                card.style.borderColor = '#e9ecef';
                card.style.background = 'white';
            });
            
            // 设置当前选中的卡片样式
            event.currentTarget.style.borderColor = '#007bff';
            event.currentTarget.style.background = '#f8f9ff';
            
            console.log('选中政策包:', packageId);
        }
        
        function createPackage() {
            window.location.href = 'policy-package-create.html';
        }
        
        function demonstratePackageLogic() {
            alert('政策包应用逻辑演示功能');
        }
        
        // Tab切换功能
        function switchAgentTab(tabName) {
            // 隐藏所有tab内容
            const signedContent = document.getElementById('signed-content');
            const draftContent = document.getElementById('draft-content');
            
            // 获取所有tab按钮
            const signedBtn = document.querySelector('.tab-btn[onclick="switchAgentTab(\'signed\')"');
            const draftBtn = document.querySelector('.tab-btn[onclick="switchAgentTab(\'draft\')"');
            
            if (tabName === 'signed') {
                // 显示已签约tab
                signedContent.style.display = 'block';
                draftContent.style.display = 'none';
                
                // 更新按钮状态
                signedBtn.style.borderBottom = '2px solid #007bff';
                signedBtn.style.color = '#007bff';
                draftBtn.style.borderBottom = '2px solid transparent';
                draftBtn.style.color = '#6c757d';
            } else if (tabName === 'draft') {
                // 显示草稿tab
                signedContent.style.display = 'none';
                draftContent.style.display = 'block';
                
                // 更新按钮状态
                draftBtn.style.borderBottom = '2px solid #007bff';
                draftBtn.style.color = '#007bff';
                signedBtn.style.borderBottom = '2px solid transparent';
                signedBtn.style.color = '#6c757d';
            }
        }
        
        // 复制到剪贴板功能
        function copyToClipboard(text) {
            if (navigator.clipboard && window.isSecureContext) {
                // 使用现代API
                navigator.clipboard.writeText(text).then(function() {
                    showCopySuccess();
                }).catch(function(err) {
                    console.error('复制失败:', err);
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                // 降级方案
                fallbackCopyTextToClipboard(text);
            }
        }
        
        // 降级复制方案
        function fallbackCopyTextToClipboard(text) {
            var textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                var successful = document.execCommand('copy');
                if (successful) {
                    showCopySuccess();
                } else {
                    console.error('复制失败');
                }
            } catch (err) {
                console.error('复制失败:', err);
            }
            
            document.body.removeChild(textArea);
        }
        
        // 显示复制成功提示
        function showCopySuccess() {
            // 创建提示元素
            var toast = document.createElement('div');
            toast.textContent = '链接已复制到剪贴板';
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 14px;
                z-index: 10000;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                transition: opacity 0.3s ease;
            `;
            
            document.body.appendChild(toast);
            
            // 3秒后自动消失
            setTimeout(function() {
                toast.style.opacity = '0';
                setTimeout(function() {
                    if (toast.parentNode) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
    </script>
    <!-- 更换政策弹窗 -->
    <div id="change-policy-modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);">
        <div style="background: white; margin: 5% auto; padding: 0; width: 80%; max-width: 800px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <!-- 弹窗标题 -->
            <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 20px; border-bottom: 1px solid #e9ecef;">
                <h3 style="margin: 0; font-size: 18px; font-weight: 600;">更换政策包</h3>
                <span onclick="closeChangePolicyModal()" style="cursor: pointer; font-size: 20px; color: #6c757d;">&times;</span>
            </div>
            
            <!-- 弹窗内容 -->
            <div style="padding: 20px;">
                <!-- 当前信息 -->
                <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                    <h4 style="margin: 0 0 10px 0; font-size: 16px; color: #495057;">当前政策信息</h4>
                    <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                        <div style="flex: 1; min-width: 200px;">
                            <div style="font-size: 13px; color: #6c757d; margin-bottom: 5px;">分配对象</div>
                            <div id="change-current-object-type" style="font-size: 14px; font-weight: 500;">代理商</div>
                        </div>
                        <div style="flex: 1; min-width: 200px;">
                            <div style="font-size: 13px; color: #6c757d; margin-bottom: 5px;">对象信息</div>
                            <div id="change-current-object-info" style="font-size: 14px; font-weight: 500;">欧瑞华科技代理商 (AGENT001)</div>
                        </div>
                        <div style="flex: 1; min-width: 200px;">
                            <div style="font-size: 13px; color: #6c757d; margin-bottom: 5px;">当前政策包</div>
                            <div id="change-current-policy" style="font-size: 14px; font-weight: 500;">A级代理商政策包 (POL-A001)</div>
                        </div>
                    </div>
                </div>
                
                <!-- 选择新政策包 -->
                <div>
                    <h4 style="margin: 0 0 15px 0; font-size: 16px; color: #495057;">选择新政策包</h4>
                    
                    <!-- 政策包筛选 -->
                    <div style="margin-bottom: 20px; background: #f8f9fa; padding: 15px; border-radius: 6px;">
                        <div style="display: flex; gap: 15px; margin-bottom: 10px;">
                            <div style="flex: 1;">
                                <label style="display: block; font-size: 13px; color: #495057; margin-bottom: 5px;">政策编号</label>
                                <input id="change-policy-filter" type="text" placeholder="请输入政策编号" style="width: 100%; padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                            </div>
                            <div style="flex: 1;">
                                <label style="display: block; font-size: 13px; color: #495057; margin-bottom: 5px;">政策描述</label>
                                <input id="change-description-filter" type="text" placeholder="请输入政策描述关键词" style="width: 100%; padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                            </div>
                        </div>
                        <div style="display: flex; justify-content: flex-end;">
                            <button onclick="filterChangePolicyPackages()" style="background: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">筛选</button>
                            <button onclick="resetChangePolicyFilter()" style="background: white; color: #6c757d; border: 1px solid #6c757d; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer; margin-left: 10px;">重置</button>
                        </div>
                    </div>
                    
                    <!-- 政策包列表 -->
                    <div style="max-height: 300px; overflow-y: auto; margin-bottom: 20px;">
                        <div id="change-policy-list" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 15px;">
                            <!-- 政策包卡片将通过JavaScript动态生成 -->
                        </div>
                        <div id="no-change-policy-results" style="display: none; text-align: center; padding: 20px; color: #6c757d;">没有符合条件的政策包</div>
                    </div>
                </div>
            </div>
            
            <!-- 弹窗底部按钮 -->
            <div style="padding: 15px 20px; border-top: 1px solid #e9ecef; display: flex; justify-content: flex-end; gap: 10px;">
                <button onclick="closeChangePolicyModal()" style="background: white; color: #6c757d; border: 1px solid #6c757d; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">取消</button>
                <button id="save-change-policy-btn" onclick="saveChangedPolicy()" style="background: #28a745; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;" disabled>保存更改</button>
            </div>
        </div>
    </div>
    <!-- 新增固定返点弹层 -->
    <div id="rebateModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); justify-content: center; align-items: center;">
        <div class="modal-content" style="background-color: #fff; border-radius: 8px; width: 90%; max-width: 800px; max-height: 90vh; overflow-y: auto; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);">
            <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #e9ecef; display: flex; justify-content: space-between; align-items: center;">
                <h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #333;">新增固定返点配置</h3>
                <span class="close" onclick="hideRebateModal()" style="font-size: 24px; font-weight: bold; color: #999; cursor: pointer; line-height: 1;">&times;</span>
            </div>
            
            <form id="rebateForm" class="modal-body" style="padding: 24px;">
                <!-- 基本信息 -->
                <div class="form-section" style="margin-bottom: 24px;">
                    <h4 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600; color: #333;">基本信息</h4>
                    
                    <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                        <div class="form-group" style="flex: 1;">
                            <label for="description" style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">返点简介 <span class="required" style="color: #e74c3c;">*</span></label>
                            <input type="text" id="description" name="description" placeholder="请输入返点简介" required style="width: 100%; padding: 10px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                        </div>
                    </div>
                    
                    <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                        <div class="form-group" style="flex: 1;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">返点类型 <span class="required" style="color: #e74c3c;">*</span></label>
                            <div class="radio-group" style="display: flex; gap: 16px;">
                                <label class="radio-label" style="display: flex; align-items: center; cursor: pointer;">
                                    <input type="radio" name="rebateType" value="standard" checked style="margin-right: 8px;">
                                    标准返点
                                </label>
                                <label class="radio-label" style="display: flex; align-items: center; cursor: pointer;">
                                    <input type="radio" name="rebateType" value="special" style="margin-right: 8px;">
                                    特殊返点
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 返点配置 -->
                <div class="form-section" style="margin-bottom: 24px;">
                    <h4 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600; color: #333;">返点配置</h4>
                    
                    <!-- 卖出币种 -->
                    <div class="form-row" style="margin-bottom: 16px;">
                        <div class="form-group">
                            <label for="sellCurrency" style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">卖出币种 <span class="required" style="color: #e74c3c;">*</span></label>
                            <select id="sellCurrency" name="sellCurrency" onchange="updateQuoteAndProfit()" required style="width: 100%; padding: 10px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                                <option value="">请选择币种</option>
                                <option value="USD">USD - 美元</option>
                                <option value="EUR">EUR - 欧元</option>
                                <option value="GBP">GBP - 英镑</option>
                                <option value="JPY">JPY - 日元</option>
                                <option value="HKD">HKD - 港币</option>
                                <option value="AUD">AUD - 澳元</option>
                                <option value="CAD">CAD - 加元</option>
                                <option value="SGD">SGD - 新币</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 买入币种 -->
                    <div class="form-row" style="margin-bottom: 16px;">
                        <div class="form-group">
                            <label for="buyCurrency" style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">买入币种 <span class="required" style="color: #e74c3c;">*</span></label>
                            <select id="buyCurrency" name="buyCurrency" onchange="updateQuoteAndProfit()" required style="width: 100%; padding: 10px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                                <option value="">请选择币种</option>
                                <option value="CNY">CNY - 人民币</option>
                                <option value="USD">USD - 美元</option>
                                <option value="EUR">EUR - 欧元</option>
                                <option value="GBP">GBP - 英镑</option>
                                <option value="JPY">JPY - 日元</option>
                                <option value="HKD">HKD - 港币</option>
                                <option value="AUD">AUD - 澳元</option>
                                <option value="CAD">CAD - 加元</option>
                                <option value="SGD">SGD - 新币</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 卖出金额 -->
                    <div class="form-row" style="margin-bottom: 16px;">
                        <div class="form-group">
                            <label for="sellAmount" style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">卖出金额</label>
                            <div class="amount-display" style="display: flex; align-items: center; gap: 8px; padding: 10px 12px; border: 1px solid #ddd; border-radius: 4px; background: #f8f9fa; color: #666;">
                                <span id="sellAmountDisplay" style="font-weight: 600;">1,000</span>
                                <span class="currency-unit" id="sellCurrencyUnit" style="color: #999;"></span>
                            </div>
                            <input type="hidden" id="sellAmount" name="sellAmount" value="1000">
                        </div>
                    </div>
                    
                    <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                        <div class="form-group" style="flex: 1;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">报价类型 <span class="required" style="color: #e74c3c;">*</span></label>
                            <div class="radio-group" style="display: flex; gap: 16px;">
                                <label class="radio-label" style="display: flex; align-items: center; cursor: pointer;">
                                    <input type="radio" name="quoteType" value="standard" checked onchange="toggleQuoteType()" style="margin-right: 8px;">
                                    标准对客报价
                                </label>
                                <label class="radio-label" style="display: flex; align-items: center; cursor: pointer;">
                                    <input type="radio" name="quoteType" value="special" onchange="toggleQuoteType()" style="margin-right: 8px;">
                                    特殊客户报价
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div id="customerAccountGroup" class="form-row" style="display: none; gap: 16px; margin-bottom: 16px;">
                        <div class="form-group" style="flex: 1;">
                            <label for="customerAccount" style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">账户号码</label>
                            <input type="text" id="customerAccount" name="customerAccount" placeholder="请输入账户号码" onblur="queryCustomer()" style="width: 100%; padding: 10px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                        </div>
                        <div class="form-group" style="flex: 1;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">商户名称</label>
                            <div class="customer-name" id="customerName" style="padding: 10px 12px; border: 1px solid #ddd; border-radius: 4px; background: #f8f9fa; color: #666; min-height: 38px; display: flex; align-items: center;"></div>
                        </div>
                    </div>
                    
                    <!-- 报价信息展示 -->
                    <div class="quote-info" style="background: #f8f9fa; padding: 16px; border-radius: 6px; margin-bottom: 16px;">
                        <div class="quote-row" style="display: flex; flex-direction: column; gap: 12px;">
                            <div class="quote-item" style="display: flex; justify-content: space-between; align-items: center;">
                                <label style="font-weight: 500; color: #666;">对客报价:</label>
                                <span id="clientQuote" style="font-size: 16px; font-weight: 600; color: #333;">0.0000</span>
                            </div>
                            <div class="quote-item" style="display: flex; justify-content: space-between; align-items: center;">
                                <label style="font-weight: 500; color: #666;">成本报价:</label>
                                <span id="costQuote" style="font-size: 16px; font-weight: 600; color: #333;">0.0000</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                        <div class="form-group" style="flex: 1;">
                            <label for="rebateValue" style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">返点值 <span class="required" style="color: #e74c3c;">*</span></label>
                            <div class="input-with-unit" style="position: relative;">
                                <input type="number" id="rebateValue" name="rebateValue" step="0.000001" max="100" placeholder="0.000000" onchange="updateQuoteAndProfit()" required style="width: 100%; padding: 10px 12px; padding-right: 30px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                                <span class="unit" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #666;">%</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 预计收益展示 -->
                    <div class="profit-info" style="background: #e8f5e8; padding: 12px 16px; border-radius: 6px; margin-bottom: 16px;">
                        <label style="font-weight: 500; color: #333; margin-right: 8px;">预计收益:</label>
                        <span id="expectedProfit" style="font-size: 16px; font-weight: 600; color: #28a745;">0.00</span> CNY
                    </div>
                    
                    <div class="form-row" style="display: flex; gap: 16px; margin-bottom: 16px;">
                        <div class="form-group" style="flex: 1;">
                            <label for="remarks" style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">备注</label>
                            <textarea id="remarks" name="remarks" rows="3" placeholder="请输入备注信息" style="width: 100%; padding: 10px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; resize: vertical;"></textarea>
                        </div>
                    </div>
                </div>
            </form>
            
            <div class="modal-footer" style="padding: 16px 24px; border-top: 1px solid #e9ecef; display: flex; justify-content: flex-end; gap: 12px;">
                <button type="button" class="btn btn-default" onclick="hideRebateModal()" style="padding: 8px 16px; border: 1px solid #ddd; background: white; color: #666; border-radius: 4px; cursor: pointer;">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRebateConfig()" style="padding: 8px 16px; border: none; background: #007bff; color: white; border-radius: 4px; cursor: pointer;">保存</button>
            </div>
        </div>
    </div>

    <!-- 更换政策模态框 -->
    <div id="changePolicyModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1000; overflow-y: auto;">
        <div style="background: white; width: 80%; max-width: 800px; margin: 50px auto; border-radius: 8px; box-shadow: 0 5px 15px rgba(0,0,0,0.3); padding: 0;">
            <!-- 模态框头部 -->
            <div style="padding: 20px; border-bottom: 1px solid #e9ecef; display: flex; justify-content: space-between; align-items: center;">
                <h3 style="margin: 0; font-size: 20px; font-weight: 600;">更换政策</h3>
                <button onclick="closeChangePolicyModal()" style="background: none; border: none; font-size: 20px; cursor: pointer; color: #6c757d;">&times;</button>
            </div>
            
            <!-- 模态框内容 -->
            <div style="padding: 20px;">
                <!-- 当前分配信息 -->
                <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                    <h4 style="margin: 0 0 10px 0; font-size: 16px; color: #495057;">当前分配信息</h4>
                    <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                        <div style="min-width: 200px;">
                            <div style="font-size: 13px; color: #6c757d; margin-bottom: 3px;">政策编号</div>
                            <div id="current-policy-id" style="font-size: 14px; font-weight: 500;">POL-A001</div>
                        </div>
                        <div style="min-width: 200px;">
                            <div style="font-size: 13px; color: #6c757d; margin-bottom: 3px;">政策简介</div>
                            <div id="current-policy-desc" style="font-size: 14px; font-weight: 500;">A级代理商政策包</div>
                        </div>
                        <div style="min-width: 200px;">
                            <div style="font-size: 13px; color: #6c757d; margin-bottom: 3px;">分配对象</div>
                            <div id="current-object-type" style="font-size: 14px; font-weight: 500;">代理商</div>
                        </div>
                        <div style="min-width: 200px;">
                            <div style="font-size: 13px; color: #6c757d; margin-bottom: 3px;">对象信息</div>
                            <div id="current-object-info" style="font-size: 14px; font-weight: 500;">AGENT001 - 欧瑞华科技代理商</div>
                        </div>
                    </div>
                </div>
                
                <!-- 政策包筛选 -->
                <div style="margin-bottom: 20px;">
                    <h4 style="margin: 0 0 10px 0; font-size: 16px; color: #495057;">选择新政策包</h4>
                    <div style="background: #f8f9fa; border-radius: 6px; padding: 15px; margin-bottom: 15px;">
                        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                            <div style="min-width: 200px;">
                                <label style="display: block; font-size: 13px; color: #495057; margin-bottom: 5px;">政策编号</label>
                                <input id="change-policy-number-filter" type="text" placeholder="请输入政策编号" style="width: 100%; padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                            </div>
                            <div style="min-width: 200px;">
                                <label style="display: block; font-size: 13px; color: #495057; margin-bottom: 5px;">政策简介</label>
                                <input id="change-policy-desc-filter" type="text" placeholder="请输入政策简介" style="width: 100%; padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                            </div>
                            <div style="min-width: 200px; display: flex; align-items: flex-end;">
                                <button onclick="filterChangePolicyPackages()" style="background: #007bff; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">搜索</button>
                                <button onclick="resetChangePolicyFilter()" style="background: white; color: #6c757d; border: 1px solid #6c757d; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer; margin-left: 10px;">重置</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 政策包列表 -->
                <div style="margin-bottom: 20px; max-height: 300px; overflow-y: auto;">
                    <div id="change-policy-list" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px;">
                        <!-- 政策包项目 - 将通过JavaScript动态生成 -->
                        <div class="change-policy-item" data-id="POL-A001" onclick="selectChangePolicy(this)" style="border: 1px solid #dee2e6; border-radius: 6px; padding: 15px; cursor: pointer;">
                            <div style="font-weight: 600; margin-bottom: 5px;">POL-A001</div>
                            <div style="font-size: 14px; color: #495057;">A级代理商政策包</div>
                            <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">适用于A级代理商的标准政策</div>
                        </div>
                        <div class="change-policy-item" data-id="POL-B002" onclick="selectChangePolicy(this)" style="border: 1px solid #dee2e6; border-radius: 6px; padding: 15px; cursor: pointer;">
                            <div style="font-weight: 600; margin-bottom: 5px;">POL-B002</div>
                            <div style="font-size: 14px; color: #495057;">B级代理商政策包</div>
                            <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">适用于B级代理商的标准政策</div>
                        </div>
                        <div class="change-policy-item" data-id="POL-C003" onclick="selectChangePolicy(this)" style="border: 1px solid #dee2e6; border-radius: 6px; padding: 15px; cursor: pointer;">
                            <div style="font-weight: 600; margin-bottom: 5px;">POL-C003</div>
                            <div style="font-size: 14px; color: #495057;">C级代理商政策包</div>
                            <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">适用于C级代理商的标准政策</div>
                        </div>
                        <div class="change-policy-item" data-id="POL-S004" onclick="selectChangePolicy(this)" style="border: 1px solid #dee2e6; border-radius: 6px; padding: 15px; cursor: pointer;">
                            <div style="font-weight: 600; margin-bottom: 5px;">POL-S004</div>
                            <div style="font-size: 14px; color: #495057;">特殊优惠政策包</div>
                            <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">适用于特殊客户的定制政策</div>
                        </div>
                    </div>
                    <div id="no-change-policy-results" style="display: none; padding: 20px; text-align: center; color: #6c757d;">没有符合条件的政策包</div>
                </div>
            </div>
            
            <!-- 模态框底部 -->
            <div style="padding: 15px 20px; border-top: 1px solid #e9ecef; display: flex; justify-content: flex-end; gap: 10px;">
                <button onclick="closeChangePolicyModal()" style="background: white; color: #6c757d; border: 1px solid #6c757d; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer;">取消</button>
                <button id="save-change-policy-btn" onclick="saveChangePolicy()" style="background: #28a745; color: white; border: none; padding: 8px 20px; border-radius: 4px; font-size: 14px; cursor: pointer; opacity: 0.6;" disabled>保存</button>
            </div>
        </div>
    </div>
<script>
// 新增固定返点弹层交互函数
function showAddRebateModal() {
    document.getElementById('rebateModal').style.display = 'flex';
    resetRebateForm();
}

function hideRebateModal() {
    document.getElementById('rebateModal').style.display = 'none';
}

function resetRebateForm() {
    document.getElementById('rebateForm').reset();
    document.getElementById('customerAccountGroup').style.display = 'none';
    document.getElementById('customerName').textContent = '';
    document.getElementById('clientQuote').textContent = '0.0000';
    document.getElementById('costQuote').textContent = '0.0000';
    document.getElementById('expectedProfit').textContent = '0.00';
}

function toggleQuoteType() {
    const quoteType = document.querySelector('input[name="quoteType"]:checked').value;
    const customerAccountGroup = document.getElementById('customerAccountGroup');
    
    if (quoteType === 'special') {
        customerAccountGroup.style.display = 'flex';
    } else {
        customerAccountGroup.style.display = 'none';
        document.getElementById('customerAccount').value = '';
        document.getElementById('customerName').textContent = '';
    }
    
    updateQuoteAndProfit();
}

function queryCustomer() {
    const account = document.getElementById('customerAccount').value;
    const customerNames = {
        'ACC001': '北京科技有限公司',
        'ACC002': '上海贸易公司',
        'ACC003': '深圳电商企业',
        'ACC004': '广州进出口公司'
    };
    
    const customerName = customerNames[account] || '';
    document.getElementById('customerName').textContent = customerName;
    
    if (customerName) {
        updateQuoteAndProfit();
    }
}

function updateQuoteAndProfit() {
    const sellCurrency = document.getElementById('sellCurrency').value;
    const buyCurrency = document.getElementById('buyCurrency').value;
    const sellAmount = parseFloat(document.getElementById('sellAmount').value) || 1000;
    const rebateValue = parseFloat(document.getElementById('rebateValue').value) || 0;
    const quoteType = document.querySelector('input[name="quoteType"]:checked').value;
    
    if (!sellCurrency || !buyCurrency || sellCurrency === buyCurrency) {
        return;
    }
    
    // 模拟汇率数据
    const exchangeRates = {
        'USD-CNY': { client: 7.1230, cost: 7.1330 },
        'EUR-CNY': { client: 7.8450, cost: 7.8550 },
        'GBP-CNY': { client: 9.1200, cost: 9.1300 },
        'JPY-CNY': { client: 0.0485, cost: 0.0495 },
        'HKD-CNY': { client: 0.9120, cost: 0.9130 },
        'AUD-CNY': { client: 4.7800, cost: 4.7900 },
        'CAD-CNY': { client: 5.2300, cost: 5.2400 },
        'SGD-CNY': { client: 5.3100, cost: 5.3200 }
    };
    
    const rateKey = `${sellCurrency}-${buyCurrency}`;
    const reverseKey = `${buyCurrency}-${sellCurrency}`;
    
    let clientQuote = 0, costQuote = 0;
    
    if (exchangeRates[rateKey]) {
        clientQuote = exchangeRates[rateKey].client;
        costQuote = exchangeRates[rateKey].cost;
    } else if (exchangeRates[reverseKey]) {
        clientQuote = 1 / exchangeRates[reverseKey].client;
        costQuote = 1 / exchangeRates[reverseKey].cost;
    }
    
    // 特殊客户报价调整
    if (quoteType === 'special') {
        clientQuote *= 0.999; // 给特殊客户更优惠的价格
        costQuote *= 0.999;
    }
    
    document.getElementById('clientQuote').textContent = clientQuote.toFixed(4);
    document.getElementById('costQuote').textContent = costQuote.toFixed(4);
    
    // 计算预计收益
    const spread = costQuote - clientQuote;
    const rebateAmount = spread * (rebateValue / 100);
    const expectedProfit = (spread - rebateAmount) * sellAmount;
    
    document.getElementById('expectedProfit').textContent = expectedProfit.toFixed(2);
}

function saveRebateConfig() {
    const formData = new FormData(document.getElementById('rebateForm'));
    const rebateData = {
        description: formData.get('description'),
        rebateType: formData.get('rebateType'),
        sellCurrency: formData.get('sellCurrency'),
        buyCurrency: formData.get('buyCurrency'),
        sellAmount: formData.get('sellAmount'),
        quoteType: formData.get('quoteType'),
        customerAccount: formData.get('customerAccount'),
        rebateValue: formData.get('rebateValue'),
        remarks: formData.get('remarks')
    };
    
    // 表单验证
    if (!rebateData.description || !rebateData.sellCurrency || !rebateData.buyCurrency || !rebateData.rebateValue) {
        alert('请填写所有必填字段');
        return;
    }
    
    if (rebateData.sellCurrency === rebateData.buyCurrency) {
        alert('卖出币种和买入币种不能相同');
        return;
    }
    
    if (rebateData.quoteType === 'special' && !rebateData.customerAccount) {
        alert('特殊客户报价需要输入账户号码');
        return;
    }
    
    // 模拟保存
    console.log('保存返点配置:', rebateData);
    alert('返点配置保存成功！');
    hideRebateModal();
    
    // 这里可以刷新表格数据
    // loadRebateList();
}

// 点击弹层外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('rebateModal');
    if (event.target === modal) {
        hideRebateModal();
    }
}
</script>
</body>
</html>
