# 汇率返点与交易分佣风险控制规则

## 1. 交易分佣信息

### 1.1 底价分佣规则
- **本期范围**：本期仅包括底价分佣模式
- **配置参考**：配置底价时，需要参考以下两个关键指标：
  - 自身成本价
  - 对客户的标准报价
- **底价限制**：租户给代理商配置的底价不能突破 ex 给租户的底价
- **价格层级**：ex → 租户 → 代理商 → 客户

### 1.2 价格保护机制
- 默认情况下，系统会自动校验底价设置，确保不会突破上级配置的底价
- 租户可以让利给代理商（突破自身成本价），但默认不能突破 ex 给租户的价格
- 如需特殊情况突破底价，需通过风控审核流程

## 2. ex 配置租户底价与汇率返点

### 2.1 基本规则
- ex 给租户配置底价和汇率返点需要经过风控审核
- 风控初始规则：不能突破 ex 给租户的底价

### 2.2 风控配置参数
风控系统可配置以下参数来管理底价突破情况：

| 参数类型 | 说明 |
|---------|------|
| 可突破底价的配置条数 | 允许突破底价的最大政策条目数量 |
| 突破的浮动值 | 允许突破底价的最大百分比或固定值 |
| 突破的交易笔数 | 允许在突破底价情况下的最大交易笔数 |
| 突破的交易金额 | 允许在突破底价情况下的最大交易金额总额 |

### 2.3 风控处理机制
当交易超过风控阈值时，系统将采取以下措施：
- **出款交易**：直接拦截，不允许完成交易
- **入账交易**：进入 holding 状态，等待人工审核

## 3. 业务流程图

```mermaid
flowchart TD
    A[开始] --> B{配置类型?}
    B -->|租户给代理商配置| C[检查底价]
    B -->|ex给租户配置| D[风控审核]
    
    C --> C1{是否突破ex给租户底价?}
    C1 -->|是| C2[拒绝配置]
    C1 -->|否| C3[允许配置]
    
    D --> D1{是否突破ex底价?}
    D1 -->|是| D2{是否在风控允许范围?}
    D1 -->|否| D3[允许配置]
    
    D2 -->|是| D4[允许配置但标记风险]
    D2 -->|否| D5[拒绝配置]
    
    C3 --> E[配置生效]
    D3 --> E
    D4 --> E
    
    E --> F[交易执行]
    F --> G{是否超过风控阈值?}
    G -->|是| H{交易类型?}
    G -->|否| I[正常处理]
    
    H -->|出款| J[直接拦截]
    H -->|入账| K[进入holding状态]
    
    J --> L[结束]
    K --> M[人工审核]
    I --> L
    M --> L
```

## 4. 风控审核流程

### 4.1 常规审核流程
1. 系统自动检查配置参数是否符合基本规则
2. 如发现突破底价情况，进入风控审核流程
3. 风控审核考虑以下因素：
   - 突破底价的幅度
   - 历史交易记录
   - 客户信用等级
   - 特殊业务需求

### 4.2 特殊审核流程
对于特殊情况（如战略客户、特殊市场活动），可申请特殊审核流程：
1. 提交特殊审核申请，说明突破底价的原因和预期收益
2. 业务部门审核
3. 风控部门审核
4. 财务部门审核
5. 最终审批

## 5. 风险监控与预警

系统将持续监控所有突破底价的交易情况，并提供以下预警机制：

- **日常监控**：每日统计突破底价的交易量和金额
- **阈值预警**：当接近风控阈值时，系统自动发出预警通知
- **异常预警**：当检测到异常交易模式时，触发人工审核

## 6. 实施建议

1. 分阶段实施风控规则，先严后松
2. 建立完善的审核流程和责任机制
3. 定期评估风控规则的有效性，根据业务发展适时调整
4. 加强对代理商和租户的培训，提高风险意识
5. 建立风险事件应急处理机制

---

*注：本文档为内部风控规则，请勿对外分享。规则可能根据业务发展进行调整，请以最新版本为准。*
