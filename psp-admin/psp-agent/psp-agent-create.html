<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增代理商</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.5;
        }

        .container {
            min-height: 100vh;
            padding: 20px;
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .breadcrumb {
            color: #666;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #007bff;
            text-decoration: none;
        }

        .step-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .step-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .step-nav {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .step-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .step-item.active {
            background: #007bff;
            color: white;
        }

        .step-item.completed {
            background: #28a745;
            color: white;
        }

        .step-item.inactive {
            background: #e9ecef;
            color: #6c757d;
        }

        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }

        .step-content {
            padding: 30px;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .form-label.required::after {
            content: " *";
            color: #dc3545;
        }

        .form-input {
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .form-select {
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            background: white;
            transition: border-color 0.2s;
        }

        .form-select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .form-textarea {
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
            transition: border-color 0.2s;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .checkbox-group {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            margin-top: 30px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-outline {
            background: transparent;
            color: #007bff;
            border: 1px solid #007bff;
        }

        .btn-outline:hover {
            background: #007bff;
            color: white;
        }

        .phone-input-group {
            display: flex;
            gap: 10px;
        }

        .country-code {
            width: 100px;
        }

        .phone-number {
            flex: 1;
        }

        .help-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 4px;
        }

        .policy-packages-container {
    display: flex;
    gap: 24px;
    margin-top: 16px;
    margin-bottom: 8px;
}
.package-card {
    flex: 1;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}
.package-card.selected,
.package-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 16px rgba(0,123,255,0.08);
    background: #f8faff;
}
.package-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}
.package-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}
.package-badge {
    display: inline-block;
    padding: 2px 10px;
    border-radius: 12px;
    font-size: 12px;
    color: #fff;
    background: #007bff;
    margin-left: 8px;
}
.badge-standard { background: #007bff; }
.badge-premium { background: #28a745; }
.badge-basic { background: #6c757d; }
.package-desc {
    font-size: 14px;
    color: #666;
    margin-bottom: 16px;
}
.fee-structure {
    display: flex;
    gap: 24px;
}
.fee-column {
    flex: 1;
}
.fee-column-title {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 6px;
}
.fee-item {
    display: flex;
    justify-content: space-between;
    font-size: 13px;
    margin-bottom: 4px;
}
.fee-name { color: #333; }
.fee-type { color: #888; }
.fee-value.commission { color: #007bff; font-weight: 600; }
.fee-value.rebate { color: #28a745; font-weight: 600; }

/* 保留原有.radio-card样式，防止其它区域受影响 */
.radio-card {
            flex: 1;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .radio-card:hover {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .radio-card input[type="radio"] {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 18px;
            height: 18px;
        }

        .radio-card.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .radio-card-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .radio-card-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        .checkbox-card {
            flex: 1;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .checkbox-card:hover {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .checkbox-card input[type="checkbox"] {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 18px;
            height: 18px;
        }

        .checkbox-card.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .checkbox-card-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .checkbox-card-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        .policy-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .policy-option:hover {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .policy-option.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .policy-option input[type="radio"] {
            width: 16px;
            height: 16px;
        }

        .policy-option label {
            font-size: 14px;
            color: #333;
            cursor: pointer;
            margin: 0;
        }

        .policy-subsection {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 15px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
            text-decoration: none;
            display: inline-block;
        }

        .back-button:hover {
            background: #0056b3;
            color: white;
        }
    </style>
</head>
<body>
    <a href="psp-agent-simple.html" class="back-button">← 返回</a>
    
    <div class="container">
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 页面头部 -->
            <div class="page-header">
                <h1 class="page-title">新增代理商</h1>
                <div class="breadcrumb">
                    <a href="psp-agent-simple.html">代理商管理</a> / 新增代理商
                </div>
            </div>

            <!-- 步骤容器 -->
            <div class="step-container">
                <!-- 步骤导航 -->
                <div class="step-header">
                    <div class="step-nav">
                        <div class="step-item active" id="step1">
                            <div class="step-number">1</div>
                            <span>代理商基本信息</span>
                        </div>
                        <div class="step-item inactive" id="step2">
                            <div class="step-number">2</div>
                            <span>选择代理商政策</span>
                        </div>
                    </div>
                </div>

                <!-- 步骤内容 -->
                <div class="step-content">
                    <!-- 第一步：代理商基本信息 -->
                    <div id="step1-content">
                        <form id="agentForm">
                            <!-- 代理商基本信息 -->
                            <div class="form-section">
                                <h3 class="section-title">代理商基本信息</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label required">代理商全称</label>
                                        <input type="text" class="form-input" placeholder="请输入代理商全称" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label required">代理商简称</label>
                                        <input type="text" class="form-input" placeholder="请输入代理商简称" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label required">合同编号</label>
                                        <input type="text" class="form-input" placeholder="请输入合同编号" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label required">代理商类型</label>
                                        <select class="form-select" required>
                                            <option value="">请选择代理商类型</option>
                                            <option value="individual">个人</option>
                                            <option value="enterprise">企业</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label required">代理商等级</label>
                                        <select class="form-select" required>
                                            <option value="">请选择代理商等级</option>
                                            <option value="A">A级 - 高等级</option>
                                            <option value="B">B级 - 中等级</option>
                                            <option value="C">C级 - 低等级</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">所属销售</label>
                                        <select class="form-select">
                                            <option value="">请选择所属销售</option>
                                            <option value="sales1">李销售</option>
                                            <option value="sales2">张销售</option>
                                            <option value="sales3">王销售</option>
                                        </select>
                                    </div>
                                </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">备注</label>
                                        <textarea class="form-textarea" placeholder="请输入备注信息"></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- 代理商管理员 -->
                            <div class="form-section">
                                <h3 class="section-title">代理商管理员</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label required">管理员姓名</label>
                                        <input type="text" class="form-input" placeholder="请输入管理员姓名" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label required">管理员手机</label>
                                        <div class="phone-input-group">
                                            <select class="form-select country-code">
                                                <option value="+86">+86</option>
                                                <option value="+1">+1</option>
                                                <option value="+44">+44</option>
                                                <option value="+852">+852</option>
                                            </select>
                                            <input type="tel" class="form-input phone-number" placeholder="请输入手机号码" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label required">管理员邮箱</label>
                                        <input type="email" class="form-input" placeholder="请输入管理员邮箱" required>
                                    </div>
                                </div>
                            </div>

                            <!-- 结算信息 -->
                            <div class="form-section">
                                <h3 class="section-title">结算信息</h3>
                                
                                <!-- 1. 结算日期与结算币种 -->
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                                    <h4 style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 15px;">结算日期与结算币种</h4>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">结算周期</label>
                                            <select class="form-select">
                                                <option value="">请选择结算周期</option>
                                                <option value="monthly">每月20日</option>
                                                <option value="quarterly">每季度末</option>
                                                <option value="custom">自定义</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">结算币种</label>
                                            <select class="form-select" id="settlementCurrency" onchange="toggleInvoiceCapability()">
                                                <option value="">请选择结算币种</option>
                                                <option value="USD">USD - 美元</option>
                                                <option value="CNY">CNY - 人民币</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <!-- 开票能力（仅CNY时显示） -->
                                    <div id="invoiceCapabilitySection" style="display: none; margin-top: 15px;">
                                        <div class="form-group">
                                            <label class="form-label">开票能力</label>
                                            <div style="display: flex; gap: 15px; margin-top: 8px;">
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="invoiceCapability" value="general" style="margin-right: 6px;">
                                                    <span>增值税普通发票</span>
                                                </label>
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="invoiceCapability" value="vat" style="margin-right: 6px;">
                                                    <span>增值税专用发票</span>
                                                </label>
                                                <label style="display: flex; align-items: center; cursor: pointer;">
                                                    <input type="radio" name="invoiceCapability" value="none" style="margin-right: 6px;">
                                                    <span>不能开票</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 2. 结算账户 -->
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
                                    <h4 style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 15px;">结算账户</h4>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">账户名称</label>
                                            <input type="text" class="form-input" placeholder="请输入账户名称">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">账号</label>
                                            <input type="text" class="form-input" placeholder="请输入账号">
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">开户银行</label>
                                            <input type="text" class="form-input" placeholder="请输入开户银行">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">银行分支</label>
                                            <input type="text" class="form-input" placeholder="请输入银行分支">
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">SWIFT Code</label>
                                            <input type="text" class="form-input" placeholder="请输入SWIFT Code">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">路由号码</label>
                                            <input type="text" class="form-input" placeholder="请输入路由号码（可选）">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="form-actions">
                                <a href="psp-agent-simple.html" class="btn btn-outline">取消</a>
                                <button type="button" class="btn btn-secondary" onclick="saveDraft()" style="background: #6c757d; margin-right: 10px;">保存草稿</button>
                                <button type="button" class="btn btn-primary" onclick="nextStep()">下一步</button>
                            </div>
                        </form>
                    </div>

                    <!-- 第二步：选择政策包 -->
                    <div id="step2-content" style="display: none;">
                        <form id="policyForm">
                            <!-- 政策包选择 -->
                            <div class="form-section">
                                <h3 class="section-title">选择政策包</h3>
                                <div class="form-row" style="margin-bottom: 20px;">
                                    <div class="form-group">
                                        <label class="form-label required">政策生效日期</label>
                                        <div style="display: flex; gap: 10px; align-items: center;">
                                            <input type="date" class="form-input" style="flex: 1;" required>
                                            <span>至</span>
                                            <input type="date" class="form-input" style="flex: 1;" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="policy-packages-container">
                                    <!-- A级代理商政策包 -->
                                    <div class="package-card" onclick="selectPackage('A-LEVEL', this)">
                                        <input type="radio" name="policyPackage" value="A-LEVEL" id="package-a" tabindex="-1" style="position: absolute; top: 15px; right: 15px; pointer-events:none;">
                                        <div class="package-header">
                                            <div class="package-title">A级代理商政策包</div>
                                            <div class="package-badge badge-standard">优惠费率</div>
                                        </div>
                                        <div class="package-desc">适用于大型代理商，提供最优惠的费率政策</div>
                                        <div class="fee-structure">
                                            <div class="fee-column">
                                                <div class="fee-column-title">交易分佣</div>
                                                <div class="fee-item">
                                                    <span class="fee-name">外贸收款</span>
                                                    <span class="fee-type">底价分佣</span>
                                                    <span class="fee-value commission">0.5%</span>
                                                </div>
                                                <div class="fee-item">
                                                    <span class="fee-name">跨境电商</span>
                                                    <span class="fee-type">底价分佣</span>
                                                    <span class="fee-value commission">0.6%</span>
                                                </div>
                                            </div>
                                            <div class="fee-column">
                                                <div class="fee-column-title">汇率返点</div>
                                                <div class="fee-item">
                                                    <span class="fee-name">USD/CNY</span>
                                                    <span class="fee-type">底价汇率</span>
                                                    <span class="fee-value rebate">+0.05</span>
                                                </div>
                                                <div class="fee-item">
                                                    <span class="fee-name">EUR/CNY</span>
                                                    <span class="fee-type">底价汇率</span>
                                                    <span class="fee-value rebate">+0.08</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- B级代理商政策包 -->
                                    <div class="package-card" onclick="selectPackage('B-LEVEL', this)">
                                        <input type="radio" name="policyPackage" value="B-LEVEL" id="package-b" tabindex="-1" style="position: absolute; top: 15px; right: 15px; pointer-events:none;">
                                        <div class="package-header">
                                            <div class="package-title">B级代理商政策包</div>
                                            <div class="package-badge badge-standard">标准费率</div>
                                        </div>
                                        <div class="package-desc">适用于中型代理商，提供标准的费率政策</div>
                                        <div class="fee-structure">
                                            <div class="fee-column">
                                                <div class="fee-column-title">交易分佣</div>
                                                <div class="fee-item">
                                                    <span class="fee-name">外贸收款</span>
                                                    <span class="fee-type">底价分佣</span>
                                                    <span class="fee-value commission">0.8%</span>
                                                </div>
                                                <div class="fee-item">
                                                    <span class="fee-name">跨境电商</span>
                                                    <span class="fee-type">固定比例</span>
                                                    <span class="fee-value commission">1.5%</span>
                                                </div>
                                            </div>
                                            <div class="fee-column">
                                                <div class="fee-column-title">汇率返点</div>
                                                <div class="fee-item">
                                                    <span class="fee-name">USD/CNY</span>
                                                    <span class="fee-type">固定返点</span>
                                                    <span class="fee-value rebate">0.15%</span>
                                                </div>
                                                <div class="fee-item">
                                                    <span class="fee-name">EUR/CNY</span>
                                                    <span class="fee-type">固定返点</span>
                                                    <span class="fee-value rebate">0.20%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- C级代理商政策包 -->
                                    <div class="package-card" onclick="selectPackage('C-LEVEL', this)">
                                        <input type="radio" name="policyPackage" value="C-LEVEL" id="package-c" tabindex="-1" style="position: absolute; top: 15px; right: 15px; pointer-events:none;">
                                        <div class="package-header">
                                            <div class="package-title">C级代理商政策包</div>
                                            <div class="package-badge badge-standard">基础费率</div>
                                        </div>
                                        <div class="package-desc">适用于小型代理商，提供基础的费率政策</div>
                                        <div class="fee-structure">
                                            <div class="fee-column">
                                                <div class="fee-column-title">交易分佣</div>
                                                <div class="fee-item">
                                                    <span class="fee-name">外贸收款</span>
                                                    <span class="fee-type">固定比例</span>
                                                    <span class="fee-value commission">2.0%</span>
                                                </div>
                                                <div class="fee-item">
                                                    <span class="fee-name">跨境电商</span>
                                                    <span class="fee-type">固定比例</span>
                                                    <span class="fee-value commission">2.5%</span>
                                                </div>
                                            </div>
                                            <div class="fee-column">
                                                <div class="fee-column-title">汇率返点</div>
                                                <div class="fee-item">
                                                    <span class="fee-name">USD/CNY</span>
                                                    <span class="fee-type">固定返点</span>
                                                    <span class="fee-value rebate">0.10%</span>
                                                </div>
                                                <div class="fee-item">
                                                    <span class="fee-name">EUR/CNY</span>
                                                    <span class="fee-type">固定返点</span>
                                                    <span class="fee-value rebate">0.12%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="form-actions">
                                <button type="button" class="btn btn-outline" onclick="prevStep()">上一步</button>
                                <button type="button" class="btn btn-secondary" onclick="saveDraft()">保存草稿</button>
                                <button type="button" class="btn btn-primary" onclick="submitForm()">提交</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化步骤显示
            showStep(1);
        });
        
        // 显示指定步骤
        function showStep(stepNumber) {
            console.log('切换到步骤:', stepNumber);
            
            // 隐藏所有步骤内容
            const allStepContents = document.querySelectorAll('div[id^="step"][id$="-content"]');
            console.log('找到步骤内容元素数量:', allStepContents.length);
            allStepContents.forEach(content => {
                content.style.display = 'none';
                console.log('隐藏:', content.id);
            });
            
            // 更新步骤导航状态
            const allStepItems = document.querySelectorAll('div[id^="step"].step-item');
            console.log('找到步骤导航元素数量:', allStepItems.length);
            allStepItems.forEach(step => {
                step.className = 'step-item inactive';
                console.log('重置状态:', step.id);
            });
            
            // 激活当前步骤
            const currentStepItem = document.getElementById('step' + stepNumber);
            if (currentStepItem) {
                currentStepItem.className = 'step-item active';
                console.log('激活当前步骤:', currentStepItem.id);
            } else {
                console.error('找不到步骤导航元素:', 'step' + stepNumber);
            }
            
            // 显示当前步骤内容
            const currentStepContent = document.getElementById('step' + stepNumber + '-content');
            if (currentStepContent) {
                currentStepContent.style.display = 'block';
                console.log('显示当前步骤内容:', currentStepContent.id);
            } else {
                console.error('找不到步骤内容元素:', 'step' + stepNumber + '-content');
            }
            
            // 如果不是第一步，将前面的步骤标记为已完成
            for (let i = 1; i < stepNumber; i++) {
                const prevStepItem = document.getElementById('step' + i);
                if (prevStepItem) {
                    prevStepItem.className = 'step-item completed';
                    console.log('标记完成:', prevStepItem.id);
                }
            }
            
            // 滚动到顶部
            window.scrollTo(0, 0);
        }
        
        function nextStep() {
            // 验证表单
            const form = document.getElementById('agentForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // 切换到第二步
            showStep(2);
        }

        function prevStep() {
            // 切换到第一步
            showStep(1);
        }

        // 切换开票能力显示
        function toggleInvoiceCapability() {
            const currencySelect = document.getElementById('settlementCurrency');
            const invoiceSection = document.getElementById('invoiceCapabilitySection');
            
            if (currencySelect.value === 'CNY') {
                invoiceSection.style.display = 'block';
            } else {
                invoiceSection.style.display = 'none';
                // 清除选中状态
                const radios = document.querySelectorAll('input[name="invoiceCapability"]');
                radios.forEach(radio => radio.checked = false);
            }
        }

        // 保存草稿功能
        function saveDraft() {
            // 收集表单数据
            const formData = {
                agentName: document.querySelector('input[placeholder="请输入代理商全称"]').value,
                agentShortName: document.querySelector('input[placeholder="请输入代理商简称"]').value,
                contractNumber: document.querySelector('input[placeholder="请输入合同编号"]').value,
                agentType: document.querySelector('select').value,
                status: 'draft',
                createTime: new Date().toISOString().split('T')[0],
                updateTime: new Date().toISOString().split('T')[0]
            };
            
            // 模拟保存草稿（实际项目中会发送到后端）
            console.log('保存草稿数据:', formData);
            
            // 显示成功提示
            alert('草稿保存成功！');
            
            // 跳转回列表页面
            window.location.href = 'psp-agent-simple.html';
        }

        function toggleMode(event, mode) {
            const checkbox = document.getElementById('mode-' + mode);
            const card = event.currentTarget;

            // 切换选中状态
            if (checkbox.checked) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }

            // 显示/隐藏对应的政策选择区域
            if (mode === 'commission') {
                const section = document.getElementById('commission-policy-section');
                section.style.display = checkbox.checked ? 'block' : 'none';
            } else if (mode === 'rebate') {
                const section = document.getElementById('rebate-policy-section');
                section.style.display = checkbox.checked ? 'block' : 'none';
            }
        }

        function selectCommissionPolicy(event, type) {
            // 清除选中状态
            document.querySelectorAll('#commission-policy-section .policy-option').forEach(option => {
                option.classList.remove('selected');
            });

            // 选中当前选项
            event.currentTarget.classList.add('selected');

            // 隐藏所有选项
            document.getElementById('commission-base-options').style.display = 'none';
            document.getElementById('commission-fixed-options').style.display = 'none';

            // 显示对应选项
            if (type === 'base') {
                document.getElementById('commission-base-options').style.display = 'block';
            } else if (type === 'fixed') {
                document.getElementById('commission-fixed-options').style.display = 'block';
            }
        }

        function selectPackage(packageId, element) {
    // 1. 取消所有卡片高亮
    document.querySelectorAll('.package-card').forEach(card => card.classList.remove('selected'));
    // 2. 取消所有radio选中
    document.querySelectorAll('input[name="policyPackage"]').forEach(radio => radio.checked = false);
    // 3. 高亮当前卡片
    if (element) element.classList.add('selected');
    // 4. 选中radio
    const radioId = `package-${packageId.toLowerCase().split('-')[0]}`;
    const radio = document.getElementById(radioId);
    if (radio) radio.checked = true;
}
// 页面加载后自动回显选中卡片
window.addEventListener('DOMContentLoaded', function() {
    const checked = document.querySelector('input[name="policyPackage"]:checked');
    if (checked) {
        const val = checked.value;
        const card = document.querySelector(`.package-card[onclick*='${val}']`);
        if (card) card.classList.add('selected');
    }
});

        function saveDraft() {
            alert('草稿已保存');
        }

        function checkSpecialPolicy(select, type) {
            if (select.value === 'new') {
                let policyType = '';
                if (type === 'base') {
                    policyType = '底价';
                } else if (type === 'rebate') {
                    policyType = '汇率返点';
                } else if (type === 'hybrid-base') {
                    policyType = '混合模式底价';
                } else if (type === 'hybrid-rebate') {
                    policyType = '混合模式汇率返点';
                }

                if (confirm(`当前没有合适的特殊${policyType}政策，是否要新增特殊${policyType}政策？`)) {
                    // 这里可以跳转到新增特殊政策页面
                    alert(`跳转到新增特殊${policyType}政策页面`);
                } else {
                    select.value = '';
                }
            }
        }

        function saveDraft() {
            alert('草稿已保存');
        }

        function submitForm() {
            // 验证第二步表单
            const selectedPackage = document.querySelector('input[name="policyPackage"]:checked');

            if (!selectedPackage) {
                alert('请选择一个政策包');
                return;
            }

            // 提交表单
            alert('代理商创建成功！已选择政策包：' + selectedPackage.value);
            // 这里可以跳转回列表页面
            window.location.href = 'psp-agent-simple.html';
        }
    </script>
</body>
</html>
