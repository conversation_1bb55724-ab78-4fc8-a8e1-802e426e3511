<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策包详情</title>
    <style>
        body {
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 1100px;
            margin: 40px auto;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            padding: 32px 40px 32px 40px;
        }
        .breadcrumb {
            color: #666;
            font-size: 14px;
            margin-bottom: 18px;
        }
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        .tabs {
            display: flex;
            gap: 32px;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 24px;
        }
        .tab {
            font-size: 16px;
            font-weight: 500;
            color: #666;
            padding: 10px 0;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: color 0.2s, border-color 0.2s;
        }
        .tab.active {
            color: #007bff;
            border-bottom: 2px solid #007bff;
        }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px 10px;
            border-bottom: 1px solid #f1f3f4;
            font-size: 14px;
            text-align: left;
        }
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        .btn-detail {
            color: #007bff;
            background: #f5faff;
            border: 1px solid #007bff;
            border-radius: 6px;
            padding: 6px 16px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn-detail:hover {
            background: #e3f2fd;
        }
        .coming-soon {
            text-align: center;
            color: #888;
            font-size: 16px;
            padding: 60px 0 40px 0;
        }
        .back-btn {
            display: inline-block;
            margin-bottom: 18px;
            color: #007bff;
            text-decoration: none;
            font-size: 15px;
        }
        .back-btn:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="psp-agent-simple.html" class="back-btn">← 返回</a>
        <div class="breadcrumb">代理商管理 / 政策包详情</div>
        <div class="page-title">政策包详情</div>
        <div class="tabs">
            <div class="tab active" data-tab="commission">交易分佣</div>
            <div class="tab" data-tab="rebate">汇率返点</div>
            <div class="tab" data-tab="newcustomer">新客分佣</div>
        </div>
        <div class="tab-content active" id="tab-commission">
            <table>
                <thead>
                    <tr>
                        <th>分佣简介</th>
                        <th>产品名称</th>
                        <th>类型</th>
                        <th>分佣方式</th>
                        <th>费用值</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>外贸收款底价分佣</td>
                        <td>外贸收款</td>
                        <td>标准</td>
                        <td>底价分佣</td>
                        <td>0.8%</td>
                        <td>2025-07-30</td>
                        <td><button class="btn-detail">详情</button></td>
                    </tr>
                    <tr>
                        <td>跨境电商底价分佣</td>
                        <td>跨境电商</td>
                        <td>特殊</td>
                        <td>底价分佣</td>
                        <td>0.9%</td>
                        <td>2025-07-30</td>
                        <td><button class="btn-detail">详情</button></td>
                    </tr>
                    <tr>
                        <td>数字货币固定比例</td>
                        <td>数字货币</td>
                        <td>标准</td>
                        <td>固定比例</td>
                        <td>1.5%</td>
                        <td>2025-07-30</td>
                        <td><button class="btn-detail">详情</button></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="tab-content" id="tab-rebate">
            <table>
                <thead>
                    <tr>
                        <th>返点简介</th>
                        <th>货币对</th>
                        <th>类型</th>
                        <th>分佣方式</th>
                        <th>加点/返点值</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>USD/CNY底价汇率</td>
                        <td>USD/CNY</td>
                        <td>标准</td>
                        <td>底价汇率</td>
                        <td>+0.05</td>
                        <td>2025-07-30</td>
                        <td><button class="btn-detail">详情</button></td>
                    </tr>
                    <tr>
                        <td>EUR/CNY底价汇率</td>
                        <td>EUR/CNY</td>
                        <td>特殊</td>
                        <td>底价汇率</td>
                        <td>+0.08</td>
                        <td>2025-07-30</td>
                        <td><button class="btn-detail">详情</button></td>
                    </tr>
                    <tr>
                        <td>GBP/CNY固定返点</td>
                        <td>GBP/CNY</td>
                        <td>标准</td>
                        <td>固定返点</td>
                        <td>0.15%</td>
                        <td>2025-07-30</td>
                        <td><button class="btn-detail">详情</button></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="tab-content" id="tab-newcustomer">
            <div class="coming-soon">新客分佣功能即将上线，敬请期待！</div>
        </div>
    </div>
    <script>
        // Tab切换
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(tc => tc.classList.remove('active'));
                this.classList.add('active');
                document.getElementById('tab-' + this.dataset.tab).classList.add('active');
            });
        });
    </script>
</body>
</html>
