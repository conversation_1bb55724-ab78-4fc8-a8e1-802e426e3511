<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户收费配置</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        body {
            background-color: #f0f2f5;
            color: #333;
            padding: 20px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
        }
        .page-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e8e8e8;
        }
        .page-title {
            font-size: 24px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: 600;
            margin-bottom: 8px;
        }
        .page-subtitle {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
        }
        .breadcrumb {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            margin-bottom: 8px;
        }
        .breadcrumb a {
            color: #1890ff;
            text-decoration: none;
        }
        .form-section {
            margin-bottom: 32px;
        }
        .section-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 16px;
            color: rgba(0, 0, 0, 0.85);
        }
        .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
            align-items: center;
        }
        .form-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .form-item label {
            min-width: 120px;
            text-align: right;
            color: rgba(0, 0, 0, 0.85);
        }
        .form-item input, .form-item select, .form-item textarea {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-item input, .form-item select {
            width: 200px;
        }
        .form-item textarea {
            width: 400px;
            min-height: 60px;
            resize: vertical;
        }
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            border: none;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        .btn-default {
            background-color: #fff;
            border: 1px solid #d9d9d9;
            color: rgba(0, 0, 0, 0.65);
        }
        .btn-default:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        .btn-danger {
            background-color: #ff4d4f;
            color: white;
        }
        .btn-danger:hover {
            background-color: #ff7875;
        }
        .merchant-info-display {
            background: #f0f8ff;
            padding: 12px;
            border-radius: 4px;
            margin-top: 16px;
            border: 1px solid #d6e4ff;
            display: none;
        }
        .merchant-info-item {
            display: inline-block;
            margin-right: 24px;
            color: rgba(0, 0, 0, 0.65);
        }
        .merchant-info-value {
            color: rgba(0, 0, 0, 0.85);
            font-weight: 500;
        }
        
        /* 费用配置样式 */
        .fee-config-section {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            background: #fff;
        }
        .fee-config-header {
            background: #fafafa;
            padding: 16px 20px;
            border-bottom: 1px solid #e8e8e8;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .priority-notice {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 4px;
            padding: 12px 16px;
            margin-bottom: 16px;
            color: #d46b08;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .priority-notice .icon {
            font-size: 16px;
        }
        .fee-config-table-header {
            display: flex;
            background: #f5f5f5;
            border-bottom: 1px solid #e8e8e8;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
        }
        .fee-config-column {
            padding: 12px 16px;
            border-right: 1px solid #e8e8e8;
            text-align: center;
        }
        .fee-config-column:last-child {
            border-right: none;
        }
        .fee-item-column {
            flex: 0.6;
            min-width: 80px;
        }
        .transaction-config-column {
            flex: 3;
            min-width: 380px;
        }
        .charge-config-column {
            flex: 2;
            min-width: 250px;
        }
        .priority-column {
            flex: 0.6;
            min-width: 70px;
        }
        .actions-column {
            flex: 0.8;
            min-width: 100px;
        }
        .fee-config-row {
            display: flex;
            border-bottom: 1px solid #e8e8e8;
            min-height: 80px;
            align-items: center;
        }
        .fee-config-row:last-child {
            border-bottom: none;
        }
        .fee-config-cell {
            padding: 12px 16px;
            border-right: 1px solid #e8e8e8;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .fee-config-cell:last-child {
            border-right: none;
        }
        .fee-config-field {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        .fee-config-field label {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.65);
            margin: 0;
        }
        .fee-config-field select,
        .fee-config-field input {
            padding: 6px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            font-size: 14px;
            width: 100%;
        }
        .fee-config-actions {
            display: flex;
            flex-direction: column;
            gap: 4px;
            align-items: center;
        }
        .fee-item-name {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            text-align: center;
            padding: 8px;
        }
        .priority-input {
            width: 60px !important;
            text-align: center;
            font-weight: 600;
            color: #1890ff;
        }
        .multi-select-container {
            position: relative;
            min-width: 120px;
        }
        .multi-select-display {
            padding: 6px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            background: #fff;
            cursor: pointer;
            min-height: 32px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
        }
        .multi-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #fff;
            border: 1px solid #d9d9d9;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        .multi-select-option {
            padding: 6px 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
        }
        .multi-select-option:hover {
            background: #f5f5f5;
        }
        .multi-select-tag {
            display: inline-block;
            background: #e6f7ff;
            color: #1890ff;
            padding: 1px 4px;
            border-radius: 2px;
            font-size: 11px;
            margin: 1px;
        }
        .save-section {
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e8e8e8;
            text-align: right;
        }
        .back-link {
            color: #1890ff;
            text-decoration: none;
            margin-right: 16px;
        }
        .back-link:hover {
            color: #40a9ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <div class="breadcrumb">
                <a href="计费管理.html">代理商管理</a> / <a href="特殊代理商产品底价.html">代理商底价配置</a> / 新增产品底价
            </div>
            <div class="page-title">新增代理商产品底价配置</div>
            <div class="page-subtitle">为代理商配置个性化的底价</div>
        </div>

        <!-- 基本信息 -->
        <div class="form-section">
            <div class="section-title">基本信息</div>
            <div class="form-row">
                <div class="form-item">
                    <label>代理商ID：</label>
                    <input type="text" id="merchant-account-number" placeholder="请输代理商ID" onblur="loadMerchantInfo()">
                </div>
                <div class="form-item">
                    <label>产品名称：</label>
                    <select id="merchant-product-name">
                        <option value="">请选择产品</option>
                        <option value="B2C-电商收款">B2C-电商收款</option>
                        <option value="B2C-开发者平台收款">B2C-开发者平台收款</option>
                        <option value="B2C-广告联盟收款">B2C-广告联盟收款</option>
                        <option value="B2B-外贸收款">B2B-外贸收款</option>
                        <option value="B2B-物流收款">B2B-物流收款</option>
                        <option value="人民币结汇">人民币结汇</option>
                        <option value="全球付款">全球付款</option>
                        <option value="全球结汇">本地付款</option>
                    </select>
                </div>
            </div>

            <!-- 商户信息显示 -->
            <div id="merchant-info-display" class="merchant-info-display">
                <div class="merchant-info-item">
                    <span>代理商名称：</span>
                    <span id="merchant-name-display" class="merchant-info-value"></span>
                </div>
                <div class="merchant-info-item">
                    <span>激活状态：</span>
                    <span id="merchant-status" class="merchant-info-value"></span>
                </div>
            </div>

            <!-- 备注说明 -->
            <div class="form-row" style="margin-top: 16px;">
                <div class="form-item">
                    <label>备注说明：</label>
                    <textarea id="merchant-remark" placeholder="请输入备注说明..."></textarea>
                </div>
            </div>

            <!-- 交易手续费 -->
            <div class="fee-section" style="margin-bottom: 30px; border-bottom: 4px solid #f0f0f0; padding-bottom: 20px;">
                <div class="fee-section-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3 style="margin: 0; font-size: 18px; color: #333;">交易手续费</h3>
                    <button class="btn btn-primary" onclick="addFeeConfigRow()">新增配置</button>
                </div>
                
                <!-- 交易手续费展示项目 -->
                <div style="margin-bottom: 20px;">
                    <!-- 交易手续费项目 1 -->
                    <div class="transaction-fee-item" style="display: flex; align-items: center; padding: 16px; border: 1px solid #e0e0e0; border-radius: 6px; margin-bottom: 12px; background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收款类型</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="全球收款" selected>全球收款</option>
                                <option value="本地收款">本地收款</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收款币种</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="USD" selected>USD</option>
                                <option value="EUR">EUR</option>
                                <option value="GBP">GBP</option>
                                <option value="HKD">HKD</option>
                                <option value="JPY">JPY</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">银行</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="HSBC" selected>HSBC</option>
                                <option value="DBS">DBS</option>
                                <option value="BOC">BOC</option>
                                <option value="ICBC">ICBC</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">费率(%)</label>
                            <input type="number" step="0.01" value="0.8" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 0.6; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">优先级</label>
                            <input type="number" value="5" min="1" max="10" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px; text-align: center;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 1.5; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">备注</label>
                            <input type="text" value="标准全球收款费率" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 0.3; padding: 0 12px;">
                            <button class="btn btn-small btn-danger" style="padding: 4px 8px; font-size: 12px;">删除</button>
                        </div>
                    </div>
                    
                    <!-- 交易手续费项目 2 -->
                    <div class="transaction-fee-item" style="display: flex; align-items: center; padding: 16px; border: 1px solid #e0e0e0; border-radius: 6px; margin-bottom: 12px; background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收款类型</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="全球收款">全球收款</option>
                                <option value="本地收款" selected>本地收款</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收款币种</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="USD">USD</option>
                                <option value="EUR">EUR</option>
                                <option value="GBP">GBP</option>
                                <option value="HKD" selected>HKD</option>
                                <option value="JPY">JPY</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">银行</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="HSBC">HSBC</option>
                                <option value="DBS" selected>DBS</option>
                                <option value="BOC">BOC</option>
                                <option value="ICBC">ICBC</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">费率(%)</label>
                            <input type="number" step="0.01" value="0.5" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 0.6; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">优先级</label>
                            <input type="number" value="3" min="1" max="10" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px; text-align: center;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 1.5; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">备注</label>
                            <input type="text" value="本地银行优惠费率" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 0.3; padding: 0 12px;">
                            <button class="btn btn-small btn-danger" style="padding: 4px 8px; font-size: 12px;">删除</button>
                        </div>
                    </div>
                    
                    <!-- 交易手续费项目 3 -->
                    <div class="transaction-fee-item" style="display: flex; align-items: center; padding: 16px; border: 1px solid #e0e0e0; border-radius: 6px; margin-bottom: 12px; background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收款类型</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="全球收款" selected>全球收款</option>
                                <option value="本地收款">本地收款</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收款币种</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="USD">USD</option>
                                <option value="EUR" selected>EUR</option>
                                <option value="GBP">GBP</option>
                                <option value="HKD">HKD</option>
                                <option value="JPY">JPY</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">银行</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="HSBC">HSBC</option>
                                <option value="DBS">DBS</option>
                                <option value="BOC" selected>BOC</option>
                                <option value="ICBC">ICBC</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">费率(%)</label>
                            <input type="number" step="0.01" value="0.9" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 0.6; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">优先级</label>
                            <input type="number" value="4" min="1" max="10" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px; text-align: center;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 1.5; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">备注</label>
                            <input type="text" value="欧元收款标准费率" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 0.3; padding: 0 12px;">
                            <button class="btn btn-small btn-danger" style="padding: 4px 8px; font-size: 12px;">删除</button>
                        </div>
                    </div>
                    
                    <!-- 交易手续费项目 4 -->
                    <div class="transaction-fee-item" style="display: flex; align-items: center; padding: 16px; border: 1px solid #e0e0e0; border-radius: 6px; margin-bottom: 12px; background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收款类型</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="全球收款">全球收款</option>
                                <option value="本地收款" selected>本地收款</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收款币种</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="USD">USD</option>
                                <option value="EUR">EUR</option>
                                <option value="GBP" selected>GBP</option>
                                <option value="HKD">HKD</option>
                                <option value="JPY">JPY</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">银行</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="HSBC">HSBC</option>
                                <option value="DBS">DBS</option>
                                <option value="BOC">BOC</option>
                                <option value="ICBC" selected>ICBC</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">费率(%)</label>
                            <input type="number" step="0.01" value="0.7" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 0.6; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">优先级</label>
                            <input type="number" value="2" min="1" max="10" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px; text-align: center;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 1.5; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">备注</label>
                            <input type="text" value="英镑本地收款优惠" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 0.3; padding: 0 12px;">
                            <button class="btn btn-small btn-danger" style="padding: 4px 8px; font-size: 12px;">删除</button>
                        </div>
                    </div>
                    
                    <!-- 交易手续费项目 5 -->
                    <div class="transaction-fee-item" style="display: flex; align-items: center; padding: 16px; border: 1px solid #e0e0e0; border-radius: 6px; margin-bottom: 12px; background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收款类型</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="全球收款">全球收款</option>
                                <option value="本地收款">本地收款</option>
                                <option value="全部" selected>全部</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收款币种</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="USD">USD</option>
                                <option value="EUR">EUR</option>
                                <option value="GBP">GBP</option>
                                <option value="HKD">HKD</option>
                                <option value="JPY">JPY</option>
                                <option value="全部" selected>全部</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">银行</label>
                            <select style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                                <option value="HSBC">HSBC</option>
                                <option value="DBS">DBS</option>
                                <option value="BOC">BOC</option>
                                <option value="ICBC">ICBC</option>
                                <option value="全部" selected>全部</option>
                            </select>
                        </div>
                        <div class="transaction-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">费率(%)</label>
                            <input type="number" step="0.01" value="1.0" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 0.6; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">优先级</label>
                            <input type="number" value="1" min="1" max="10" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px; text-align: center;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 1.5; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">备注</label>
                            <input type="text" value="默认兜底费率" style="width: 100%; padding: 6px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                        </div>
                        <div class="transaction-fee-field" style="flex: 0.3; padding: 0 12px;">
                            <button class="btn btn-small btn-danger" style="padding: 4px 8px; font-size: 12px;">删除</button>
                        </div>
                    </div>
                </div>
            </div>

        <!-- 风险处理费展示 -->
        <div class="form-section" style="margin-bottom: 40px; border-bottom: 3px solid #e8e8e8; padding-bottom: 32px;">
            <div class="section-title" style="margin-bottom: 20px; font-size: 18px; color: #333; border-left: 4px solid #1890ff; padding-left: 12px;">
                风险处理费
                <span style="margin-left: 16px; font-size: 14px; color: #999; font-weight: normal;">(当前费用项不支持分佣，也不支持特殊定价)</span>
            </div>
            
            <div class="risk-fee-container" style="border: 1px solid #e8e8e8; border-radius: 8px; background: #fafafa; padding: 20px;">
                <!-- 风险处理费项目 1 -->
                <div class="risk-fee-item" style="display: flex; align-items: center; padding: 16px; border: 1px solid #e0e0e0; border-radius: 6px; margin-bottom: 12px; background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <div class="risk-fee-field" style="flex: 1; padding: 0 12px;">
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收款币种</label>
                        <div style="font-size: 14px; color: #333; font-weight: 500;">USD</div>
                    </div>
                    <div class="risk-fee-field" style="flex: 1; padding: 0 12px;">
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">汇款国家</label>
                        <div style="font-size: 14px; color: #333; font-weight: 500;">俄罗斯</div>
                    </div>
                    <div class="risk-fee-field" style="flex: 1; padding: 0 12px;">
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">汇款银行</label>
                        <div style="font-size: 14px; color: #333; font-weight: 500;">俄罗斯联邦储蓄银行</div>
                    </div>
                    <div class="risk-fee-field" style="flex: 1; padding: 0 12px;">
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收费</label>
                        <div style="font-size: 14px; color: #e74c3c; font-weight: 600;">2.5%</div>
                    </div>
                    <div class="risk-fee-field" style="flex: 0.6; padding: 0 12px;">
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">优先级</label>
                        <div style="font-size: 14px; color: #1890ff; font-weight: 600; text-align: center;">10</div>
                    </div>
                    <div class="risk-fee-field" style="flex: 1.5; padding: 0 12px;">
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">备注</label>
                        <div style="font-size: 14px; color: #666;">高风险国家额外风险处理费</div>
                    </div>
                </div>
                    
                    <!-- 风险处理费项目 2 -->
                <div class="risk-fee-item" style="display: flex; align-items: center; padding: 16px; border: 1px solid #e0e0e0; border-radius: 6px; margin-bottom: 12px; background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <div class="risk-fee-field" style="flex: 1; padding: 0 12px;">
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收款币种</label>
                        <div style="font-size: 14px; color: #333; font-weight: 500;">EUR</div>
                    </div>
                    <div class="risk-fee-field" style="flex: 1; padding: 0 12px;">
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">汇款国家</label>
                        <div style="font-size: 14px; color: #333; font-weight: 500;">俄罗斯</div>
                    </div>
                    <div class="risk-fee-field" style="flex: 1; padding: 0 12px;">
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">汇款银行</label>
                        <div style="font-size: 14px; color: #333; font-weight: 500;">俄罗斯对外贸易银行</div>
                    </div>
                    <div class="risk-fee-field" style="flex: 1; padding: 0 12px;">
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收费</label>
                        <div style="font-size: 14px; color: #e74c3c; font-weight: 600;">3.0%</div>
                    </div>
                    <div class="risk-fee-field" style="flex: 0.6; padding: 0 12px;">
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">优先级</label>
                        <div style="font-size: 14px; color: #1890ff; font-weight: 600; text-align: center;">9</div>
                    </div>
                    <div class="risk-fee-field" style="flex: 1.5; padding: 0 12px;">
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">备注</label>
                        <div style="font-size: 14px; color: #666;">欧元高风险地区处理费</div>
                    </div>
                </div>
                    
                    <!-- 风险处理费项目 3 -->
                    <div class="risk-fee-item" style="display: flex; align-items: center; padding: 12px; border: 1px solid #e0e0e0; border-radius: 4px; background: white;">
                        <div class="risk-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收款币种</label>
                            <div style="font-size: 14px; color: #333; font-weight: 500;">GBP</div>
                        </div>
                        <div class="risk-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">汇款国家</label>
                            <div style="font-size: 14px; color: #333; font-weight: 500;">俄罗斯</div>
                        </div>
                        <div class="risk-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">汇款银行</label>
                            <div style="font-size: 14px; color: #333; font-weight: 500;">俄罗斯国家银行</div>
                        </div>
                        <div class="risk-fee-field" style="flex: 1; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">收费</label>
                            <div style="font-size: 14px; color: #e74c3c; font-weight: 600;">2.8%</div>
                        </div>
                        <div class="risk-fee-field" style="flex: 0.6; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">优先级</label>
                            <div style="font-size: 14px; color: #1890ff; font-weight: 600; text-align: center;">8</div>
                        </div>
                        <div class="risk-fee-field" style="flex: 1.5; padding: 0 12px;">
                            <label style="display: block; font-size: 12px; color: #666; margin-bottom: 4px;">备注</label>
                            <div style="font-size: 14px; color: #666;">英镑高风险地区处理费</div>
                        </div>
                    </div>
                </div>
            </div>
            
            


        <!-- 保存按钮 -->
        <div class="save-section">
            <a href="计费管理.html" class="back-link">← 返回列表</a>
            <button class="btn btn-default" style="margin-right: 12px;" onclick="resetForm()">重置</button>
            <button class="btn btn-primary" onclick="saveMerchantPricing()">保存配置</button>
        </div>
    </div>

    <script>
        let feeConfigRowIndex = 0;
        
        // 商户信息加载
        function loadMerchantInfo() {
            const accountNumber = document.getElementById('merchant-account-number').value;
            if (accountNumber) {
                const merchantInfo = {
                    'M202503001': { name: '上海润渠科技有限公司', registerTime: '2023-01-15', status: '已激活' },
                    'M202503002': { name: '北京智汇通科技有限公司', registerTime: '2023-02-20', status: '已激活' },
                    'M202503003': { name: '广州环球贸易有限公司', registerTime: '2023-03-10', status: '已激活' }
                };
                
                const info = merchantInfo[accountNumber];
                if (info) {
                    document.getElementById('merchant-name-display').textContent = info.name;
                    document.getElementById('merchant-register-time').textContent = info.registerTime;
                    document.getElementById('merchant-status').textContent = info.status;
                    document.getElementById('merchant-info-display').style.display = 'block';
                } else {
                    document.getElementById('merchant-info-display').style.display = 'none';
                    alert('未找到该商户信息');
                }
            }
        }

        // 添加费用配置行
        function addFeeConfigRow() {
            const container = document.getElementById('fee-config-rows-container');
            const configRow = document.createElement('div');
            configRow.className = 'fee-config-row';
            configRow.id = `fee-config-row-${feeConfigRowIndex}`;
            
            // 设置默认的费用类型为比例费

            configRow.innerHTML = `
                <!-- 费用项列 -->
                <div class="fee-config-cell fee-item-column">
                    <div class="fee-item-name">交易手续费</div>
                </div>

                <!-- 交易手续费配置项列 -->
                <div class="fee-config-cell transaction-config-column">
                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                        <div class="fee-config-field" style="flex: 1; min-width: 100px;">
                            <label>收款类型</label>
                            <div class="multi-select-container">
                                <div class="multi-select-display" onclick="togglePaymentTypeDropdown('fee-config-row-${feeConfigRowIndex}')">
                                    <span class="selected-payment-types">请选择</span>
                                    <span>▼</span>
                                </div>
                                <div class="multi-select-dropdown" id="payment-type-dropdown-fee-config-row-${feeConfigRowIndex}">
                                    <div class="multi-select-option">
                                        <input type="checkbox" value="all" onchange="toggleAllPaymentTypes(this, 'fee-config-row-${feeConfigRowIndex}')">
                                        <span>全部</span>
                                    </div>
                                    <div class="multi-select-option">
                                        <input type="checkbox" value="全球收款" onchange="updatePaymentTypeSelection('fee-config-row-${feeConfigRowIndex}')">
                                        <span>全球收款</span>
                                    </div>
                                    <div class="multi-select-option">
                                        <input type="checkbox" value="本地收款" onchange="updatePaymentTypeSelection('fee-config-row-${feeConfigRowIndex}')">
                                        <span>本地收款</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fee-config-field" style="flex: 1; min-width: 100px;">
                            <label>收款币种</label>
                            <div class="multi-select-container">
                                <div class="multi-select-display" onclick="toggleCurrencyDropdown('fee-config-row-${feeConfigRowIndex}')">
                                    <span class="selected-currencies">请选择</span>
                                    <span>▼</span>
                                </div>
                                <div class="multi-select-dropdown" id="currency-dropdown-fee-config-row-${feeConfigRowIndex}">
                                    <div class="multi-select-option">
                                        <input type="checkbox" value="all" onchange="toggleAllCurrencies(this, 'fee-config-row-${feeConfigRowIndex}')">
                                        <span>全部</span>
                                    </div>
                                    <div class="multi-select-option">
                                        <input type="checkbox" value="HKD" onchange="updateCurrencySelection('fee-config-row-${feeConfigRowIndex}')">
                                        <span>HKD</span>
                                    </div>
                                    <div class="multi-select-option">
                                        <input type="checkbox" value="USD" onchange="updateCurrencySelection('fee-config-row-${feeConfigRowIndex}')">
                                        <span>USD</span>
                                    </div>
                                    <div class="multi-select-option">
                                        <input type="checkbox" value="EUR" onchange="updateCurrencySelection('fee-config-row-${feeConfigRowIndex}')">
                                        <span>EUR</span>
                                    </div>
                                    <div class="multi-select-option">
                                        <input type="checkbox" value="GBP" onchange="updateCurrencySelection('fee-config-row-${feeConfigRowIndex}')">
                                        <span>GBP</span>
                                    </div>
                                    <div class="multi-select-option">
                                        <input type="checkbox" value="JPY" onchange="updateCurrencySelection('fee-config-row-${feeConfigRowIndex}')">
                                        <span>JPY</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fee-config-field" style="flex: 1; min-width: 100px;">
                            <label>银行</label>
                            <div class="multi-select-container">
                                <div class="multi-select-display" onclick="toggleBankDropdown('fee-config-row-${feeConfigRowIndex}')">
                                    <span class="selected-banks">请选择</span>
                                    <span>▼</span>
                                </div>
                                <div class="multi-select-dropdown" id="bank-dropdown-fee-config-row-${feeConfigRowIndex}">
                                    <div class="multi-select-option">
                                        <input type="checkbox" value="all" onchange="toggleAllBanks(this, 'fee-config-row-${feeConfigRowIndex}')">
                                        <span>全部</span>
                                    </div>
                                    <div class="multi-select-option">
                                        <input type="checkbox" value="DBS" onchange="updateBankSelection('fee-config-row-${feeConfigRowIndex}')">
                                        <span>DBS</span>
                                    </div>
                                    <div class="multi-select-option">
                                        <input type="checkbox" value="HSBC" onchange="updateBankSelection('fee-config-row-${feeConfigRowIndex}')">
                                        <span>HSBC</span>
                                    </div>
                                    <div class="multi-select-option">
                                        <input type="checkbox" value="BOC" onchange="updateBankSelection('fee-config-row-${feeConfigRowIndex}')">
                                        <span>BOC</span>
                                    </div>
                                    <div class="multi-select-option">
                                        <input type="checkbox" value="ICBC" onchange="updateBankSelection('fee-config-row-${feeConfigRowIndex}')">
                                        <span>ICBC</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 收费配置列 -->
                <div class="fee-config-cell charge-config-column">
                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                        <div class="fee-config-field" style="flex: 1; min-width: 80px;">
                            <label>费用类型</label>
                            <select class="charge-fee-type" onchange="toggleChargeFields('fee-config-row-${feeConfigRowIndex}')">
                                <option value="比例费" selected>比例费</option>
                                <option value="固定费">固定费</option>
                            </select>
                        </div>
                        <div class="fee-config-field charge-rate-field" style="flex: 1; min-width: 80px;">
                            <label>费率(%)</label>
                            <input type="number" step="0.01" placeholder="0.00" class="charge-rate">
                        </div>
                        <div class="fee-config-field charge-min-field" style="flex: 1; min-width: 80px;">
                            <label>最低收费</label>
                            <input type="number" step="0.01" placeholder="0.00" class="charge-min-fee">
                        </div>
                        <div class="fee-config-field charge-fixed-field" style="flex: 1; min-width: 80px; display: none;">
                            <label>固定费用</label>
                            <input type="number" step="0.01" placeholder="0.00" class="charge-fixed-fee">
                        </div>
                    </div>
                </div>

                <!-- 优先级列 -->
                <div class="fee-config-cell priority-column">
                    <div class="fee-config-field">
                        <input type="number" class="priority-input" value="${feeConfigRowIndex + 1}" min="100" max="9999" placeholder="1">
                    </div>
                </div>

                <!-- 操作列 -->
                <div class="fee-config-cell actions-column">
                    <div class="fee-config-actions">
                        <button class="btn btn-small btn-danger" onclick="removeFeeConfigRow('fee-config-row-${feeConfigRowIndex}')">删除</button>
                    </div>
                </div>
            `;

            container.appendChild(configRow);
            
            // 初始化新行的费用类型为比例费，并显示相应的字段
            setTimeout(() => {
                const feeTypeSelect = document.querySelector(`#fee-config-row-${feeConfigRowIndex-1} .charge-fee-type`);
                if (feeTypeSelect) {
                    feeTypeSelect.value = '比例费';
                    toggleChargeFields(`fee-config-row-${feeConfigRowIndex-1}`);
                }
            }, 0);
            
            feeConfigRowIndex++;
        }

        function removeFeeConfigRow(rowId) {
            const row = document.getElementById(rowId);
            if (row) {
                row.remove();
            }
        }

        function moveFeeConfigRow(rowId, direction) {
            const row = document.getElementById(rowId);
            const container = row.parentNode;

            if (direction === 'up' && row.previousElementSibling) {
                container.insertBefore(row, row.previousElementSibling);
            } else if (direction === 'down' && row.nextElementSibling) {
                container.insertBefore(row.nextElementSibling, row);
            }
        }

        function toggleChargeFields(rowId) {
            const row = document.getElementById(rowId);
            const feeType = row.querySelector('.charge-fee-type').value;
            const rateField = row.querySelector('.charge-rate-field');
            const minField = row.querySelector('.charge-min-field');
            const fixedField = row.querySelector('.charge-fixed-field');

            // 隐藏所有字段
            rateField.style.display = 'none';
            minField.style.display = 'none';
            fixedField.style.display = 'none';

            // 根据选择显示相应字段
            if (feeType === '比例费') {
                rateField.style.display = 'flex';
                minField.style.display = 'flex';
            } else if (feeType === '固定费') {
                fixedField.style.display = 'flex';
            }
        }

        // 多选功能 - 收款类型
        function togglePaymentTypeDropdown(configId) {
            const dropdown = document.getElementById(`payment-type-dropdown-${configId}`);
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        }

        function toggleAllPaymentTypes(checkbox, configId) {
            const dropdown = document.getElementById(`payment-type-dropdown-${configId}`);
            const typeCheckboxes = dropdown.querySelectorAll('input[type="checkbox"]:not([value="all"])');

            typeCheckboxes.forEach(cb => {
                cb.checked = checkbox.checked;
            });

            updatePaymentTypeSelection(configId);
        }

        function updatePaymentTypeSelection(configId) {
            const dropdown = document.getElementById(`payment-type-dropdown-${configId}`);
            const typeCheckboxes = dropdown.querySelectorAll('input[type="checkbox"]:not([value="all"])');
            const allCheckbox = dropdown.querySelector('input[value="all"]');
            const display = dropdown.parentNode.querySelector('.selected-payment-types');

            const selectedTypes = [];
            typeCheckboxes.forEach(cb => {
                if (cb.checked) {
                    selectedTypes.push(cb.value);
                }
            });

            // 更新"全部"复选框状态
            allCheckbox.checked = selectedTypes.length === typeCheckboxes.length;

            // 更新显示文本
            if (selectedTypes.length === 0) {
                display.textContent = '请选择';
            } else if (selectedTypes.length === typeCheckboxes.length) {
                display.textContent = '全部';
            } else {
                display.innerHTML = selectedTypes.map(type => `<span class="multi-select-tag">${type}</span>`).join('');
            }
        }

        // 多选功能 - 币种
        function toggleCurrencyDropdown(configId) {
            const dropdown = document.getElementById(`currency-dropdown-${configId}`);
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        }

        function toggleAllCurrencies(checkbox, configId) {
            const dropdown = document.getElementById(`currency-dropdown-${configId}`);
            const currencyCheckboxes = dropdown.querySelectorAll('input[type="checkbox"]:not([value="all"])');

            currencyCheckboxes.forEach(cb => {
                cb.checked = checkbox.checked;
            });

            updateCurrencySelection(configId);
        }

        function updateCurrencySelection(configId) {
            const dropdown = document.getElementById(`currency-dropdown-${configId}`);
            const currencyCheckboxes = dropdown.querySelectorAll('input[type="checkbox"]:not([value="all"])');
            const allCheckbox = dropdown.querySelector('input[value="all"]');
            const display = dropdown.parentNode.querySelector('.selected-currencies');

            const selectedCurrencies = [];
            currencyCheckboxes.forEach(cb => {
                if (cb.checked) {
                    selectedCurrencies.push(cb.value);
                }
            });

            // 更新"全部"复选框状态
            allCheckbox.checked = selectedCurrencies.length === currencyCheckboxes.length;

            // 更新显示文本
            if (selectedCurrencies.length === 0) {
                display.textContent = '请选择';
            } else if (selectedCurrencies.length === currencyCheckboxes.length) {
                display.textContent = '全部';
            } else {
                display.innerHTML = selectedCurrencies.map(currency => `<span class="multi-select-tag">${currency}</span>`).join('');
            }
        }

        // 多选功能 - 银行
        function toggleBankDropdown(configId) {
            const dropdown = document.getElementById(`bank-dropdown-${configId}`);
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        }

        function toggleAllBanks(checkbox, configId) {
            const dropdown = document.getElementById(`bank-dropdown-${configId}`);
            const bankCheckboxes = dropdown.querySelectorAll('input[type="checkbox"]:not([value="all"])');

            bankCheckboxes.forEach(cb => {
                cb.checked = checkbox.checked;
            });

            updateBankSelection(configId);
        }

        function updateBankSelection(configId) {
            const dropdown = document.getElementById(`bank-dropdown-${configId}`);
            const bankCheckboxes = dropdown.querySelectorAll('input[type="checkbox"]:not([value="all"])');
            const allCheckbox = dropdown.querySelector('input[value="all"]');
            const display = dropdown.parentNode.querySelector('.selected-banks');

            const selectedBanks = [];
            bankCheckboxes.forEach(cb => {
                if (cb.checked) {
                    selectedBanks.push(cb.value);
                }
            });

            // 更新"全部"复选框状态
            allCheckbox.checked = selectedBanks.length === bankCheckboxes.length;

            // 更新显示文本
            if (selectedBanks.length === 0) {
                display.textContent = '请选择';
            } else if (selectedBanks.length === bankCheckboxes.length) {
                display.textContent = '全部';
            } else {
                display.innerHTML = selectedBanks.map(bank => `<span class="multi-select-tag">${bank}</span>`).join('');
            }
        }

        // 点击外部关闭下拉框
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.multi-select-container')) {
                document.querySelectorAll('.multi-select-dropdown').forEach(dropdown => {
                    dropdown.style.display = 'none';
                });
            }
        });

        // 保存商户收费配置
        function saveMerchantPricing() {
            const accountNumber = document.getElementById('merchant-account-number').value;
            const productName = document.getElementById('merchant-product-name').value;
            const remark = document.getElementById('merchant-remark').value;

            if (!accountNumber || !productName) {
                alert('请填写商户账户号码和产品名称');
                return;
            }

            // 收集费用配置行数据
            const feeConfigs = [];
            const configRows = document.querySelectorAll('#fee-config-rows-container .fee-config-row');

            configRows.forEach(row => {
                // 费用项（固定为交易手续费）
                const feeItem = "交易手续费";

                // 交易手续费配置项 - 收款类型（多选）
                const paymentTypeCheckboxes = row.querySelectorAll('#payment-type-dropdown-' + row.id + ' input[type="checkbox"]:checked:not([value="all"])');
                const selectedPaymentTypes = Array.from(paymentTypeCheckboxes).map(cb => cb.value);

                // 交易手续费配置项 - 币种（多选）
                const currencyCheckboxes = row.querySelectorAll('#currency-dropdown-' + row.id + ' input[type="checkbox"]:checked:not([value="all"])');
                const selectedCurrencies = Array.from(currencyCheckboxes).map(cb => cb.value);

                // 交易手续费配置项 - 银行（多选）
                const bankCheckboxes = row.querySelectorAll('#bank-dropdown-' + row.id + ' input[type="checkbox"]:checked:not([value="all"])');
                const selectedBanks = Array.from(bankCheckboxes).map(cb => cb.value);

                // 收费配置
                const feeType = row.querySelector('.charge-fee-type').value;
                let chargeConfig = null;

                if (feeType) {
                    chargeConfig = { feeType };

                    if (feeType === '比例费') {
                        chargeConfig.rate = row.querySelector('.charge-rate').value;
                        chargeConfig.minFee = row.querySelector('.charge-min-fee').value;
                    } else if (feeType === '固定费') {
                        chargeConfig.fixedFee = row.querySelector('.charge-fixed-fee').value;
                    }
                }

                // 优先级
                const priority = parseInt(row.querySelector('.priority-input').value) || 1;

                // 验证必填项
                if (selectedPaymentTypes.length > 0 && selectedCurrencies.length > 0 && selectedBanks.length > 0 && chargeConfig) {
                    feeConfigs.push({
                        feeItem,
                        paymentTypes: selectedPaymentTypes,
                        currencies: selectedCurrencies,
                        banks: selectedBanks,
                        chargeConfig,
                        priority
                    });
                }
            });

            // 验证至少有一个完整的配置
            if (feeConfigs.length === 0) {
                alert('请至少添加一个完整的费用配置');
                return;
            }

            // 按优先级排序
            feeConfigs.sort((a, b) => b.priority - a.priority);

            // 构建完整的配置数据
            const merchantPricingData = {
                accountNumber,
                productName,
                remark,
                feeConfigs,
                createTime: new Date().toISOString()
            };

            console.log('商户收费配置数据：', merchantPricingData);
            alert('保存成功！');

            // 跳转回列表页面
            window.location.href = '计费管理.html';
        }

        function resetForm() {
            if (confirm('确定要重置表单吗？')) {
                document.getElementById('merchant-account-number').value = '';
                document.getElementById('merchant-product-name').value = '';
                document.getElementById('merchant-remark').value = '';
                document.getElementById('merchant-info-display').style.display = 'none';
                document.getElementById('fee-config-rows-container').innerHTML = '';
                feeConfigRowIndex = 0;
            }
        }

        // 页面加载时添加一个默认配置行
        document.addEventListener('DOMContentLoaded', function() {
            addFeeConfigRow();
            
            // 初始化提示框功能
            initTooltips();
            
            // 初始化产品选择事件
            initProductSelection();
        });
        
        // 初始化提示框功能
        function initTooltips() {
            const tooltipContainers = document.querySelectorAll('.tooltip-container');
            
            tooltipContainers.forEach(container => {
                const tooltip = container.querySelector('.tooltip');
                
                container.addEventListener('mouseenter', () => {
                    tooltip.style.visibility = 'visible';
                    tooltip.style.opacity = '1';
                });
                
                container.addEventListener('mouseleave', () => {
                    tooltip.style.visibility = 'hidden';
                    tooltip.style.opacity = '0';
                });
            });
        }
        
        // 初始化产品选择事件
        function initProductSelection() {
            const productSelect = document.getElementById('merchant-product-name');
            const b2cNotice = document.getElementById('b2c-product-notice');
            
            // 初始检查
            toggleB2CNotice(productSelect.value);
            
            // 添加事件监听器
            productSelect.addEventListener('change', function() {
                toggleB2CNotice(this.value);
            });
            
            // 切换B2C产品说明显示状态
            function toggleB2CNotice(productValue) {
                if (productValue.startsWith('B2C')) {
                    b2cNotice.style.display = 'flex';
                } else {
                    b2cNotice.style.display = 'none';
                }
            }
        }
    </script>
</body>
</html>
