# 代理政策管理系统

## 概述

代理政策管理系统是EUREWAX SaaS平台的核心功能模块，用于管理代理商的产品定价和汇率政策。系统采用分层架构，支持周期性政策发布和版本管理。

## 系统架构

```mermaid
graph TB
    A[代理政策管理系统] --> B[原子配置层]
    A --> C[政策版本层]
    A --> D[发布管理层]
    
    B --> B1[产品底价配置]
    B --> B2[汇率配置]
    
    B1 --> B11[标准底价]
    B1 --> B12[特殊底价]
    
    B2 --> B21[底价汇率]
    B2 --> B22[固定返点]
    
    C --> C1[政策版本创建]
    C --> C2[版本预览]
    C --> C3[版本对比]
    
    D --> D1[政策发布]
    D --> D2[生效管理]
    D --> D3[历史版本]
```

## 业务流程

### 1. 政策创建流程

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant System as 政策系统
    participant Config as 配置模块
    participant Version as 版本管理
    participant Agent as 代理商
    
    Admin->>System: 启动新政策创建
    System->>Config: 配置产品底价
    Config->>Config: 标准底价设置
    Config->>Config: 特殊底价设置
    Config->>System: 底价配置完成
    
    System->>Config: 配置汇率政策
    Config->>Config: 底价汇率设置
    Config->>Config: 固定返点设置
    Config->>System: 汇率配置完成
    
    System->>Version: 创建政策版本
    Version->>Version: 版本预览生成
    Admin->>Version: 审核政策版本
    Version->>System: 版本审核通过
    
    System->>Agent: 发布政策通知
    Agent->>System: 确认政策接收
    System->>Version: 政策正式生效
```

### 2. 政策层级结构

#### 第一层：原子配置
- **产品底价配置**
  - 标准底价：适用于常规业务场景
  - 特殊底价：针对特定代理商的个性化定价
- **汇率配置**
  - 底价汇率：基础汇率加点配置
  - 固定返点：固定比例返点配置

#### 第二层：政策版本
- 将原子配置组合成完整的政策包
- 支持版本号管理和历史追溯
- 提供政策预览和对比功能

#### 第三层：发布管理
- 政策生效时间控制
- 代理商通知机制
- 政策回滚和更新管理

## 政策周期管理

### 发布周期
- **年度政策**：每年发布一次，通常在年初
- **半年度政策**：每半年调整一次，适应市场变化
- **临时政策**：根据业务需要随时发布

### 版本命名规则
```
格式：YYYY.[H/A].V[n]
示例：
- 2024.A.V1 (2024年年度政策第1版)
- 2024.H1.V1 (2024年上半年政策第1版)
- 2024.H2.V2 (2024年下半年政策第2版)
```

## 数据结构设计

### 政策版本表 (policy_versions)
```sql
CREATE TABLE policy_versions (
    id BIGINT PRIMARY KEY,
    version_code VARCHAR(20) NOT NULL,
    version_name VARCHAR(100) NOT NULL,
    policy_type ENUM('ANNUAL', 'SEMI_ANNUAL', 'TEMPORARY'),
    effective_date DATE NOT NULL,
    expiry_date DATE,
    status ENUM('DRAFT', 'PENDING', 'ACTIVE', 'EXPIRED'),
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 产品底价配置表 (product_base_prices)
```sql
CREATE TABLE product_base_prices (
    id BIGINT PRIMARY KEY,
    policy_version_id BIGINT NOT NULL,
    product_type VARCHAR(50) NOT NULL,
    product_name VARCHAR(100) NOT NULL,
    price_type ENUM('STANDARD', 'SPECIAL'),
    agent_id BIGINT NULL,
    base_price DECIMAL(10,4) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    effective_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (policy_version_id) REFERENCES policy_versions(id)
);
```

### 汇率配置表 (exchange_rate_configs)
```sql
CREATE TABLE exchange_rate_configs (
    id BIGINT PRIMARY KEY,
    policy_version_id BIGINT NOT NULL,
    currency_pair VARCHAR(10) NOT NULL,
    rate_type ENUM('BASE_RATE', 'FIXED_REBATE'),
    rate_value DECIMAL(8,6) NOT NULL,
    agent_id BIGINT NULL,
    effective_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (policy_version_id) REFERENCES policy_versions(id)
);
```

## 业务规则

### 1. 政策继承规则
- 新政策版本可以继承上一版本的配置
- 支持部分配置更新，未更新项保持原值
- 特殊配置优先级高于标准配置

### 2. 生效规则
- 政策必须设置明确的生效时间
- 同一时间只能有一个政策版本生效
- 新政策生效时，旧政策自动失效

### 3. 权限控制
- 只有授权管理员可以创建和发布政策
- 代理商只能查看适用于自己的政策内容
- 政策发布需要审核流程

## 系统功能模块

### 1. 政策配置模块
- **产品底价管理**
  - 标准底价配置界面
  - 特殊底价配置界面
  - 批量导入/导出功能
  
- **汇率管理**
  - 底价汇率配置界面
  - 固定返点配置界面
  - 汇率历史查询

### 2. 版本管理模块
- **版本创建**
  - 政策版本向导
  - 配置项选择和组合
  - 版本预览和验证
  
- **版本对比**
  - 版本差异对比
  - 变更影响分析
  - 版本回滚功能

### 3. 发布管理模块
- **发布流程**
  - 政策审核工作流
  - 发布时间调度
  - 代理商通知机制
  
- **监控统计**
  - 政策使用情况统计
  - 代理商反馈收集
  - 政策效果分析

## 用户界面设计

### 1. 政策总览页面
- 当前生效政策展示
- 政策版本时间线
- 快速操作入口

### 2. 配置页面
- 分步骤配置向导
- 实时预览功能
- 配置验证提示

### 3. 版本管理页面
- 版本列表和状态
- 版本对比工具
- 发布管理控制台

## 技术实现要点

### 1. 数据一致性
- 使用数据库事务确保配置完整性
- 实现配置项的关联验证
- 提供数据回滚机制

### 2. 性能优化
- 政策数据缓存策略
- 批量操作优化
- 异步处理机制

### 3. 安全控制
- 操作日志记录
- 权限验证机制
- 数据加密存储

## 扩展功能

### 1. 智能推荐
- 基于历史数据的政策推荐
- 市场趋势分析
- 竞争对手价格监控

### 2. 自动化流程
- 政策自动生效
- 异常情况自动处理
- 智能通知推送

### 3. 数据分析
- 政策效果分析报告
- 代理商满意度调查
- 业务指标监控

## 总结

代理政策管理系统通过分层架构设计，实现了从原子配置到政策版本的完整管理流程。系统支持周期性政策发布，提供灵活的配置选项和强大的版本管理功能，确保代理商政策的有效管理和执行。
