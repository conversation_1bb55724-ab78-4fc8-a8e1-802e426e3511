# 代理产品文档 - 标准版本

## 1. 产品概述

### 1.1 业务背景

**拓客方式现状**

- 目前主要通过渠道代理方式进行客户拓展
- **优势**：节约销售成本，快速扩大市场覆盖
- **劣势**：收益会被代理压缩，利润空间相对较低

### 1.2 产品定位

为PSP租户提供代理分销能力的管理平台，通过标准化的代理管理体系，实现高效的渠道拓客和收益分配。

### 1.3 核心价值

- **降本增效**：通过代理网络降低直销成本
- **快速扩张**：利用代理资源快速覆盖目标市场
- **精细管理**：提供完整的代理管理和政策配置能力
- **收益平衡**：通过灵活的返佣模式平衡各方利益

## 2. 产品功能

### 2.1 主要功能模块

```mermaid
graph TD
    A[代理产品-标准版] --> B[代理管理能力]
    A --> C[拓客能力]
    A --> D[定价能力]
    A --> E[返佣能力]
  
    B --> B1[代理商基本信息]
    B --> B2[代理商签约产品]
    B --> B3[代理政策配置]
  
    C --> C1[获取链接]
    C --> C3[业绩统计]
  
    D --> D1[底价模式]
    D --> D2[汇率返点模式]
    D --> D3[混合模式]
  
    E --> E1[交易分佣]
    E --> E2[汇率返点]
    E --> E3[账单结算]
```

### 2.2 代理等级体系

| 代理等级 | 等级描述   | 适用场景               | 政策特点           |
| -------- | ---------- | ---------------------- | ------------------ |
| A级      | 高等级代理 | 大型机构、战略合作伙伴 | 混合模式，最优政策 |
| B级      | 中等级代理 | 中型机构、专业服务商   | 底价或返点模式     |
| C级      | 低等级代理 | 小型机构、个人代理     | 标准底价模式       |

## 3. 代理政策模式

### 3.1 底价模式

**模式描述**：为代理设定产品底价，代理可在底价基础上自主定价，超出部分全部归代理所有。

**适用场景**：

- 新代理商培育期
- 标准化产品推广
- 价格敏感型市场

**收益计算**：

```
代理收益 = 客户支付金额 - 代理底价
```

**示例**：

- 代理底价：0.5%
- 客户报价：0.8%
- 代理收益：0.3%

### 3.2 汇率返点模式

**模式描述**：客户按标准报价支付，根据币种对给予代理固定比例的汇率返点。

**适用场景**：

- 汇率敏感业务
- 大额交易场景
- 需要统一定价的市场

**收益计算**：

```
代理收益 = 汇率收益 × 返点比例
```

**示例**：

- 汇率收益：1000 USD
- 返点比例：50%
- 代理收益：500 USD

### 3.3 混合模式（底价+汇率返点）

**模式描述**：同时享受底价差价和汇率返点，适用于高等级代理商。

**适用场景**：

- 战略合作伙伴
- 大规模交易代理
- 长期合作关系

**收益计算**：

```
代理收益 = (客户支付金额 - 代理底价) + (汇率收益 × 返点比例)
```

## 4. 业务流程

### 4.1 代理商入驻流程

```mermaid
flowchart TD
    A[代理商申请] --> B[资质审核]
    B --> C{审核通过?}
    C -->|是| D[确定代理等级]
    C -->|否| E[申请驳回]
  
    D --> F[选择适配政策]
    F --> G[线下签约]
    G --> H[线上录入基本信息]
    H --> I[配置签约产品]
    I --> J[配置代理政策]
    J --> K[代理商激活]
    K --> L[开始推广]
```

### 4.2 政策配置流程

```mermaid
flowchart TD
    A[开始配置] --> B[选择代理政策模式]
    B --> C{选择模式}
  
    C -->|底价模式| D[配置产品底价]
    C -->|汇率返点模式| E[配置汇率返点]
    C -->|混合模式| F[配置底价+返点]
  
    D --> G{标准配置?}
    E --> H{标准配置?}
    F --> I{标准配置?}
  
    G -->|是| J[应用标准底价]
    G -->|否| K[自定义底价]
  
    H -->|是| L[应用标准返点]
    H -->|否| M[自定义返点]
  
    I -->|是| N[应用标准混合政策]
    I -->|否| O[自定义混合政策]
  
    J --> P[保存配置]
    K --> P
    L --> P
    M --> P
    N --> P
    O --> P
  
    P --> Q[配置完成]
```

### 4.3 交易分佣流程

```mermaid
sequenceDiagram
    participant C as 客户
    participant A as 代理商
    participant P as PSP平台
    participant S as 结算系统
  
    C->>A: 发起交易需求
    A->>P: 提交交易订单
    P->>P: 执行交易处理
    P->>S: 计算代理分佣
    S->>S: 生成分佣记录
    S->>A: 分佣入账
    S->>P: 返回结算结果
    P->>A: 交易完成通知
```

## 5. 系统逻辑

### 5.1 配置优先级

```mermaid
graph TD
    A[代理政策配置] --> B{是否有特殊配置?}
    B -->|是| C[使用特殊配置]
    B -->|否| D[使用标准配置]
  
    D --> E{代理等级}
    E -->|A级| F[A级标准政策]
    E -->|B级| G[B级标准政策]
    E -->|C级| H[C级标准政策]
  
    C --> I[特殊底价配置]
    C --> J[特殊返点配置]
  
    F --> K[政策生效]
    G --> K
    H --> K
    I --> K
    J --> K
```

### 5.2 标准配置管理

**标准底价配置**

- 按代理等级设定不同的标准底价
- 按产品类型设定差异化底价
- 支持批量配置和个性化调整

**标准汇率返点配置**

- 按币种对设定标准返点比例
- 按代理等级设定不同返点档次
- 支持动态调整和历史版本管理

### 5.3 特殊配置逻辑

**触发条件**

- 代理商交易规模达到阈值
- 战略合作协议要求
- 市场竞争需要

**配置方式**

- 针对特定代理商的个性化配置
- 优先级高于标准配置
- 需要审批流程确认
- 时性
- **服务质量**：提供优质的代理商服务和支持
- **持续优化**：根据市场反馈持续优化产品功能

---

*本文档版本：v1.0*
*最后更新：2025年1月*
*文档状态：草案*
