<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理商激活</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .activation-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
            width: 100%;
            max-width: 500px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            margin: 0 auto 20px;
        }

        .title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .form-label.required::after {
            content: " *";
            color: #e74c3c;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-control:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }

        .verification-group {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        .verification-input {
            flex: 1;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            padding: 8px 16px;
            font-size: 12px;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-secondary:disabled {
            background: #e9ecef;
            color: #6c757d;
            cursor: not-allowed;
        }

        .btn-full {
            width: 100%;
        }

        .password-rules {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-top: 8px;
            font-size: 12px;
            color: #6c757d;
        }

        .password-rules ul {
            margin: 0;
            padding-left: 16px;
        }

        .password-rules li {
            margin-bottom: 4px;
        }

        .error-message {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .success-message {
            color: #27ae60;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .countdown {
            color: #f39c12;
            font-size: 12px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin: 0 10px;
            position: relative;
        }

        .step.active {
            background: #667eea;
            color: white;
        }

        .step.completed {
            background: #27ae60;
            color: white;
        }

        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background: #e9ecef;
            transform: translateY(-50%);
        }

        .step:last-child::after {
            display: none;
        }

        .step.completed::after {
            background: #27ae60;
        }
    </style>
</head>
<body>
    <div class="activation-container">
        <!-- 头部 -->
        <div class="header">
            <div class="logo">🏢</div>
            <h1 class="title">代理商账户激活</h1>
            <p class="subtitle">完成以下步骤激活您的代理商账户</p>
        </div>

       
        <!-- 激活表单 -->
        <form id="activationForm">
            <!-- 基本信息 -->
            <div class="form-group">
                <label class="form-label required">用户名</label>
                <input type="text" class="form-control" id="username" value="guangzhou_trade" placeholder="请输入用户名">
                <div class="error-message" id="usernameError">用户名不能为空</div>
            </div>

            <div class="form-group">
                <label class="form-label">邮箱</label>
                <input type="email" class="form-control" id="email" value="<EMAIL>" disabled>
            </div>

            <div class="form-group">
                <label class="form-label">手机号</label>
                <input type="tel" class="form-control" id="phone" value="+86 13888888888" disabled>
            </div>

            <!-- 手机验证 -->
            <div class="form-group">
                <label class="form-label required">手机验证码</label>
                <div class="verification-group">
                    <div class="verification-input">
                        <input type="text" class="form-control" id="verificationCode" placeholder="请输入6位验证码" maxlength="6">
                        <div class="error-message" id="codeError">请输入正确的验证码</div>
                        <div class="success-message" id="codeSuccess">验证码验证成功</div>
                    </div>
                    <button type="button" class="btn btn-secondary" id="sendCodeBtn" onclick="sendVerificationCode()">发送验证码</button>
                </div>
            </div>

            <!-- 密码设置 -->
            <div class="form-group">
                <label class="form-label required">设置密码</label>
                <input type="password" class="form-control" id="password" placeholder="请设置登录密码">
                <div class="password-rules">
                    <strong>密码规则：</strong>
                    <ul>
                        <li>长度8-20位字符</li>
                        <li>至少包含大写字母、小写字母、数字中的两种</li>
                        <li>可包含特殊字符 !@#$%^&*</li>
                        <li>不能包含空格</li>
                    </ul>
                </div>
                <div class="error-message" id="passwordError">密码格式不正确</div>
            </div>

            <div class="form-group">
                <label class="form-label required">确认密码</label>
                <input type="password" class="form-control" id="confirmPassword" placeholder="请再次输入密码">
                <div class="error-message" id="confirmPasswordError">两次输入的密码不一致</div>
            </div>

            <!-- 提交按钮 -->
            <button type="submit" class="btn btn-primary btn-full" id="submitBtn">激活账户</button>
        </form>
    </div>

    <script>
        let countdown = 0;
        let countdownTimer = null;

        // 发送验证码
        function sendVerificationCode() {
            const btn = document.getElementById('sendCodeBtn');
            
            // 开始倒计时
            countdown = 60;
            btn.disabled = true;
            updateCountdown();
            
            countdownTimer = setInterval(() => {
                countdown--;
                if (countdown <= 0) {
                    clearInterval(countdownTimer);
                    btn.disabled = false;
                    btn.textContent = '发送验证码';
                } else {
                    updateCountdown();
                }
            }, 1000);

            // 模拟发送验证码
            console.log('验证码已发送到手机：+86 13888888888');
            alert('验证码已发送，请查收短信');
        }

        function updateCountdown() {
            const btn = document.getElementById('sendCodeBtn');
            btn.innerHTML = `<span class="countdown">${countdown}s</span> 重新发送`;
        }

        // 验证码验证
        function validateVerificationCode() {
            const code = document.getElementById('verificationCode').value;
            const errorEl = document.getElementById('codeError');
            const successEl = document.getElementById('codeSuccess');
            
            if (code === '123456') { // 模拟正确验证码
                errorEl.style.display = 'none';
                successEl.style.display = 'block';
                updateStep(2);
                return true;
            } else if (code.length === 6) {
                errorEl.style.display = 'block';
                successEl.style.display = 'none';
                return false;
            }
            return false;
        }

        // 密码验证
        function validatePassword() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const passwordError = document.getElementById('passwordError');
            const confirmPasswordError = document.getElementById('confirmPasswordError');
            
            // 密码规则验证
            const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d!@#$%^&*]{8,20}$/;
            
            let isValid = true;
            
            if (!passwordRegex.test(password)) {
                passwordError.style.display = 'block';
                isValid = false;
            } else {
                passwordError.style.display = 'none';
            }
            
            if (password !== confirmPassword && confirmPassword.length > 0) {
                confirmPasswordError.style.display = 'block';
                isValid = false;
            } else {
                confirmPasswordError.style.display = 'none';
            }
            
            if (isValid && password.length > 0 && confirmPassword.length > 0) {
                updateStep(3);
            }
            
            return isValid;
        }

        // 更新步骤状态
        function updateStep(step) {
            for (let i = 1; i <= 3; i++) {
                const stepEl = document.getElementById(`step${i}`);
                if (i < step) {
                    stepEl.className = 'step completed';
                } else if (i === step) {
                    stepEl.className = 'step active';
                } else {
                    stepEl.className = 'step';
                }
            }
        }

        // 表单提交
        document.getElementById('activationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const code = document.getElementById('verificationCode').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            // 验证所有字段
            let isValid = true;
            
            if (!username.trim()) {
                document.getElementById('usernameError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('usernameError').style.display = 'none';
            }
            
            if (!validateVerificationCode()) {
                isValid = false;
            }
            
            if (!validatePassword()) {
                isValid = false;
            }
            
            if (isValid) {
                // 模拟激活成功
                alert('激活成功！即将跳转到登录页面...');
                // 这里可以跳转到登录页面
                // window.location.href = 'login.html';
            }
        });

        // 实时验证
        document.getElementById('verificationCode').addEventListener('input', validateVerificationCode);
        document.getElementById('password').addEventListener('input', validatePassword);
        document.getElementById('confirmPassword').addEventListener('input', validatePassword);
        
        // 用户名验证
        document.getElementById('username').addEventListener('input', function() {
            const username = this.value;
            const errorEl = document.getElementById('usernameError');
            
            if (username.trim()) {
                errorEl.style.display = 'none';
                updateStep(1);
            } else {
                errorEl.style.display = 'block';
            }
        });

        // 初始化第一步
        updateStep(1);
    </script>
</body>
</html>
