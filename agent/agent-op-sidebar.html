<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理商管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 250px;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            color: white;
            overflow-y: auto;
            box-shadow: 2px 0 8px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .logo {
            width: 50px;
            height: 50px;
            background: #3498db;
            border-radius: 50%;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
        }

        .company-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .agent-level {
            font-size: 12px;
            color: #bdc3c7;
        }

        /* 导航菜单样式 */
        .nav-menu {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            border-left-color: #3498db;
        }

        .nav-link.active {
            background-color: rgba(52, 152, 219, 0.2);
            border-left-color: #3498db;
        }

        .nav-text {
            font-size: 14px;
        }

        .nav-arrow {
            font-size: 12px;
            transition: transform 0.3s ease;
        }

        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background-color: rgba(0,0,0,0.2);
        }

        .nav-submenu.open {
            max-height: 200px;
        }

        .nav-submenu .nav-link {
            padding-left: 40px;
            font-size: 13px;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: white;
            padding: 15px 30px;
            border-bottom: 1px solid #e0e0e0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
        }

        .content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .page-content {
            display: none;
        }

        .page-content.active {
            display: block;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .card-header {
            padding: 20px 25px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .card-body {
            padding: 25px;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 统计卡片样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
        }

        /* 代理商信息卡片 */
        .agent-info-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
        }

        .agent-avatar {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 20px;
        }

        .agent-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .agent-code {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 15px;
        }

        .invite-link {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .invite-label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 8px;
        }

        .invite-url {
            font-size: 14px;
            word-break: break-all;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .copy-btn {
            background: rgba(255,255,255,0.3);
            border: none;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .status-badge {
            background: #27ae60;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            display: inline-block;
            margin-right: 8px;
        }

        .activation-badge {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            display: inline-block;
        }

        .activation-badge.active {
            background: #3498db;
            color: white;
        }

        .activation-badge.inactive {
            background: #f39c12;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">🏢</div>
                <div class="company-name">上海易也网络科技有限公司</div>
                <div class="agent-level">A级代理商</div>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="#" class="nav-link active" onclick="showPage('overview')">
                        <span class="nav-text">首页概览</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showPage('dashboard')">
                        <span class="nav-text">数据看板</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                        <span class="nav-text">分佣账单</span>
                        <span class="nav-arrow">▼</span>
                    </a>
                    <div class="nav-submenu">
                        <a href="#" class="nav-link" onclick="showPage('bill-records')">
                            <span class="nav-text">账单记录</span>
                        </a>
                        <a href="#" class="nav-link" onclick="showPage('bill-details')">
                            <span class="nav-text">账单明细</span>
                        </a>
                    </div>
                </div>
                
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showPage('settlement-records')">
                        <span class="nav-text">结算记录</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="header">
                <h1 class="header-title" id="pageTitle">首页概览</h1>
            </div>
            
            <div class="content">
                <!-- 首页概览 -->
                <div id="overview" class="page-content active">
                    <!-- 代理商信息卡片 -->
                    <div class="agent-info-card">
                        <div style="display: flex; align-items: flex-start; gap: 20px;">
                            <div class="agent-avatar">🐸</div>
                            <div style="flex: 1;">
                                <div class="agent-name">上海易也网络科技有限公司</div>
                                <div style="margin-bottom: 8px;">
                                </div>
                                <div class="agent-code">代理商id：20092110169372</div>
                                
                                <div class="invite-link">
                                    <div class="invite-label">商户注册链接：<span>https://mp.sand.eurewax.com/#/register?inviteCode=A***  <button class="copy-btn" onclick="copyToClipboard()">📋</button></span></div>
                                    <div class="invite-url">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据统计 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">数据统计（累计）</h3>
                        </div>
                        <div class="card-body">
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-value">156</div>
                                    <div class="stat-label">入网客户数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-value">2,847</div>
                                    <div class="stat-label">入账笔数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-value">$1,245,678</div>
                                    <div class="stat-label">入账GMV(USD)</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-value">$12,456</div>
                                    <div class="stat-label">获取佣金(USD)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据看板 -->
                <div id="dashboard" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">数据看板</h3>
                        </div>
                        <div class="card-body">
                            <p style="text-align: center; color: #7f8c8d; padding: 50px;">数据看板功能开发中...</p>
                        </div>
                    </div>
                </div>

                <!-- 账单记录 -->
                <div id="bill-records" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">分佣账单查询</h3>
                            <button class="btn btn-primary btn-sm">⬇ 下载</button>
                        </div>
                        <div class="card-body">
                            <!-- 搜索表单 -->
                            <div class="search-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>对账单号</label>
                                        <input type="text" class="form-control" placeholder="请输入">
                                    </div>
                                    <div class="form-group">
                                        <label>更新时间</label>
                                        <div class="date-range">
                                            <input type="date" class="form-control">
                                            <span>至</span>
                                            <input type="date" class="form-control">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>结算周期</label>
                                        <div class="date-range">
                                            <input type="month" class="form-control">
                                            <span>至</span>
                                            <input type="month" class="form-control">
                                        </div>
                                    </div>
                                    <div class="form-actions">
                                        <button class="btn btn-primary">查询</button>
                                        <button class="btn btn-secondary">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Tab切换 -->
                            <div style="border-bottom: 1px solid #e0e0e0; margin-bottom: 20px;">
                                <div style="display: flex; gap: 30px;">
                                    <button class="tab-btn active" onclick="switchTab('pending')">
                                        待处理 <span style="background: #e74c3c; color: white; padding: 2px 6px; border-radius: 10px; font-size: 10px; margin-left: 5px;">3</span>
                                    </button>
                                    <button class="tab-btn" onclick="switchTab('all')">全部</button>
                                </div>
                            </div>

                            <!-- 表格 -->
                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>对账单号</th>
                                            <th>结算周期</th>
                                            <th>结算金额</th>
                                            <th>账单状态</th>
                                            <th>能否开票</th>
                                            <th>备注</th>
                                            <th>更新时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="7" class="empty-state">
                                                <div class="empty-icon">📄</div>
                                                <div class="empty-text">暂无数据</div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <div class="pagination">
                                <button class="page-btn" disabled>‹</button>
                                <button class="page-btn active">1</button>
                                <button class="page-btn" disabled>›</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 账单明细 -->
                <div id="bill-details" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">分佣明细查询</h3>
                            <button class="btn btn-primary btn-sm">⬇ 下载</button>
                        </div>
                        <div class="card-body">
                            <!-- 搜索表单 -->
                            <div class="search-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>对账单号</label>
                                        <input type="text" class="form-control" placeholder="请输入">
                                    </div>
                                    <div class="form-group">
                                        <label>结算周期</label>
                                        <div class="date-range">
                                            <input type="month" class="form-control">
                                            <span>至</span>
                                            <input type="month" class="form-control">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>商户账号</label>
                                        <input type="text" class="form-control" placeholder="请输入">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>商户名称</label>
                                        <input type="text" class="form-control" placeholder="请输入">
                                    </div>
                                    <div class="form-group">
                                        <label>分佣金额</label>
                                        <input type="text" class="form-control" placeholder="请输入">
                                    </div>
                                    <div class="form-actions">
                                        <button class="btn btn-primary">查询</button>
                                        <button class="btn btn-secondary">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 表格 -->
                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>对账单号</th>
                                            <th>结算周期</th>
                                            <th>商户账号</th>
                                            <th>商户名称</th>
                                            <th>分佣金额</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="7" class="empty-state">
                                                <div class="empty-icon">📄</div>
                                                <div class="empty-text">暂无数据</div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <div class="pagination">
                                <button class="page-btn" disabled>‹</button>
                                <button class="page-btn active">1</button>
                                <button class="page-btn" disabled>›</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 结算记录 -->
                <div id="settlement-records" class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">提现记录</h3>
                            <button class="btn btn-primary btn-sm">⬇ 下载</button>
                        </div>
                        <div class="card-body">
                            <!-- 搜索表单 -->
                            <div class="search-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>结算订单号</label>
                                        <input type="text" class="form-control" placeholder="请输入">
                                    </div>
                                    <div class="form-group">
                                        <label>结算时间区间</label>
                                        <div class="date-range">
                                            <input type="date" class="form-control">
                                            <span>至</span>
                                            <input type="date" class="form-control">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>状态</label>
                                        <select class="form-control">
                                            <option value="">请选择</option>
                                            <option value="pending">处理中</option>
                                            <option value="success">成功</option>
                                            <option value="failed">失败</option>
                                        </select>
                                    </div>
                                    <div class="form-actions">
                                        <button class="btn btn-primary">查询</button>
                                        <button class="btn btn-secondary">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 表格 -->
                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>结算订单号</th>
                                            <th>结算周期</th>
                                            <th>结算账户</th>
                                            <th>佣金金额</th>
                                            <th>结算手续费金额</th>
                                            <th>实际结算金额</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="7" class="empty-state">
                                                <div class="empty-icon">📄</div>
                                                <div class="empty-text">暂无数据</div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <div class="pagination">
                                <button class="page-btn" disabled>‹</button>
                                <button class="page-btn active">1</button>
                                <button class="page-btn" disabled>›</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面切换功能
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => page.classList.remove('active'));
            
            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }
            
            // 更新导航状态
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => link.classList.remove('active'));
            event.target.closest('.nav-link').classList.add('active');
            
            // 更新页面标题
            const pageTitles = {
                'overview': '首页概览',
                'dashboard': '数据看板',
                'bill-records': '账单记录',
                'bill-details': '账单明细',
                'settlement-records': '结算记录'
            };
            
            const pageTitle = document.getElementById('pageTitle');
            if (pageTitle && pageTitles[pageId]) {
                pageTitle.textContent = pageTitles[pageId];
            }
        }

        // 子菜单切换
        function toggleSubmenu(element) {
            const submenu = element.nextElementSibling;
            const arrow = element.querySelector('.nav-arrow');
            
            if (submenu.classList.contains('open')) {
                submenu.classList.remove('open');
                arrow.style.transform = 'rotate(0deg)';
            } else {
                submenu.classList.add('open');
                arrow.style.transform = 'rotate(180deg)';
            }
        }

        // 复制链接功能
        function copyToClipboard() {
            const url = "https://mp.ipaylinks.com/#/register?inviteCode=A***";
            navigator.clipboard.writeText(url).then(() => {
                alert('链接已复制到剪贴板');
            });
        }

        // Tab切换功能
        function switchTab(tabType) {
            const tabs = document.querySelectorAll('.tab-btn');
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
        }
    </script>

    <style>
        .tab-btn {
            background: none;
            border: none;
            padding: 10px 0;
            font-size: 14px;
            color: #7f8c8d;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            color: #3498db;
            border-bottom-color: #3498db;
        }

        .tab-btn:hover {
            color: #3498db;
        }

        /* 表单样式 */
        .search-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            align-items: end;
            margin-bottom: 15px;
        }

        .form-row:last-child {
            margin-bottom: 0;
        }

        .form-group {
            flex: 1;
            min-width: 200px;
        }

        .form-group label {
            display: block;
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .date-range {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .date-range span {
            font-size: 14px;
            color: #666;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .data-table th {
            background: #f8f9fa;
            padding: 12px 15px;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #e0e0e0;
            font-size: 14px;
        }

        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            color: #333;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        .empty-state {
            text-align: center;
            padding: 50px 20px;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .empty-text {
            color: #999;
            font-size: 16px;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            gap: 5px;
            margin-top: 20px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .page-btn:hover:not(:disabled) {
            background: #f0f0f0;
        }

        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</body>
</html>
