# EX分佣风险控制方案（MVP版）

## 1. 风控目标

- 唯一底线：**EX不能亏钱**。
- 只要确保EX给租户的分佣（手续费收入 - EX给租户底价）≥ 0，EX无资金损失风险。

---

## 2. 风控规则与处理流程

### 2.1 分佣负数底线校验（唯一强制风控）

- 每笔交易结算前，系统自动校验：
  - EX分佣 = 商户实际手续费收入 - EX给租户底价 × 交易金额
  - 若EX分佣 < 0，系统阻止结算，提示租户需补差额。

### 2.2 租户-代理让利与特殊补差

- 租户可自由配置给代理的底价，即使低于EX底价，只要**商户收费不低于EX底价**，EX无风险。
- 若租户为拿下客户，让利导致商户收费低于EX底价，系统自动识别，结算时要求租户补差额，补齐后EX再放款。
- 该类补差单可配置为需运营/财务人工审核确认。

### 2.3 其他风控措施

- 不对租户给代理的底价做系统强控，仅做数据留痕。
- 不做复杂浮动、批量、金额等风控参数。
- 不做自动拦截/holding，除非EX分佣为负。
- 所有分佣、让利、补差数据均详细记录，便于追溯。

---

## 3. 风控业务流程图

```mermaid
graph TD
    A[交易结算] --> B{商户收费 大于 EX底价?}
    B -- 是 --> C[正常结算]
    B -- 否 --> D[租户补差额给EX]
    D --> E[补齐后结算]
```

---

## 4. 典型场景说明

### 场景1：正常分佣

- 商户收费≥EX底价，EX分佣为正，系统自动结算。

### 场景2：租户让利导致商户收费低于EX底价

- 系统自动识别，生成补差单，租户补齐后才结算。

### 场景3：租户-代理让利但商户收费未破底价

- 系统正常结算，EX无风险。

---

## 5. 后续风控增强建议

- 若业务规模扩大、风险点增多，可逐步引入：
  - 价格浮动容忍阈值
  - 批量/金额风险预警
  - 自动拦截/holding机制
  - 更丰富的风控审核流

---

## 6. 负分佣累计监控与预警机制（补充）

### 6.1 监控目标

- 实时监控每个租户的交易分佣，累计负分佣金额。
- 若某租户累计负分佣金额达到或超过100 USD，系统自动预警，及时通知清算团队介入。

### 6.2 预警处理流程

1. 系统每日统计每个租户的负分佣累计金额（即所有手续费收入≤EX底价的交易，分佣为负的部分累加）。
2. 当累计负分佣≥100 USD时，自动触发预警通知（如邮件、短信、系统消息）给清算/风控/运营负责人。
3. 清算团队收到预警后，可立即联系租户，要求上调交易处理费、补差或暂停相关业务，防止风险扩大。
4. 预警记录和处理结果需留痕，便于后续复盘和风控策略优化。

### 6.3 流程图补充

```mermaid
graph TD
    A[每日监控租户负分佣] --> B{累计负分佣大于100USD}
    B -- 否 --> C[继续监控]
    B -- 是 --> D[系统预警通知清算]
    D --> E[清算团队干预:上调商户收费&上调ex交易处理费&联系租户补差]
```

### 6.4 文档片段建议

> **负分佣累计监控与预警机制：**
>
> - 系统每日统计每个租户的负分佣累计金额（手续费收入≤EX底价的所有交易）。
> - 若累计负分佣≥100 USD，自动预警清算团队，提示及时干预。
> - 清算团队可上调租户交易处理费、要求补差或暂停业务，确保EX风险可控。
> - 所有预警和处理过程均留痕备查。

> 本方案为MVP阶段极简风控设计，后续可根据实际业务和风险情况逐步增强。
