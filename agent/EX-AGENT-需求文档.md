# EX-AGENT 需求文档

## 1. 项目概述

### 1.1 项目背景

EX-AGENT是租户代理EX产品的分销渠道系统，本期开发普通版本。系统层级为：租户 → 代理商(AGENT) → 商户，代理商作为租户的分销渠道，在租户成本价基础上获得底价配置，底价之上为代理商收入。

### 1.2 版本规划

- **普通版本**：本期开发重点，提供基础代理商管理和费率配置功能
- **高级版本**：后续版本，提供更多高级功能

### 1.3 系统架构

```
租户 (Tenant)
    ↓ 成本价基础上配置底价
代理商 (AGENT)
    ↓ 底价之上获得收入
商户 (Merchant)
```

## 2. 功能模块

### 2.1 代理商签约管理

#### 2.1.1 线下签约流程

1. 代理商与PSP线下签约
2. PSP在线登记代理商信息
3. 配置代理商底价策略

#### 2.1.2 PSP-OP 代理商管理功能

- **新增代理商**：参考agent-op-sidebar交互设计
- **代理商信息管理**：基本信息、合同信息、结算账户
- **代理商状态管理**：草稿、待审核、未激活、已激活、已过期
- **代理商激活功能**：发送激活邮件，管理激活状态

#### 2.1.3 代理商激活流程

1. **PSP-OP操作**：

   - 确认代理商配置完整（基本信息、费率配置等）
   - 点击激活按钮，系统提示确认
   - 系统向代理商邮箱发送激活链接
2. **代理商激活操作**：

   - 收到激活邮件，点击激活链接
   - 进入激活页面，输入手机号
   - 接收并输入手机验证码
   - 设置登录密码
   - 激活成功，获得登录权限
3. **激活状态管理**：

   - 未激活：代理商信息已录入，但未完成激活流程
   - 已激活：代理商已完成激活，可正常登录使用系统

### 2.2 费率配置管理

#### 2.2.1 标准费率配置

- **标准产品费率**：按代理商等级配置标准费率
- **标准汇率配置**：按代理商等级配置标准汇率

#### 2.2.2 特殊费率配置

- **特殊代理商产品费率**：针对特定代理商的特殊费率
- **特殊代理商汇率**：针对特定代理商的特殊汇率
- **特殊商户费率**：针对特定商户的特殊底价

### 2.3 费率降级规则

#### 2.3.1 商户计费逻辑

商户交易费用按以下优先级计算：

1. 特殊商户配置
2. 特定代理商配置
3. 产品标准价格

#### 2.3.2 代理价格降级规则

代理商底价按以下优先级获取：

1. 针对特定商户的特殊底价
2. 代理商的标准底价
3. 产品标准底价

#### 2.3.3 代理商汇率返点计算模型

**基础概念：**

- **租户成本汇率**：EX 给租户的汇率底价
- **商户汇率加点**：租户给商户配置的汇率加点百分比
  - **负值（-）**：收客户更多钱（加价）
  - **正值（+）**：给客户让利（优惠）
- **代理商返点比例**：代理商获得的独立返点比例，与汇率加点无关

**核心理念：**

代理商返点基于汇率加点收益计算，返点比例用"万分之几"表示。计算公式为：返点比例 = 代理商返点率 ÷ 汇率加点率

- **返点率 < 加点率**：租户保留部分汇率收益（如加点百1，返点万3，返点比例30%，租户保留70%）
- **返点率 = 加点率**：租户将汇率收益全部给代理商（如加点百1，返点百1，返点比例100%，租户保本）
- **返点率 > 加点率**：租户额外补贴代理商（如加点百1，返点万15，返点比例150%，租户额外补贴50%）

**计算公式：**

1. **商户实际汇率** = 租户成本汇率 × (1 + 汇率加点百分比)
2. **租户成本** = 交易金额 × 租户成本汇率
3. **商户实际支付** = 交易金额 × 商户实际汇率
4. **汇率加点收益** = 商户实际支付 - 租户成本
5. **返点比例** = 代理商返点率 ÷ 汇率加点率
6. **代理商汇率返点** = 汇率加点收益 × 返点比例

**特点：**

- 返点基于汇率加点收益的分成比例
- 租户可通过返点比例控制收益分配
- 支持超过100%的返点实现额外补贴激励

**计算示例：**

## **场景一：商户汇率加点不等于0**

**基础场景设置：**
- 汇率对：USD-CNH
- 租户成本汇率：7.5000
- 商户汇率加点：-1%（收客户更多钱）
- 交易金额：10000 USD

**基础计算：**
1. **商户实际汇率** = 7.5000 × (1 + (-1%)) = 7.5000 × 1.01 = 7.575
2. **租户成本** = 10000 USD × 7.5000 = 75000 CNH
3. **商户实际支付** = 10000 USD × 7.575 = 75750 CNH
4. **汇率加点收益** = 75750 - 75000 = 750 CNH

##### **示例1.1：租户有收益（返点万3）**

**配置：**
- 汇率加点率：百1（1%）
- 代理商返点率：万3（0.3%）
- 返点比例：万3 ÷ 百1 = 0.3% ÷ 1% = 30%

**计算：**
5. **返点比例** = 0.3% ÷ 1% = 30%
6. **代理商汇率返点** = 750 × 30% = 225 CNH

**结果：**
- 租户汇率收益：750 CNH
- 代理商返点：225 CNH
- 租户净收益：525 CNH

##### **示例1.2：租户全贴代理商（返点百1）**

**配置：**
- 汇率加点率：百1（1%）
- 代理商返点率：百1（1%）
- 返点比例：百1 ÷ 百1 = 1% ÷ 1% = 100%

**计算：**
5. **返点比例** = 1% ÷ 1% = 100%
6. **代理商汇率返点** = 750 × 100% = 750 CNH

**结果：**
- 租户汇率收益：750 CNH
- 代理商返点：750 CNH
- 租户净收益：0 CNH（保本）

##### **示例1.3：租户额外补贴代理商（返点万15）**

**配置：**
- 汇率加点率：百1（1%）
- 代理商返点率：万15（1.5%）
- 返点比例：万15 ÷ 百1 = 1.5% ÷ 1% = 150%

**计算：**
5. **返点比例** = 1.5% ÷ 1% = 150%
6. **代理商汇率返点** = 750 × 150% = 1125 CNH

**结果：**
- 租户汇率收益：750 CNH
- 代理商返点：1125 CNH
- 租户净亏损：375 CNH（额外补贴）

## **场景二：商户汇率加点等于0**

**基础场景设置：**
- 汇率对：USD-CNH
- 租户成本汇率：7.5000
- 商户汇率加点：0%（按成本价给商户）
- 交易金额：10000 USD

**基础计算：**
1. **商户实际汇率** = 7.5000 × (1 + 0%) = 7.5000
2. **租户成本** = 10000 USD × 7.5000 = 75000 CNH
3. **商户实际支付** = 10000 USD × 7.5000 = 75000 CNH
4. **汇率加点收益** = 75000 - 75000 = 0 CNH

##### **示例2.1：租户有收益（返点万3）**

**配置：**
- 汇率加点率：0%
- 代理商返点率：万3（0.3%）
- 返点比例：无法计算（除数为0）

**计算：**
5. **返点比例** = 无法计算（0% ÷ 0%）
6. **代理商汇率返点** = 0 × 任何比例 = 0 CNH

**结果：**
- 租户汇率收益：0 CNH
- 代理商返点：0 CNH
- 租户净收益：0 CNH

##### **示例2.2：租户全贴代理商（返点百1）**

**配置：**
- 汇率加点率：0%
- 代理商返点率：百1（1%）
- 返点比例：无法计算（除数为0）

**计算：**
5. **返点比例** = 无法计算（1% ÷ 0%）
6. **代理商汇率返点** = 0 × 任何比例 = 0 CNH

**结果：**
- 租户汇率收益：0 CNH
- 代理商返点：0 CNH
- 租户净收益：0 CNH

##### **示例2.3：租户额外补贴代理商（返点万15）**

**配置：**
- 汇率加点率：0%
- 代理商返点率：万15（1.5%）
- 返点比例：无法计算（除数为0）

**计算：**
5. **返点比例** = 无法计算（1.5% ÷ 0%）
6. **代理商汇率返点** = 0 × 任何比例 = 0 CNH

**结果：**
- 租户汇率收益：0 CNH
- 代理商返点：0 CNH
- 租户净收益：0 CNH

**注意：** 当汇率加点为0时，无论返点比例多高，代理商都无法获得汇率返点，因为没有汇率收益可以分成。

##### **策略对比（基于汇率加点百1的场景）：**

| 策略 | 返点率 | 返点比例 | 代理商返点 | 租户净损益 | 适用场景 |
|------|--------|----------|------------|------------|----------|
| 有收益策略 | 万3 | 30% | 225 CNH | +525 CNH | 成熟期精细化管理 |
| 全贴策略 | 百1 | 100% | 750 CNH | 0 CNH | 平衡发展期 |
| 额外补贴策略 | 万15 | 150% | 1125 CNH | -375 CNH | 市场拓展期 |

##### **汇率返点计算流程图：**

```mermaid
flowchart TD
    A[商户发起汇率交易] --> B[获取租户成本汇率]
    B --> C[获取商户汇率加点配置]
    C --> D[计算商户实际汇率]
    D --> E[计算商户实际支付]
    E --> F[计算汇率加点收益]
    F --> G[获取代理商返点比例]
    G --> H[计算代理商汇率返点]
    H --> I[记录交易信息]
    I --> J[累积到月度账单]
    J --> K[月度结算]
```

### 2.4 账单和结算管理

#### 2.4.1 账单生成功能

- **自动生成**：每月20号系统自动生成代理商分佣账单
- **账单内容**：包含交易明细、佣金计算、应付金额等
- **账单状态**：待处理、已核对、有争议、已结算等状态管理

#### 2.4.2 账单核对功能

- **在线核对**：租户和代理商可在线查看和核对账单
- **明细查询**：支持按时间、商户、产品等维度查询明细
- **争议处理**：支持账单争议标记和处理流程

#### 2.4.3 结算管理功能

- **结算记录**：记录每次结算的详细信息
- **状态管理**：结算申请、处理中、已完成等状态
- **线下结算**：支持线下转账后的状态确认
- **结算通知**：结算完成后的系统通知功能

### 2.5 汇率返点管理

#### 2.5.1 汇率返点配置

- **返点率设置**：为代理商配置汇率返点率（万3、万5、百1等）
- **返点比例计算**：系统自动计算返点比例（返点率 ÷ 汇率加点率）
- **汇率对管理**：支持不同汇率对的返点配置
- **生效时间控制**：支持返点配置的生效和失效时间
- **特殊配置**：支持针对特定代理商的特殊返点率
- **灵活策略**：支持盈利、保本、贴钱激励等不同策略

#### 2.5.2 汇率交易处理

- **实时计算**：交易时实时计算汇率返点
- **基于报价**：返点基于商户实际支付金额计算
- **交易记录**：完整记录汇率交易和返点信息
- **异常处理**：处理汇率波动和计算异常情况

#### 2.5.3 汇率返点结算

- **返点累积**：按周期累积代理商汇率返点
- **结算账单**：生成包含汇率返点的综合账单
- **分项明细**：区分交易佣金和汇率返点
- **对账功能**：支持汇率返点的对账和确认

## 3. 业务流程

### 3.1 代理商完整业务流程

```mermaid
flowchart TD
    A[代理商和租户线下签约] --> B[租户在OP后台登记代理商信息]
    B --> C[配置代理商底价]
    C --> D{是否需要特殊底价?}
    D -->|是| E[配置特殊底价]
    D -->|否| F[使用标准底价]
    E --> G[发送激活链接]
    F --> G
    G --> H[代理商激活账号]
    H --> I[代理商登录系统]
    I --> J[获取拓客链接]
    J --> K[发展客户]
    K --> L[客户产生交易]
    L --> M[每月20号租户生成代理账单]
    M --> N[租户和代理商核对账单]
    N --> O{账单是否有问题?}
    O -->|有问题| P[账单调整和重新核对]
    O -->|没问题| Q[线下结算]
    P --> N
    Q --> R[线上登记结算状态]
    R --> S[完成结算周期]
    S --> K
```

#### 3.1.1 业务流程详细说明

**阶段一：签约和配置**

1. **线下签约**：代理商与租户完成线下合同签署
2. **信息登记**：租户在OP后台录入代理商基本信息、合同信息、结算账户等
3. **底价配置**：根据代理商等级和合同条款配置费率底价
4. **特殊配置**：如需要，为特定代理商或特定商户配置特殊底价

**阶段二：激活和运营**
5. **发送激活**：租户确认配置完成后，系统发送激活邮件
6. **账户激活**：代理商通过邮件链接完成账户激活
7. **登录系统**：代理商使用激活的账户登录管理后台
8. **获取拓客链接**：代理商在后台获取专属的商户注册推广链接

**阶段三：客户发展**
9. **发展客户**：代理商使用拓客链接推广，发展下级商户
10. **客户交易**：商户通过代理商渠道产生支付交易
11. **佣金累积**：根据交易量和费率差异累积代理商佣金

**阶段四：账单和结算**
12. **生成账单**：每月20号系统自动生成代理商分佣账单
13. **账单核对**：租户和代理商核对账单明细，确认佣金金额
14. **问题处理**：如有账单争议，进行调整和重新核对
15. **线下结算**：账单确认无误后，租户向代理商进行线下转账结算
16. **状态登记**：租户在系统中登记结算完成状态
17. **周期循环**：进入下一个结算周期，继续客户发展和交易

### 3.2 代理商激活

```mermaid
flowchart TD
    A[代理商申请] --> B[线下签约]
    B --> C[PSP-OP录入代理商信息]
    C --> D[配置代理商等级]
    D --> E[配置标准费率/汇率]
    E --> F{是否需要特殊费率?}
    F -->|是| G[配置特殊费率]
    F -->|否| H[完成配置]
    G --> H
    H --> I[PSP-OP发送激活邮件]
    I --> J[代理商收到激活链接]
    J --> K[点击链接进入激活页面]
    K --> L[验证手机号]
    L --> M[设置登录密码]
    M --> N[激活成功]
    N --> O[代理商可正常登录]
```

### 3.3 代理商激活流程

```mermaid
flowchart TD
    A[PSP-OP点击激活] --> B[系统确认配置完整性]
    B --> C{配置是否完整?}
    C -->|否| D[提示完善配置]
    C -->|是| E[发送激活邮件]
    E --> F[代理商收到邮件]
    F --> G[点击激活链接]
    G --> H[进入激活页面]
    H --> I[输入手机号]
    I --> J[发送验证码]
    J --> K[输入验证码]
    K --> L{验证码正确?}
    L -->|否| M[重新输入]
    L -->|是| N[设置登录密码]
    M --> K
    N --> O[确认密码]
    O --> P[激活成功]
    P --> Q[跳转登录页面]
```

### 3.4 商户交易计费流程

```mermaid
flowchart TD
    A[商户发起交易] --> B[获取商户信息]
    B --> C{是否有特殊商户配置?}
    C -->|是| D[使用特殊商户费率]
    C -->|否| E{是否有特定代理商?}
    E -->|是| F[使用代理商费率]
    E -->|否| G[使用产品标准费率]
    D --> H[计算商户费用]
    F --> H
    G --> H
    H --> I[交易处理完成]
```

### 3.5 代理商底价获取流程

```mermaid
flowchart TD
    A[获取代理商底价] --> B[查询商户ID]
    B --> C{是否有商户特殊底价?}
    C -->|是| D[使用商户特殊底价]
    C -->|否| E{代理商是否有特殊底价?}
    E -->|是| F[使用代理商特殊底价]
    E -->|否| G[使用标准产品底价]
    D --> H[返回底价]
    F --> H
    G --> H
```

## 4. 数据模型

### 4.1 代理商信息表

```sql
agent_info:
- agent_id: 代理商ID
- agent_name: 代理商名称
- agent_level: 代理商等级 (A级/B级/C级)
- contract_start: 合同开始时间
- contract_end: 合同结束时间
- status: 状态 (draft/pending/inactive/active/expired)
- activation_status: 激活状态 (未激活/已激活)
- email: 代理商邮箱
- phone: 代理商手机号
- settlement_account: 结算账户信息
- activation_token: 激活令牌
- activation_time: 激活时间
```

### 4.2 标准费率配置表

```sql
standard_rates:
- product_type: 产品类型
- agent_level: 代理商等级
- rate_type: 费率类型 (product/exchange)
- rate_value: 费率值
- currency: 币种
```

### 4.3 特殊费率配置表

```sql
special_rates:
- agent_id: 代理商ID
- merchant_id: 商户ID (可为空)
- product_type: 产品类型
- rate_type: 费率类型
- rate_value: 费率值
- currency: 币种
```

### 4.4 代理商账单表

```sql
agent_bills:
- bill_id: 账单ID
- agent_id: 代理商ID
- bill_period: 账单周期 (YYYY-MM)
- total_amount: 总佣金金额
- bill_status: 账单状态 (pending/confirmed/disputed/settled)
- generate_time: 生成时间
- confirm_time: 确认时间
- settle_time: 结算时间
- remark: 备注
```

### 4.5 账单明细表

```sql
bill_details:
- detail_id: 明细ID
- bill_id: 账单ID
- merchant_id: 商户ID
- transaction_id: 交易ID
- commission_amount: 佣金金额
- commission_rate: 佣金费率
- transaction_amount: 交易金额
- transaction_time: 交易时间
```

### 4.6 结算记录表

```sql
settlement_records:
- settlement_id: 结算ID
- agent_id: 代理商ID
- bill_id: 账单ID
- settlement_amount: 结算金额
- settlement_method: 结算方式 (bank_transfer/offline)
- settlement_status: 结算状态 (pending/processing/completed/failed)
- settlement_time: 结算时间
- operator_id: 操作员ID
- remark: 备注
```

### 4.7 汇率返点配置表

```sql
exchange_rate_commission:
- config_id: 配置ID
- agent_id: 代理商ID
- currency_pair: 汇率对 (USD-CNH/EUR-USD等)
- commission_rate: 返点率 (0.003表示万3，0.01表示百1)
- effective_date: 生效日期
- expire_date: 失效日期
- status: 状态 (active/inactive)
- create_time: 创建时间
- update_time: 更新时间
```

### 4.8 汇率交易记录表

```sql
exchange_transactions:
- transaction_id: 交易ID
- agent_id: 代理商ID
- merchant_id: 商户ID
- currency_pair: 汇率对
- transaction_amount: 交易金额
- tenant_cost_rate: 租户成本汇率
- merchant_rate: 商户实际汇率
- rate_markup: 汇率加点百分比
- tenant_cost: 租户成本
- merchant_payment: 商户实际支付
- markup_revenue: 汇率加点收益
- agent_commission: 代理商汇率返点
- commission_rate: 返点率 (万分之几)
- commission_ratio: 返点比例 (计算得出)
- transaction_time: 交易时间
- settlement_status: 结算状态
```

## 5. 界面设计要求

### 5.1 PSP-OP 代理商管理界面

- 参考 agent-op-sidebar.html 的交互设计
- 包含代理商列表、新增、编辑、详情功能
- 支持代理商等级管理和状态变更

### 5.2 费率配置界面

- 标准费率配置：按等级展示和配置
- 特殊费率配置：支持代理商和商户维度配置
- 费率查询和历史记录功能

### 5.3 代理商运营界面

- 参考现有 agent-op-sidebar.html 设计
- 包含数据概览、账单管理、结算记录等功能

### 5.4 账单管理界面

- **租户端账单管理**：生成、核对、确认代理商账单
- **代理商端账单查询**：查看账单详情、明细、状态
- **账单核对功能**：在线核对、争议标记、确认功能
- **账单导出功能**：支持Excel、PDF格式导出

### 5.5 汇率返点配置界面

- **返点率配置**：支持万分之几的返点率设置（万1、万3、万5、百1等）
- **返点比例显示**：实时显示计算出的返点比例（返点率 ÷ 汇率加点率）
- **汇率对管理**：支持多种汇率对的返点配置
- **策略预览**：直观展示不同返点率的盈亏情况
- **配置历史记录**：记录返点配置的变更历史

### 5.6 结算管理界面

- **结算申请界面**：代理商发起结算申请
- **结算审核界面**：租户审核和处理结算申请
- **结算记录界面**：查看历史结算记录和状态
- **汇率返点明细**：在结算账单中区分显示汇率返点
- **结算状态管理**：线上登记结算完成状态

## 6. 技术实现要点

### 6.1 费率计算引擎

- 实现费率降级规则算法
- 支持实时费率查询和缓存
- 确保计算准确性和性能

### 6.2 汇率返点计算引擎

- **双模型支持**：同时支持两种返点计算模型
- **模型识别**：根据配置自动选择计算模型
- **实时计算**：交易时实时计算返点金额
- **精度控制**：确保金额计算的精度和准确性
- **性能优化**：支持高并发的返点计算
- **异常处理**：处理汇率异常和计算错误

### 6.3 权限控制

- PSP-OP：代理商管理和费率配置权限
- 代理商：自身数据查看和商户管理权限
- 商户：交易数据查看权限

### 6.4 数据一致性

- 费率变更的生效时间控制
- 历史费率数据保留
- 交易数据与费率配置的关联
- 汇率返点计算的数据一致性
- 返点模型切换的数据处理

## 7. 验收标准

### 7.1 功能验收

- [ ] 代理商签约流程完整可用
- [ ] 费率配置功能正常
- [ ] 费率降级规则正确执行
- [ ] 代理商激活流程正常
- [ ] 拓客链接生成和使用正常
- [ ] 汇率返点模型配置功能正常
- [ ] 两种返点模型计算准确
- [ ] 返点模型切换功能正常
- [ ] 汇率交易返点实时计算正确
- [ ] 账单自动生成功能正常
- [ ] 账单核对和确认功能正常
- [ ] 汇率返点账单明细准确
- [ ] 结算申请和处理流程正常
- [ ] 结算状态管理功能正常
- [ ] 界面交互符合设计要求

### 7.2 性能验收

- [ ] 费率查询响应时间 < 100ms
- [ ] 支持并发代理商操作
- [ ] 数据一致性保证

### 7.3 安全验收

- [ ] 权限控制正确
- [ ] 敏感数据加密
- [ ] 操作日志完整

## 8. 后续规划

### 8.1 高级版本功能

- 代理商分级管理
- 智能费率推荐
- 高级数据分析
- API接口开放

### 8.2 运营支持

- 代理商培训体系
- 运营数据分析
- 自动化结算流程

## 9. 汇率返点核心原则

### 9.1 设计理念

**灵活性优先**：汇率加点和代理商返点比例完全解耦，租户可根据业务策略灵活设置返点比例，实现不同的激励效果。

**激励有效性**：支持返点比例大于汇率加点的贴钱激励策略，允许租户在市场拓展期投入更多成本激励代理商。

**成本可控性**：返点比例可精确控制，租户可根据实际情况选择盈利、保本或贴钱激励策略。

### 9.2 业务价值

**市场拓展期**：设置高返点比例（如1.1%）可以给代理商提供强力激励，即使租户亏损也能快速建立代理商网络。

**成熟运营期**：降低返点比例（如0.03%）可以实现精细化管理，确保租户盈利的可持续性。

**竞争优势**：独立的返点比例设计为租户提供了灵活的竞争工具，可以在不同市场环境下采用最优策略。

### 9.3 实施建议

1. **初期建议**：新租户建议设置较高返点比例快速建立代理商网络
2. **过渡策略**：当代理商网络稳定后，逐步降低返点比例
3. **差异化策略**：可以对不同等级代理商设置不同返点比例
4. **定期评估**：建议每季度评估返点效果，适时调整返点比例
