# IPL 与 EX 客户流程对比

本文档详细说明了IPL直客户和EX租户商户在入网流程和交易流程上的区别，包括客户入网、收款交易和付款交易三个主要环节。

## 目录
- [系统架构概述](#系统架构概述)
- [客户入网流程](#客户入网流程)
- [收款交易流程](#收款交易流程)
- [付款交易流程](#付款交易流程)

## 系统架构概述

IPL(iPayLinks)和EX系统在架构上存在以下关系：

- **IPL系统**：直接服务于IPL直客户，提供完整的支付服务
- **EX系统**：作为租户平台，服务于EX商户，但实际支付能力由IPL提供
- **产品映射**：EX产品会映射到IPL产品，实现能力复用

```mermaid
graph TD
    A[IPL系统] --- B[IPL直客户]
    C[EX系统] --- D[EX商户]
    C --> A
    E[入网平台] --> A
    E --> C
```

## 客户入网流程

客户入网流程根据客户类型（IPL直客户或EX商户）有不同的处理路径。

```mermaid
sequenceDiagram
    participant 客户
    participant 入网平台
    participant EX系统
    participant IPL系统

    客户->>入网平台: 提交入网申请
    入网平台->>入网平台: 判断客户类型
    
    alt 是EX商户
        入网平台->>EX系统: KYC审核
        EX系统-->>入网平台: 审核结果
        入网平台->>EX系统: 开通EX产品
        EX系统->>IPL系统: 推送客户信息
        EX系统->>IPL系统: 推送EX产品映射IPL产品信息
        IPL系统-->>EX系统: 确认接收
        EX系统-->>入网平台: 开通成功
    else 是IPL直客户
        入网平台->>IPL系统: KYC审核
        IPL系统-->>入网平台: 审核结果
        入网平台->>IPL系统: 选择业务线
        入网平台->>IPL系统: 开通IPL产品
        IPL系统-->>入网平台: 开通成功
    end
    
    入网平台-->>客户: 入网完成通知
```

### 入网流程关键点

1. **客户类型判断**：
   - 通过入网平台判断客户类型
   - EX商户：进行KYC审核后开通EX产品
   - IPL直客户：选择业务线后开通IPL产品

2. **EX商户特殊处理**：
   - EX系统将客户信息推送至IPL系统
   - EX系统将EX产品映射到IPL产品并推送至IPL系统
   - 确保IPL系统能够识别并处理来自EX系统的交易请求

## 收款交易流程

收款交易流程展示了资金从汇款人到客户账户的流转过程，以及不同客户类型的计费差异。

```mermaid
sequenceDiagram
    participant 汇款人
    participant IPL系统
    participant EX系统
    participant 客户账户
    
    汇款人->>IPL系统: 发起付款
    IPL系统->>IPL系统: 合规审核
    
    alt 审核通过
        IPL系统->>IPL系统: 判断客户类型
        
        alt 是IPL直客户
            IPL系统->>IPL系统: 调用IPL产品计费/汇率配置
            IPL系统->>客户账户: 资金入账(IPL费率)
        else 是EX商户
            IPL系统->>EX系统: 请求计费信息
            EX系统->>EX系统: 调用EX产品计费/汇率配置
            EX系统-->>IPL系统: 返回计费结果
            IPL系统->>客户账户: 资金入账(EX费率)
        end
        
        IPL系统-->>汇款人: 交易成功通知
    else 审核不通过
        IPL系统-->>汇款人: 交易拒绝通知
    end
```

### 收款流程关键点

1. **合规审核**：
   - 所有交易都经过IPL系统的合规审核
   - 审核不通过的交易会被拒绝

2. **计费差异**：
   - IPL直客户：直接调用IPL产品计费/汇率配置
   - EX商户：通过EX系统调用EX产品计费/汇率配置

## 付款交易流程

付款交易流程展示了资金从客户账户到收款人的流转过程，以及不同客户类型的计费差异。

```mermaid
sequenceDiagram
    participant 客户
    participant EX系统
    participant IPL系统
    participant 收款人
    
    客户->>IPL系统: 发起付款请求
    
    alt 是IPL直客户
        IPL系统->>IPL系统: 调用IPL产品计费/汇率配置
        IPL系统->>IPL系统: 合规审核
        
        alt 审核通过
            IPL系统->>收款人: 执行付款
            IPL系统-->>客户: 交易成功通知
        else 审核不通过
            IPL系统-->>客户: 交易拒绝通知
        end
        
    else 是EX商户
        IPL系统->>EX系统: 转发付款请求
        EX系统->>EX系统: 调用EX产品计费/汇率配置
        EX系统->>IPL系统: 使用IPL付款渠道
        IPL系统->>IPL系统: 转换为IPL产品
        IPL系统->>IPL系统: 合规审核
        
        alt 审核通过
            IPL系统->>收款人: 执行付款
            IPL系统-->>EX系统: 交易成功通知
            EX系统-->>客户: 交易成功通知
        else 审核不通过
            IPL系统-->>EX系统: 交易拒绝通知
            EX系统-->>客户: 交易拒绝通知
        end
    end
```

### 付款流程关键点

1. **客户类型判断**：
   - 系统判断发起请求的是IPL直客户还是EX商户

2. **计费差异**：
   - IPL直客户：直接调用IPL产品计费/汇率配置
   - EX商户：先调用EX产品计费/汇率配置，再转换为IPL产品

3. **产品转换**：
   - EX商户的交易需要将EX产品转换为IPL产品
   - 确保IPL系统能够正确处理并执行付款

## 总结

IPL直客户和EX商户在入网和交易流程上存在明显差异：

1. **入网流程**：
   - IPL直客户直接在IPL系统开通产品
   - EX商户在EX系统开通产品，并将信息同步至IPL系统

2. **计费机制**：
   - IPL直客户使用IPL产品计费/汇率配置
   - EX商户使用EX产品计费/汇率配置

3. **系统关系**：
   - EX系统依赖IPL系统提供的支付能力
   - EX产品需要映射到IPL产品才能实现实际支付功能

这种设计使EX系统能够为其商户提供定制化的产品和费率，同时复用IPL系统的支付能力，实现了业务和技术的有效分离。
