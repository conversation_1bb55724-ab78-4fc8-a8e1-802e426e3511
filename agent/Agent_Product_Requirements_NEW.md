# 代理产品需求文档（新版）

## 1. 项目背景

EUREWAX SaaS平台需建立完善的代理商管理与风控系统，覆盖代理商签约、政策管理、分佣返点、结算、风控全流程，确保业务合规与高效。

---

## 2. 功能模块

### 2.1 代理商签约与管理

- 代理商信息录入、编辑、草稿保存、自动生成编号
- 结算信息、联系人、等级、类型等字段完整
- 激活/停用、状态流转（未激活→已激活→停用/过期）
- 支持两步向导式流程

### 2.2 政策管理与分配（详细）

#### 2.2.1 政策包生命周期流程

- 支持新增、编辑、删除、启用、停用、历史版本管理
- 所有操作均有权限校验与日志记录
- 新增/修改/停用/删除需要进工单
  - 工单复用
  - 审核通过默认是“可用状态“"
  - 业务逻辑：删除/停用没有任何人用的政策包，自动审核通过

```mermaid
graph TD
    A[新增/编辑/删除/停用/删除政策包] --> C[提交审核]
    C --> D{审核通过?}
    D -- 否 --> E[驳回修改]
    D -- 是 --> F[启用政策包]
    F --> G[停用政策包]
    G --> H[归档-历史版本]
    F --> I[编辑-政策包新版本]
    I --> C
    F --> J[删除,未分配未启用可删]
```

#### 2.2.2 政策包状态流转

- 状态包括：可用/不可用

```mermaid
stateDiagram-v2
    [*] --> 可用: 工单审核通过
    可用 --> 不可用: 工单审核通过
  
```

#### 2.2.3 政策分配与变更流程

- 支持将政策包分配给代理商或下属商户
- 支持分配、变更（更换政策）、撤销分配，所有历史可追溯

```mermaid
graph TD
    A[可用政策包] --> B[分配给代理商/商户]
    B --> C[分配记录]
    C --> D{是否变更?}
    D -- 否 --> E[持续生效]
    D -- 是 --> F[更换新政策包]
    F --> C

```

#### 2.2.4 分配关系结构图

- 支持一个政策包分配给多个代理商/商户，也支持一个代理商/商户拥有多次分配历史（仅一个当前生效）

```mermaid
graph LR
    PolicyA[政策包A] --> Agent1[代理商1]
    PolicyA --> Agent2[代理商2]
    PolicyB[政策包B] --> Agent3[代理商3]
    PolicyB --> Agent1
    Agent1 -->|历史| PolicyA
    Agent1 -->|当前| PolicyB
```

#### 2.2.5 结算周期内政策变更处理

- 结算周期内如有政策变更，系统自动按生效时间分段结算，结算明细标注对应政策编号和区间

### 2.3 分佣与返点管理

- 底价分佣、固定比例分佣两种模式
- 底价汇率、固定返点两种返点模式
- 明细自动生成、导出、对账
- 支持弹层/独立页面配置，现代化UI，表单验证

### 2.4 结算管理

- 账单核对、结算单生成、线下出款、状态管理
- 结算明细分段展示政策编号与适用时间
- 历史结算单可查、可导出

### 2.5 风控与审核

- 价格层级：EX→租户→代理→客户，严禁击穿EX成本
- 让利可控，突破EX成本须特殊审核
- 风控参数可配，超阈值自动预警/拦截/holding
- 所有违规配置和交易有日志与审批流

---

## 3. 业务流程图示例

### 3.1 签约与激活流程

```mermaid
graph TD
    A[录入信息] --> B[配置政策]
    B --> C[财务审核]
    C --> D[激活]
    D --> E[代理商业务开展]
```

### 3.2 风控处理流程

```mermaid
flowchart TD
    A[配置政策] --> B{是否突破EX成本?}
    B -- 否 --> C[直接生效]
    B -- 是 --> D[进入风控审核]
    D --> E{是否审批通过?}
    E -- 否 --> F[拒绝配置]
    E -- 是 --> G[生效并标记风险]
```

---

## 4. 用户用例与验收标准

- 新增代理、配置政策、激活后可开展业务
- 分佣/返点配置自动校验风控，违规需审核
- 结算周期内政策变更，结算单分段展示
- 财务核对、导出、历史查询
- 风控规则可配置，预警及时，人工干预可追溯
- 权限和日志完整，交互流畅

---

如需补充字段明细、页面原型、接口说明等，可随时扩展本文档。
