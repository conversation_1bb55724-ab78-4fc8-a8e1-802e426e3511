# 代理产品需求文档

## 1. 背景

EUREWAX SaaS平台需要建立完整的代理商管理系统，支持代理商从签约、政策配置、分佣计算到结算的全生命周期管理。系统需要支持多种分佣模式和汇率返点机制，确保代理商业务的规范化运营和精确的财务结算。

## 2. 功能需求

### 2.1 代理商签约管理

#### 2.1.1 签约流程需求

**流程描述**：
```mermaid
graph TD
    A[租户Admin录入代理商基本信息] --> B[配置代理政策]
    B --> C[代理商激活]
    C --> D[代理商拓展客户]
    D --> E[流程结束]
```

**功能点**：
- 租户管理员可录入代理商基本信息（名称、联系方式、合同信息等）
- 支持为代理商配置专属的分佣政策和汇率返点政策
- 提供代理商激活功能，控制代理商业务开通
- 代理商激活后可开始拓展客户业务

#### 2.1.2 激活状态管理

**状态图**：
```mermaid
stateDiagram-v2
    [*] --> 未激活
    未激活 --> 已激活: 管理员激活
    已激活 --> 未激活: 管理员停用
```

**功能点**：
- 代理商默认状态为"未激活"
- 管理员可手动激活代理商
- 支持激活状态的切换控制

#### 2.1.3 签约状态管理

**状态图**：
```mermaid
stateDiagram-v2
    [*] --> 未生效
    未生效 --> 生效中: 到达签约开始日期
    生效中 --> 签约过期: 到达签约结束日期
```

**功能点**：
- 代理商默认签约状态为"未生效"
- 系统自动根据签约开始日期将状态更新为"生效中"
- 系统自动根据签约结束日期将状态更新为"签约过期"
- 支持签约期限的自动化管理

### 2.2 代理商政策配置

#### 2.2.1 交易分佣-产品底价配置

**配置流程**：
```mermaid
graph TD
    A[代理商财务配置底价] --> B[提交工单审核]
    B --> C{审核结果}
    C -->|通过| D[生成有效配置]
    C -->|拒绝| E[配置失效]
```

**功能点**：
- 代理商财务人员可配置各产品的底价
- 配置提交后自动生成审核工单
- 支持审核通过后配置生效
- 审核拒绝后配置不生效

#### 2.2.2 交易分佣-固定比例配置

**配置流程**：与产品底价配置流程相同

**功能点**：
- 支持固定比例分佣配置
- 遵循相同的审核流程
- 配置生效后应用于交易分佣计算

#### 2.2.3 汇率返点-固定比例返点配置

**配置流程**：与产品底价配置流程相同

**功能点**：
- 支持汇率返点比例配置
- 遵循相同的审核流程
- 配置生效后应用于汇率返点计算

#### 2.2.4 工单审核状态管理

**状态图**：
```mermaid
stateDiagram-v2
    [*] --> 待审核
    待审核 --> 审核通过: 审核员通过
    待审核 --> 审核拒绝: 审核员拒绝
```

**功能点**：
- 配置提交后工单状态为"待审核"
- 审核员可选择"审核通过"或"审核拒绝"
- 审核结果影响配置的生效状态

### 2.3 分佣管理

#### 2.3.1 可分佣收费项配置

**功能点**：
- 系统预配置可分佣的收费项目
- 支持收费项目的启用/禁用控制
- 无需提供配置页面，系统后台配置即可

#### 2.3.2 分佣计算流程

**计算流程**：
```mermaid
graph TD
    A[每日凌晨同步交易数据] --> B[根据代理商政策计算分佣]
    B --> C[按结算币种计算佣金]
    C --> D[使用实时汇率转换]
    D --> E[生成分佣记录]
```

**功能点**：
- 系统每日自动同步交易数据
- 根据代理商配置的政策自动计算分佣
- 佣金币种等于结算币种
- 使用分佣计算时点的实时汇率进行币种转换
- 生成详细的分佣计算记录

#### 2.3.3 账单生成-核对流程

**账单状态图**：
```mermaid
stateDiagram-v2
    [*] --> 待财务核对
    待财务核对 --> 待代理商确认: 财务核对完成
    待代理商确认 --> 核对完成: 代理商同意
    待代理商确认 --> 代理商拒绝: 代理商拒绝
```

**功能点**：
- 系统自动生成分佣账单，状态为"待财务核对"
- 财务人员核对后状态更新为"待代理商确认"
- 代理商可选择"同意"或"拒绝"账单
- 代理商同意后状态更新为"核对完成"
- 代理商拒绝后状态更新为"代理商拒绝"

### 2.4 结算管理

#### 2.4.1 结算流程

**结算流程**：
```mermaid
graph TD
    A[账单核对完成] --> B[账单结算状态更新为待结算]
    B --> C[财务勾选待结算账单]
    C --> D[生成结算单]
    D --> E[线下出款]
    E --> F[更新结算完成状态]
```

**功能点**：
- 账单核对完成后，结算状态自动更新为"待结算"
- 财务人员可勾选多个待结算账单生成结算单
- 支持线下出款操作
- 出款完成后可更新结算单状态为"结算完成"

#### 2.4.2 结算单状态管理

**状态图**：
```mermaid
stateDiagram-v2
    [*] --> 待结算
    待结算 --> 结算完成: 财务确认出款完成
```

**功能点**：
- 结算单生成后状态为"待结算"
- 财务确认出款完成后状态更新为"结算完成"
- 支持结算状态的查询和管理

## 3. 用户用例 (Use Cases)

### 3.1 UC001: 代理商签约

**参与者**：租户管理员、代理商
**前置条件**：租户管理员已登录系统
**主流程**：
1. 租户管理员录入代理商基本信息
2. 配置代理商的分佣政策和汇率返点政策
3. 激活代理商账户
4. 代理商开始拓展客户业务

**后置条件**：代理商账户激活，可正常开展业务

### 3.2 UC002: 政策配置审核

**参与者**：代理商财务、审核员
**前置条件**：代理商已签约并激活
**主流程**：
1. 代理商财务配置产品底价/固定比例/汇率返点
2. 系统生成审核工单
3. 审核员审核配置
4. 审核通过后配置生效

**后置条件**：政策配置生效，用于分佣计算

### 3.3 UC003: 分佣计算与账单生成

**参与者**：系统、财务人员、代理商
**前置条件**：存在有效的代理商政策配置
**主流程**：
1. 系统每日同步交易数据
2. 自动计算分佣金额
3. 生成分佣账单
4. 财务核对账单
5. 代理商确认账单

**后置条件**：账单核对完成，进入待结算状态

### 3.4 UC004: 结算处理

**参与者**：财务人员
**前置条件**：存在核对完成的账单
**主流程**：
1. 财务勾选待结算账单
2. 生成结算单
3. 执行线下出款
4. 更新结算完成状态

**后置条件**：结算完成，代理商收到佣金

## 4. 验收标准

### 4.1 代理商签约管理
- [ ] 支持代理商基本信息的完整录入
- [ ] 支持代理商激活状态的正确切换
- [ ] 支持签约状态的自动化管理
- [ ] 签约开始/结束日期的自动状态更新

### 4.2 政策配置管理
- [ ] 支持产品底价配置和审核流程
- [ ] 支持固定比例分佣配置和审核流程
- [ ] 支持汇率返点配置和审核流程
- [ ] 工单审核状态的正确流转

### 4.3 分佣计算
- [ ] 每日自动同步交易数据
- [ ] 根据政策配置正确计算分佣
- [ ] 使用实时汇率进行币种转换
- [ ] 生成准确的分佣记录

### 4.4 账单管理
- [ ] 自动生成分佣账单
- [ ] 支持财务核对功能
- [ ] 支持代理商确认/拒绝功能
- [ ] 账单状态的正确流转

### 4.5 结算管理
- [ ] 支持批量选择待结算账单
- [ ] 支持结算单生成功能
- [ ] 支持结算状态更新
- [ ] 结算流程的完整性

### 4.6 系统性能
- [ ] 分佣计算的准确性和及时性
- [ ] 状态流转的实时性
- [ ] 数据同步的可靠性
- [ ] 用户操作的响应性能

### 4.7 数据安全
- [ ] 分佣数据的准确性
- [ ] 账单数据的完整性
- [ ] 结算记录的可追溯性
- [ ] 用户权限的正确控制
